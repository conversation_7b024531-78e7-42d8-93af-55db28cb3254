{"name": "data-pro-app", "private": true, "version": "0.0.0", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --dom --ui -- coverage.enable=true"}, "type": "module", "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.0"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.7.2", "@mahdikhashan/vue3-click-outside": "^0.1.2", "@vueuse/head": "^2.0.0", "apexcharts": "^4.7.0", "axios": "^1.10.0", "pagedjs": "^0.4.3", "pinia": "^3.0.3", "quasar": "^2.18.1", "rollup": "^4.44.0", "timeago.js": "^4.0.2", "vis-network": "^9.1.12", "vite": "^6.3.5", "vue": "^3.5.17", "vue-i18n": "^11.1.7", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0", "webfontloader": "^1.6.28"}, "devDependencies": {"@quasar/extras": "^1.17.0", "@quasar/vite-plugin": "^1.9.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.6", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-semistandard": "^17.0.0", "eslint-plugin-vue": "^9.32.0", "happy-dom": "^17.4.7", "rollup-plugin-visualizer": "^6.0.3", "vitest": "^3.2.4", "vue-cli-plugin-quasar": "^5.1.2", "vue-eslint-parser": "^9.4.3"}}