import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { quasar, transformAssetUrls } from '@quasar/vite-plugin';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          quasar: ['quasar'],
          utils: ['lodash', 'axios']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },

  server: {
    host: '0.0.0.0',
    port: process.env.APP_PORT || 3000,
    fs: { strict: false }
  },

  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '/data': fileURLToPath(new URL('./data', import.meta.url))
    }
  },

  define: {
    APP_VERSION: JSON.stringify(process.env.VITE_APP_VERSION)
  },

  plugins: [
    vue({
      template: { transformAssetUrls }
    }),

    quasar({
      extras: ['open-sans-font']
    }),

    visualizer({
      filename: 'dist/stats.html',
      open: true
    })
  ],

  test: {
    globalSetup: './test/config/vitest.global-setup.js',
    globals: true,
    include: ['./test/**/*.spec.js'],
    environment: 'happy-dom',
    timezone: 'UTC',
    coverage: {
      include: ['src/utils/**/*.js'],
      enabled: true,
      reporters: ['html']
    }
  }
});
