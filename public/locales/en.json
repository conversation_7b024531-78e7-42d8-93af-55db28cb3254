{"signIn": "Sign in", "interface": "Interface", "settings": "Settings", "applications": "APPLICATIONS", "solutions": "SOLUTIONS", "support": "Support", "logout": "Logout", "email": "Email", "password": "Password", "monitoring": "Monitoring", "alerts": "<PERSON><PERSON><PERSON>", "lists": "Lists", "date": "Date", "lifecycle": "Life cycle", "companyVAT": "Company VAT", "name": "Name", "assignee": "Assignee", "documents": "Documents", "actions": "Actions", "status": "Status", "statuses": {"ACTIVE": "Active", "TO_VALIDATE": "To validate", "VALIDATED": "Validated"}, "event": "Event", "list": "List", "cards": "Cards", "juridicalForm": "Juridical Form", "address": "Address", "establishments": "Establishment(s)", "establishmentsPlural": "no establishments | {n} establishment | {n} establishments", "employeesPlural": "no employees | {n} employee | {n} employees", "mainActivity": "Main activity", "relevantPublications": "Relevant publication(s)", "monitor": "Monitor", "typeOfEntity": "Type of entity", "lifecycleEvent": "Life cycle event", "updatedAt": "Last update", "updatedStatusAt": "Last status update", "disclaimer": "Disclaimer", "apiTerms": "API Terms", "legalNotices": "Legal Notices", "dataTVA": "TVA BE 0844.044.609", "dataCopyrights": "Data.be All rights reserved", "savedInList": "Saved in list", "notes": "Notes", "notePlaceholder": "Enter notes here...", "save": "Save", "cancel": "Cancel", "userActivity": "User activity", "systemEvents": "System events", "noActivityYet": "No activity yet...", "inspect": "Inspect", "open": "Open", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last Week", "clear": "Clear", "listName": "List name", "type": "Type", "frequency": "Frequency", "companiesInListing": "Companies in listing", "subscribers": "Subscribers", "tags": "Tags", "latestAlert": "Latest alert", "companiesPlural": "no companies | {count} company | {count} companies", "subscribersPlural": "no subscribers | {count} subscriber | {count} subscribers", "newWatchlist": "Create new watchlist", "overview": "Overview", "companies": "Companies", "watchlist": "Watchlist", "watchlistLead": "Welcome to your follow list! This page contains all the essential details and settings for the list you created.", "visibility": "Visibility", "CRITICAL": "Critical alert level", "HIGH": "High alert level", "MEDIUM": "Medium alert level", "LOW": "Low alert level", "searchForCompanies": "Search for companies", "batchUpload": "Batch upload", "companyVat": "Company VAT", "companyName": "Company name", "companyType": "Company type", "juridicalSituation": "Juridiacal situation", "uploadedAt": "Date uploaded", "importCompletedPlural": "Import completed; no new companies were succesfully added to the list. | Import completed; {count} new company were succesfully added to the list. | Import completed; {count} new companies were succesfully added to the list.", "add": "Add", "remove": "Remove", "searchType": {"COMPANIES": "companies | company | companies", "PERSONS": "persons | person | persons"}, "includeInactive": "Include inactive", "reset": "Reset", "apply": "Apply", "officialSources": "Official Sources", "myWatchlist": "My Watchlist", "recentlyViewed": "Recently Viewed", "search": "Search", "lastUpdate": "Last update", "active": "active", "inactive": "inactive", "denomination": "Denomination", "abbreviation": "Abbreviation", "vatNumber": "VAT number", "typeofEnterprise": "Type of enterprise", "dateOfCreation": "Date of creation", "firstPublication": "First publication", "numberOfEstablishments": "Number of establishments", "linkedEntities": "Linked entities", "legalRepresentatives": "Legal representatives", "sources": "Sources", "export": "Export", "contact": "Contact", "moreAddresses": "No more addresses | View {n} more address | View {n} more addresses", "website": "Website", "mobile": "Mobile", "phone": "Phone", "currentRatio": "Current ratio", "cashFlowInRelation": "Cash flow in relation to total debt", "returnOnEquity": "Return on equity", "numberOfEmployees": "Number of employees", "netCashRatio": "Net cash ratio", "grossOperation": "Gross operating sales margin", "annualAssembly": "Annual assembly", "endDateFinancialYear": "End date financial year", "startExc": "Start EXC fical year", "endExc": "End EXC fiscal year", "companyOverview": "Company overview", "sectors": "Sectors", "primaryActivity": "Primary activity", "secondaryActivities": "Secondary activities", "authorizations": "Authorizations", "characteristics": "Characteristics", "naceCode": "Nace code", "registrationDate": "Registration date", "dashboard": "Dashboard", "kycAndMandates": "KYC & Mandates", "companyInsights": "Company Insights", "riskAndCompliance": "Risk & Compliance", "businessGraph": "Business Graph", "includeEstablishments": "Include establishments", "includeNaturalPersons": "Include natural persons", "noAuthorizations": "No authorizations found.", "noSecondaryActivities": "No secondary activities.", "noCharacteristics": "No characteristics found.", "latestStatementFailToLoad": "Latest statement failed to load", "none": "None", "addressHistory": "Address history", "previousAddress": "Previous address", "addressType": "Address type", "googleMaps": "Google maps", "mainAddress": "Main address", "otherAddress": "Other address", "noAddressesHistory": "No addresses history available", "nameHistory": "Name history", "noNamesHistory": "No names history available", "menu": "<PERSON><PERSON>", "language": "Language", "code": "Code", "SOC": "Company", "ABB": "Abbreviation", "company": "Company", "profile": "Profile", "events": "Events", "timeline": "Timeline", "publications": "Publications", "structure": "Structure", "stakeholders": "Stakeholders", "group": "Group", "financials": "Financials", "graphs": "Graphs", "accountingFigures": "Accounting figures", "socialBalance": "Social balance", "keyRatios": "Key ratios", "primaryActivities": "Primary activities", "noprimaryActivities": "No primary activities.", "fr": "French", "en": "English", "de": "German", "nl": "Dutch", "be_moniteur": "<PERSON><PERSON><PERSON>", "be_moniteur_ifa": "Moniteur IFA", "be_moniteur_law": "Moniteur Law", "be_nbb_balance": "NBB Balance", "publicationType": "Publication type", "confirm": "Confirm", "listDeletionConfirmation": "Are you sure you want to delete this watchlist ? You can't undo this action.", "edit": "Edit", "account": "Account", "private": "Private", "nameRuleError": "Please type something.", "noCompaniesFound": "No companies found.", "addCompanyToList": "Add this company to your list.", "vat": "VAT", "updateDate": "Update date", "lastViewed": "Last viewed", "company-title": "Companies", "overview-title": "Overview", "accounting-figures-title": "Accounting Figures", "key-ratio-title": "Key Ratio", "social-balance-title": "Social Balance", "n-a": "N/A", "#na": "N/A", "profitability": "Profitability", "solvency": "Solvency", "liquidity": "Liquidity", "added_value": "Added Value", "noDataAvailable": "No data available", "create": "create", "notificationType": "Type", "notificationContact": "Contact", "notificationInterval": "Frequency", "confirmDelete": "Confirm delete", "confirmDeleteMessage": "Are you sure to delete this subscriber?", "you": "You", "searchUser": "Search for user to add", "comments": "Comments", "assignations": "Assignations", "companyIdentifier": "Company VAT", "CREATED": "Created", "ASSIGNED": "Assigned", "REVIEWED": "Reviewed", "noComments": "No comments", "addComment": "Add comment", "newComment": "New comment", "review": "Review", "assign": "Assign", "assignationsCount": "Not assigned | 1 assignation | {n} assignations", "eventName": "Event name", "closeTab": "Close tab", "selectToAssign": "Select user to assign", "confirmUnassign": "Confirm unassign", "alertUnassignConfirmation": "Are you sure to unassign this user?", "confirmUnreview": "Confirm unreview", "alertUnreviewConfirmation": "Are you sure to remove the review of this alert?", "alertNotification": "We've have detected {n} new updates about companies from your watch lists that match the criteria for the alert.", "reviewed": "Reviewed", "reviewMessage": "Action required. Please confirm that you have reviewed it.", "unreviewMessage": "Unreview an alert will trigger a notification to the subscriber.", "alert": "<PERSON><PERSON>", "history": "History", "confirmUncomment": "Remove comment from alert", "alertuncommentConfirmation": "Are you sure to remove this comment?", "sections": "Sections", "filters": "filters", "results": "Results", "typeToSearchCompanies": "Type to search for companies.", "before": "Before", "after": "After", "severity": "Severity", "WEEKLY": "Weekly", "DAILY": "Daily", "MONTHLY": "Monthly", "watchlistSaved": "Watchlist saved!", "watchlistCreated": "Watchlist created!", "watchlistCannotBeDeleted": "Watchlist cannot be deleted.", "watchlitDeletedWithSuccess": "Watchlist deleted with success!", "uncomment": "Uncomment", "assignedTo": "Assigned to", "thisAlertIsReviewed": "This alert is reviewed.", "thisAlertIsNotReviewed": "This alert is not yet reviewed.", "unreview": "unreview", "errorAddToWatchlist": "The company cannot be added to this watchlist.", "errorRemoveFromWatchlist": "The company cannot be removed from this watchlist.", "genericError": "Oops something went wrong!", "watchlistLoadError": "Watchlist cannot be loaded.", "alertsLoadError": "Alerts cannot be loaded.", "errorLoadWatchlist": "Error during load this watchlist.", "errorLoadCompany": "This company cannot be loaded.", "errorUpdateWatchlist": "Error during watchlist update.", "alertReviewSuccess": "This alert is review with success.", "alertUnreviewSuccess": "This alert is unreview with success.", "alertCommentSuccess": "<PERSON><PERSON> commented with success.", "alertAssignSuccess": "<PERSON><PERSON> assigned with success.", "subscribersRemoveSuccess": "Subscriber removed with success.", "subscribersUpdatedSuccess": "Subscriber updated with success.", "subscribersAddedSuccess": "Subscriber add with success.", "noAlerts": "There is no alerts for now.", "pageNotFound": "Page not found.", "moreCompaniesTooltip": "If don't find the company your are looking for, use the advanced search tool.", "noWatchlistCreated": "There is no watchlist linked to your account. You can create one to follow companies updates.", "noCompaniesSearched": "Here you will find recently searched companies and can access them quickly.", "hideInactives": "<PERSON>de inactives", "age": "Age", "hideEmptyValues": "Hide empty rows", "topResultsOf": "Top {0} results of {1} companies for this search.", "companiesCount": "Companies in our database", "publicationCount": "Official company publications", "accountCount": "Annual accounts", "accessToAllWatchlists": "Access to all watchlists", "clearAllRecentlyViewed": "Clear all recently viewed companies", "reviewedBy": "Reviewed by", "unreviewed": "Unreviewed", "uncommented": "Uncommented", "unassigned": "Unassigned", "NOTIFIED": "Notified", "ADD_COMMENT": "Comment added", "ADD_ASSIGN": "Assignation added", "UNREVIEWED": "Review removed", "REMOVE_ASSIGN": "Assignation removed", "REMOVE_COMMENT": "Comment removed", "alertUncommentSuccess": "Comment remove with success.", "alertUnassignSuccess": "Assignation removed with success.", "financialsOverview": "Financials Overview", "accountDate": "Account date", "employees": "Number of employees", "turnover": "Turnover", "birthdate": "Birthdate", "current_assets": "Current assets", "gross_operating_margin": "Gross operating margin", "tangible_fixed_assets": "Tangible fixed assets", "equity": "Equity", "gain_loss_period": "Gain loss for this period", "current_ratio": "Current ratio", "net_cash": "Net cash", "self_financing_degree": "Self financing degree", "return_on_equity": "Return on equity", "createdAt": "Creation date", "COMPANY_NAME_SOCIAL": "Company Name Social", "COMPANY_NAME_COMMERCIAL": "Company Name Commercial", "COMPANY_NAME_ABBREVIATED": "Company Name Abbreviated", "COMPANY_NAME_ESTABLISHMENT": "Company Name Establishment", "COMPANY_LEGALREP_PERSON": "Company Legal Representative Person", "COMPANY_LEGALREP_COMPANY": "Company Legal Representative Company", "COMPANY_STAKEHOLDERS": "Company Stakeholders", "COMPANY_MANAGEMENT": "Company Management", "COMPANY_EXTERNAL_ROLES": "Company External Roles", "COMPANY_RELATED_COMPANIES": "Company Related Companies", "COMPANY_STATUS": "Company Status", "COMPANY_LEGAL_SITUATION": "Company Legal Situation", "COMPANY_INSOLVENCY": "Company Insolvency", "COMPANY_BANKRUPTCY": "Company Bankruptcy", "COMPANY_DELAYS": "Company Delays", "COMPANY_LEGAL_CASE": "Company Legal Case", "COMPANY_LEGAL_FORM": "Company Legal Form", "COMPANY_NACE_CODES": "Company NACE Codes", "COMPANY_EMPLOYER_STATUS": "Company Employer Status", "COMPANY_EMPLOYER_SIZE": "Company Employer Size", "COMPANY_PRIMARY_ADDRESS": "Company Primary Address", "COMPANY_OTHER_ADDRESS": "Company Other Address", "COMPANY_CONTACTS": "Company Contacts", "COMPANY_FINANCIALS": "Company Financials", "COMPANY_SCORE": "Company Score", "COMPANY_RELATED_MENTION": "Company Related Mention", "COMPANY_ESTABLISHMENTS": "Company Establishments", "ESTABLISHMENT_PRIMARY_NAME": "Establishment Primary Name", "ESTABLISHMENT_OTHER_NAME": "Establishment Other Name", "PERSON_MANDATE": "Person Mandate", "PERSON_INDEPENDENT": "Person Independent", "PERSON_CONSERVATORSHIP": "Person Conservatorship", "PERSON_DEATH": "Person Death", "PERSON_COMPANY_TROUBLE": "Person Company Trouble", "PERSON_RELATED_MENTION": "Person Related Mention", "PERSON_ADDRESS": "Person Address", "PERSON_CONTACT": "Person Contact", "LOCATION_CHANGE": "Location Change", "LOCATION_ZONE": "Location Zone", "SEARCH_MENTION": "Search Mention", "COMPANY_KBO_START_DATE": "Company KBO Start Date", "risk": "Risk", "compliance": "Compliance", "entityType": "Entity type", "startDate": "Start date", "addresses": "Addresses", "country": "Country", "primaryAddress": "Primary address", "notPrimaryAddress": "Other address", "postalCode": "Postal code", "locality": "Locality", "role": "Role", "personName": "Person name", "noEstablishments": "There is no establishments known for this company.", "noLegalRepresentatives": "There is no legal representatives known for this company.", "noFuntions": "There is no legal functions known for this company.", "representatives": "Representatives", "functions": "Functions", "selectedStatus": "Selected status", "selectedEvents": "Selected events", "establishment": "Establishment", "from": "From", "title": "Title", "to": "To", "newEntry": "New entry", "selectedSeverities": "Selected severities", "publication": "Publication", "empty": "(Empty value)", "uploadCompaniesTitle": "Import companies", "importCompaniesTextarea": " One vat number per line or comma separated", "importCompaniesInstructions": "Enter a list of VAT numbers", "requiredField": "Required field", "alertUploadSuccess": "Companies add the watchlist with success.", "emailPolicy": "Please insert a valid email address", "passwordPolicy": "Password required", "exportToCSV": "Export to CSV", "lifeCycleEvent": "Life cycle event", "notAssignee": "Not assigned", "alertDate": "Alert date", "commentPlaceholder": "Click and start typing to add a note...", "alertInfos": "Alert infos", "BE_MONITEUR_PUBLICATION": "Belgian Monitor Publications", "BE_NBB_FINANCE": "Belgian NBB Finances", "BE_KBO_COMPANY": "Belgian KBO Companies", "BE_SOCSEC_EMPLOYEE": "belgian SOCSEC Employees", "BE_DATABE_COMPANY": "Belgian Data.be Companies", "resetFilters": "Reset filters", "publicationsError": "Failed to search publications", "sortBy": "Sort by", "bestMatch": "Best match", "publicationDateNewest": "Newest publication date", "publicationDateOldest": "Oldest publication date", "noResultFound": "No results for found.", "resultsFor": "{total} results for \"{input}\"", "showMore": "Show more", "showLess": "Show less", "notary": "Notary", "auditor": "Auditor", "notaries": "Notaries", "auditors": "Auditors", "postalCodes": "Postal codes", "businessReport": "Business Report", "person": "Person", "externalCompany": "External company", "report": "Report", "reportFooter": "Report generated for {email} - Based on data.be information as of {date}", "publicationDate": "Publication date", "startDateInYears": "Start date (in years)", "startDateAsc": "Start date (a-z)", "startDateDesc": "Start date (z-a)", "companyNameAsc": "Name (a-z)", "companyNameDesc": "Name (z-a)", "hasPhone": "Has phone", "hasEmail": "Has email", "hasWebsite": "Has website", "newList": "Create new list", "listIntro": "You will be notified when a change occurs to the followed companies.", "listTooltip": "You will be notified by email", "listChildIntro": "Configure your list of notification.", "listNameHelp": "Set a name for you list so you will be enable to find easily the             notification.", "listTagsHelp": "You can add tags to organise your lists by topics or any attribute you want.", "listCompaniesHelp": "Add or remove companies to follow to your list. First save the list to add companies.", "monitoringIntro": "Get notified when an change occurs to a company. You can organise your         watchlist with name and tags. Each watchlist can monitor a defined level         of alert from low to critical.", "endDate": "End date", "searchForPublications": "Search for publications", "searchForEntities": "Type a company name or VAT", "listCreateError": "Cannot create the list.", "errorUpdateList": "Cannot save the list.", "errorremoveFromList": "Cannot remove company from the list.", "errorAddToList": "Cannot add company to the list.", "listSavedSuccess": "List saved with success.", "selectLanguage": "Choose your language for the application", "persons": "Persons", "lastAppearance": "Last appearance", "agePlural": "No age | {n} year old | {n} years old", "searchPersonsNoData": "Search for a person full name.", "personNotFound": "Person not found.", "profession": "Profession", "fax": "Fax", "firstAppearance": "First appearance", "back": "Back", "firstName": "First name", "lastName": "Last name", "updateUserInfos": "Update your user infos", "organisation": "Organisation", "loginError": "Error during login.", "year": "Year", "userUpdatedSuccess": "Your profile is updated with success.", "networkGraphIntro": "Search for a company to display the company network graph.", "subscription": "Subscription", "EN": "English", "FR": "French", "NL": "Dutch", "learnMore": "Learn more", "searchForPersons": "Search for persons (first name and last name required)", "features": {"BUSINESS_GRAPH": {"title": "Business Graph", "body": "The Business Graph is a visual representation of the relationships between companies and their stakeholders. It provides a comprehensive overview of the corporate structure and the connections between companies and their stakeholders."}, "COMPANY_ALERTS_ADVANCED": {"title": "Company Alerts Advanced", "body": "Company Alerts Advanced is a powerful tool that allows you to monitor companies and receive alerts when changes occur. You can set up alerts based on a wide range of criteria, such as changes in company status, financial performance, or key personnel."}, "COMPANY_ALERTS_BASIC": {"title": "Company Alerts Basic", "body": "Company Alerts Basic is a simple tool that allows you to monitor companies and receive alerts when changes occur. You can set up alerts based on a limited range of criteria, such as changes in company status or financial performance."}, "COMPANY_ALERTS_WEBHOOK": {"title": "Company Alerts Webhook", "body": "Company Alerts Webhook is a tool that allows you to monitor companies and receive alerts when changes occur. You can set up alerts based on a wide range of criteria, such as changes in company status, financial performance, or key personnel. Alerts are sent to a webhook URL of your choice."}, "COMPANY_EXPORT": {"title": "Company Export", "body": "Company Export is a tool that allows you to export company data in a variety of formats, such as CSV or Excel. You can export data for individual companies or for multiple companies at once."}, "COMPANY_FULL_FINANCIALS": {"title": "Company Full Financials", "body": "Company Full Financials is a tool that provides detailed financial information for companies. You can view key financial ratios, balance sheet data, and income statement data for individual companies."}, "COMPANY_INFO": {"title": "Company Info", "body": "Company Info is a tool that provides basic information about companies, such as their name, address, and VAT number. You can use this tool to quickly look up information about a specific company."}, "COMPANY_KEY_FINANCIALS": {"title": "Business Graph", "body": "The Business Graph is a visual representation of the relationships between companies and their stakeholders. It provides a comprehensive overview of the corporate structure and the connections between companies and their stakeholders."}, "COMPANY_LEAD_GENERATOR": {"title": "Business Graph", "body": "The Business Graph is a visual representation of the relationships between companies and their stakeholders. It provides a comprehensive overview of the corporate structure and the connections between companies and their stakeholders."}, "COMPANY_MANDATES": {"title": "Business Mandates", "body": "The Business Mandates tool provides information about the mandates held by individuals and companies. You can use this tool to search for mandates by name, company, or other criteria."}, "COMPANY_PDF_REPORT": {"title": "Company PDF Report", "body": "The Company PDF Report tool allows you to generate a detailed report about a company in PDF format. The report includes information about the company's financial performance, key personnel, and other relevant details."}, "COMPANY_PUBLICATIONS": {"title": "Company Publications", "body": "The Company Publications tool provides access to official publications related to companies. You can use this tool to search for publications by company name, VAT number, or other criteria."}, "COMPANY_SEARCH_ADVANCED_FILTERS": {"title": "Company search with advanced filters", "body": "The Company Search tool allows you to search for companies using a wide range of criteria, such as company name, VAT number, or address. You can use advanced filters to refine your search results."}, "COMPANY_SEARCH": {"title": "Company Search", "body": "The Company Search tool allows you to search for companies using a variety of criteria, such as company name, VAT number, or address. You can use this tool to quickly find information about a specific company."}, "COMPANY_TIMELINE": {"title": "Company Timeline", "body": "The Company Timeline tool provides a chronological overview of key events related to a company. You can use this tool to track changes in company status, financial performance, and other important details."}, "COMPLIANCE_REPORT": {"title": "Compliance Report", "body": "The Compliance Report tool provides a detailed overview of a company's compliance status. You can use this tool to check whether a company is in good standing with regulatory authorities."}, "FULL_TEXT_SEARCH": {"title": "Full Text Search", "body": "The Full Text Search tool allows you to search for companies using free text search. You can enter keywords or phrases to find companies that match your search criteria."}, "MULTI_USERS": {"title": "Multi Users", "body": "The Multi Users tool allows you to create multiple user accounts for your team. You can assign different roles and permissions to each user, depending on their needs."}, "PERSON_SEARCH": {"title": "Person Search", "body": "The Person Search tool allows you to search for individuals using a variety of criteria, such as name, address, or profession. You can use this tool to quickly find information about a specific person."}}, "previouslySeenPersons": "Previously seen persons", "clearAllpreviouslySeenPersons": "Clear all previously seen persons", "errorRemoveList": "Error while remove the list.", "listRemovedSuccess": "List remove with success.", "accountingYear": "Accounting year", "personSearchNotAllowed": "Your account does not allow to search for persons informations.", "createAccount": "Not yet registered, create an account here.", "forgotPassword": "Forgot password?"}