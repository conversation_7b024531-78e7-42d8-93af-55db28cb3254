#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// List of files that are known to have template parsing issues
const brokenFiles = [
  'src/pages/companies/children/timeline.vue',
  'src/pages/persons/children/_id.vue',
  'src/pages/search/index.vue',
  'src/pages/companies/children/key-ratios.vue'
  // Add more as we discover them
];

// Function to restore and fix a Vue file
function restoreAndFixFile(filePath) {
  try {
    console.log(`Restoring and fixing ${filePath}...`);

    // Get the original content from git
    const originalContent = execSync(`git show HEAD:${filePath}`, {
      encoding: 'utf8'
    });

    // Extract template, script, and style sections
    const templateMatch = originalContent.match(
      /<template[^>]*>([\s\S]*?)<\/template>/
    );
    const scriptMatch = originalContent.match(
      /<script[^>]*>([\s\S]*?)<\/script>/
    );
    const styleMatch = originalContent.match(/<style[^>]*>([\s\S]*?)<\/style>/);

    if (!templateMatch || !scriptMatch) {
      console.log(`Skipping ${filePath} - missing template or script section`);
      return false;
    }

    // Reconstruct the file with script first, then template, then style
    let newContent = '';

    // Add script section first
    const scriptAttrs = scriptMatch[0].match(/<script([^>]*)>/)[1] || '';
    newContent += `<script${scriptAttrs}>${scriptMatch[1]}</script>\n\n`;

    // Add template section
    const templateAttrs = templateMatch[0].match(/<template([^>]*)>/)[1] || '';
    newContent += `<template${templateAttrs}>${templateMatch[1]}</template>`;

    // Add style section if it exists
    if (styleMatch) {
      const styleAttrs = styleMatch[0].match(/<style([^>]*)>/)[1] || '';
      newContent += `\n\n<style${styleAttrs}>${styleMatch[1]}</style>`;
    }

    // Add final newline
    newContent += '\n';

    // Write the new content back to the file
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Fixed ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log(`Restoring and fixing ${brokenFiles.length} broken Vue files...`);

let fixedCount = 0;

for (const file of brokenFiles) {
  const result = restoreAndFixFile(file);
  if (result) {
    fixedCount++;
  }
}

console.log(`\nSummary:`);
console.log(`Fixed: ${fixedCount} files`);
console.log(`Failed: ${brokenFiles.length - fixedCount} files`);

// Try to identify any other broken files by running a quick build check
console.log('\nChecking for any other broken files...');
try {
  execSync('npm run build', { stdio: 'pipe' });
  console.log('Build successful! All files are now fixed.');
} catch (error) {
  const output = error.stdout
    ? error.stdout.toString()
    : error.stderr.toString();
  const match = output.match(/src\/[^:]+\.vue/);
  if (match) {
    console.log(`Found another broken file: ${match[0]}`);
    console.log(
      'You may need to add this file to the brokenFiles list and run the script again.'
    );
  } else {
    console.log(
      'Build failed but could not identify the broken file from the output.'
    );
  }
}
