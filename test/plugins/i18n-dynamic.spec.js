import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  loadLocaleMessages,
  setLocale,
  getAvailableLocales,
  isLocaleLoaded,
  AVAILABLE_LOCALES,
  DEFAULT_LOCALE
} from '@/plugins/i18n-dynamic';

// Mock fetch for testing
global.fetch = vi.fn();

describe('i18n-dynamic', () => {
  beforeEach(() => {
    // Reset fetch mock
    vi.clearAllMocks();
    
    // Mock successful fetch responses
    global.fetch.mockImplementation((url) => {
      if (url.includes('.json')) {
        const mockData = url.includes('financials') 
          ? { financial_key: 'Financial Value' }
          : { test_key: 'Test Value' };
        
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockData)
        });
      }
      return Promise.reject(new Error('Not found'));
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('loadLocaleMessages', () => {
    it('should load locale messages successfully', async () => {
      const result = await loadLocaleMessages('en');
      
      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(global.fetch).toHaveBeenCalledWith('/locales/en.json');
      expect(global.fetch).toHaveBeenCalledWith('/locales/en.financials.json');
    });

    it('should return false for invalid locale', async () => {
      const result = await loadLocaleMessages('invalid');
      
      expect(result).toBe(false);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should return true if locale is already loaded', async () => {
      // Load locale first time
      await loadLocaleMessages('en');
      vi.clearAllMocks();
      
      // Load same locale again
      const result = await loadLocaleMessages('en');
      
      expect(result).toBe(true);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should handle fetch errors gracefully', async () => {
      global.fetch.mockRejectedValue(new Error('Network error'));
      
      const result = await loadLocaleMessages('fr');
      
      expect(result).toBe(false);
    });

    it('should handle non-ok responses', async () => {
      global.fetch.mockResolvedValue({
        ok: false,
        status: 404
      });
      
      const result = await loadLocaleMessages('nl');
      
      expect(result).toBe(false);
    });
  });

  describe('setLocale', () => {
    it('should set locale successfully', async () => {
      const result = await setLocale('en');
      
      expect(result).toBe(true);
    });

    it('should return false for invalid locale', async () => {
      const result = await setLocale('invalid');
      
      expect(result).toBe(false);
    });
  });

  describe('getAvailableLocales', () => {
    it('should return array of available locales', () => {
      const locales = getAvailableLocales();
      
      expect(locales).toEqual(AVAILABLE_LOCALES);
      expect(locales).toContain('en');
      expect(locales).toContain('fr');
      expect(locales).toContain('nl');
    });
  });

  describe('isLocaleLoaded', () => {
    it('should return false for unloaded locale', () => {
      const result = isLocaleLoaded('fr');
      
      expect(result).toBe(false);
    });

    it('should return true for loaded locale', async () => {
      await loadLocaleMessages('en');
      const result = isLocaleLoaded('en');
      
      expect(result).toBe(true);
    });
  });

  describe('constants', () => {
    it('should have correct default locale', () => {
      expect(DEFAULT_LOCALE).toBe('en');
    });

    it('should have correct available locales', () => {
      expect(AVAILABLE_LOCALES).toEqual(['en', 'fr', 'nl']);
    });
  });
});
