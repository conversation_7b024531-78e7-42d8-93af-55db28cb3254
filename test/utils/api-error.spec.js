import { describe, it, expect } from 'vitest';
import errorMessage from '@/utils/api-error';
import i18n from '@/plugins/i18n-dynamic';

describe('api-error', () => {
  it('should return the default error message', () => {
    expect(errorMessage()).toEqual({
      error: {
        message: i18n.global.t('genericError'),
        status: 500
      },
      isError: true
    });
  });

  it('should return the error status and message provided', () => {
    const newError = {
      response: {
        status: 404,
        data: {
          error: i18n.global.t('pageNotFound')
        }
      }
    };
    expect(errorMessage(newError)).toEqual({
      error: {
        message: {
          error: i18n.global.t('pageNotFound')
        },
        status: 404
      },
      isError: true
    });
  });
});
