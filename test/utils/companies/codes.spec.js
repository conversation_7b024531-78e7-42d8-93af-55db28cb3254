import { describe, it, expect, beforeEach } from 'vitest';
import { findCodeName } from '@/utils/companies/codes';
import i18n from '@/plugins/i18n';

beforeEach(() => {
  i18n.global.locale.value = 'en';
});

describe('findCodeName', () => {
  it('should return null for empty code', () => {
    expect(findCodeName()).toBeNull();
  });

  it('should return null for code not found in list', () => {
    const list = [
      { AUTH001: [{ en: { label: 'General Trading License' } }] },
      { AUTH002: [{ en: { label: 'Import/Export License' } }] },
      { AUTH003: [{ en: { label: 'Special Operations Permit' } }] }
    ];

    expect(findCodeName('AUTH004', list)).toBeNull();
  });

  it('should return null for code without translations', () => {
    const list = [
      { AUTH001: [{ de: { label: 'General Trading License' } }] },
      { AUTH002: [{ de: { label: 'Import/Export License' } }] },
      { AUTH003: [{ de: { label: 'Special Operations Permit' } }] }
    ];

    expect(findCodeName('AUTH001', list)).toBeNull();
  });

  it('should return translation for code with translations', () => {
    const list = [
      { AUTH001: [{ en: { label: 'General Trading License' } }] },
      { AUTH002: [{ en: { label: 'Import/Export License' } }] },
      { AUTH003: [{ en: { label: 'Special Operations Permit' } }] }
    ];

    expect(findCodeName('AUTH001', list)).toBe('General Trading License');
  });

  it('should return translation for code with translations in French', () => {
    i18n.global.locale.value = 'fr';
    const list = [
      {
        AUTH001: [
          { fr: { label: 'Licence de commerce général' } },
          { en: { label: 'General Trading License' } }
        ]
      },
      {
        AUTH002: [
          { fr: { label: "Licence d'import/export" } },
          { en: { label: 'Import/Export License' } }
        ]
      },
      {
        AUTH003: [
          { fr: { label: "Permis d'opérations spéciales" } },
          { en: { label: 'Special Operations Permit' } }
        ]
      }
    ];

    expect(findCodeName('AUTH001', list)).toBe('Licence de commerce général');
  });
});
