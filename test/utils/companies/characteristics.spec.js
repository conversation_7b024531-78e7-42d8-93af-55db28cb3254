import { describe, it, expect, vi } from 'vitest';
import { prepareCharacteristics } from '@/utils/companies/characteristics';
import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => date)
}));

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code) => `Characteristic ${code}`)
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getCharacteristicCodes: vi.fn(() =>
    Promise.resolve([
      {
        CODE1: [{ nl: { code: 'CODE1', label: 'Test Characteristic 1' } }]
      },
      {
        CODE2: [{ nl: { code: 'CODE2', label: 'Test Characteristic 2' } }]
      }
    ])
  )
}));

describe('prepareCharacteristics', () => {
  it('should return empty array for null input', async () => {
    expect(await prepareCharacteristics(null)).toEqual([]);
  });

  it('should return empty array for undefined input', async () => {
    expect(await prepareCharacteristics(undefined)).toEqual([]);
  });

  it('should return empty array for empty input', async () => {
    expect(await prepareCharacteristics([])).toEqual([]);
  });

  it('should transform characteristics with all fields', async () => {
    const input = [
      {
        code: 'CODE1',
        start_date: '2023-01-01'
      },
      {
        code: 'CODE2',
        start_date: '2023-02-01'
      }
    ];

    const expected = [
      {
        id: 1,
        code: 'CODE2',
        name: 'Characteristic CODE2',
        startDate: '2023-02-01'
      },
      {
        id: 0,
        code: 'CODE1',
        name: 'Characteristic CODE1',
        startDate: '2023-01-01'
      }
    ];

    expect(await prepareCharacteristics(input)).toEqual(expected);
    expect(findCodeName).toHaveBeenCalledWith('CODE1', expect.any(Object));
    expect(findCodeName).toHaveBeenCalledWith('CODE2', expect.any(Object));
    expect(formatDate).toHaveBeenCalledWith('2023-02-01', expect.any(Object));
    expect(formatDate).toHaveBeenCalledWith('2023-02-01', expect.any(Object));
  });

  it('should handle characteristics without start date', async () => {
    const input = [
      {
        code: 'CODE1'
      }
    ];

    const expected = [
      {
        id: 0,
        code: 'CODE1',
        name: 'Characteristic CODE1',
        startDate: null
      }
    ];

    expect(await prepareCharacteristics(input)).toEqual(expected);
  });

  it('should sort characteristics by start date in descending order', async () => {
    const input = [
      {
        code: 'CODE1',
        start_date: '2023-01-01'
      },
      {
        code: 'CODE2',
        start_date: '2023-03-01'
      },
      {
        code: 'CODE3',
        start_date: '2023-02-01'
      }
    ];

    const result = await prepareCharacteristics(input);

    expect(result[0].code).toBe('CODE2');
    expect(result[1].code).toBe('CODE3');
    expect(result[2].code).toBe('CODE1');
  });

  it('should handle characteristics with same start date', async () => {
    const input = [
      {
        code: 'CODE1',
        start_date: '2023-01-01'
      },
      {
        code: 'CODE2',
        start_date: '2023-01-01'
      }
    ];

    const result = await prepareCharacteristics(input);
    expect(result).toHaveLength(2);
    expect(result[0].startDate).toBe(result[1].startDate);
  });
});
