import { describe, it, expect, vi } from 'vitest';
import { prepareAuthorizations } from '@/utils/companies/authorizations';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => {
    const [year, month, day] = date.split('-');
    return `${month}/${day}/${year}`;
  })
}));

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code) => {
    const mockCodes = {
      AUTH001: 'General Trading License',
      AUTH002: 'Import/Export License',
      AUTH003: 'Special Operations Permit'
    };
    return mockCodes[code];
  })
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getAuthorizationCodes: vi.fn(() =>
    Promise.resolve([
      {
        AUTH001: [{ nl: { code: 'AUTH001', label: 'General Trading License' } }]
      },
      {
        AUTH002: [{ nl: { code: 'AUTH002', label: 'Import/Export License' } }]
      }
    ])
  )
}));

describe('prepareAuthorizations', () => {
  it('should return empty array for empty input', async () => {
    expect(await prepareAuthorizations()).toEqual([]);
    expect(await prepareAuthorizations([])).toEqual([]);
  });

  it('should format single authorization correctly', async () => {
    const authorizations = [
      {
        code: 'AUTH001',
        start_date: '2023-01-15'
      }
    ];

    expect(await prepareAuthorizations(authorizations)).toEqual([
      {
        id: 0,
        code: 'AUTH001',
        name: 'General Trading License',
        startDate: '01/15/2023'
      }
    ]);
  });

  it('should format multiple authorizations correctly', async () => {
    const authorizations = [
      {
        code: 'AUTH001',
        start_date: '2023-01-15'
      },
      {
        code: 'AUTH002',
        start_date: '2023-02-20'
      },
      {
        code: 'AUTH003',
        start_date: '2023-03-25'
      }
    ];

    expect(await prepareAuthorizations(authorizations)).toEqual([
      {
        id: 0,
        code: 'AUTH001',
        name: 'General Trading License',
        startDate: '01/15/2023'
      },
      {
        id: 1,
        code: 'AUTH002',
        name: 'Import/Export License',
        startDate: '02/20/2023'
      },
      {
        id: 2,
        code: 'AUTH003',
        name: 'Special Operations Permit',
        startDate: '03/25/2023'
      }
    ]);
  });

  it('should handle authorization without start date', async () => {
    const authorizations = [
      {
        code: 'AUTH001'
      }
    ];

    expect(await prepareAuthorizations(authorizations)).toEqual([
      {
        id: 0,
        code: 'AUTH001',
        name: 'General Trading License',
        startDate: null
      }
    ]);
  });

  it('should handle unknown authorization code', async () => {
    const authorizations = [
      {
        code: 'UNKNOWN_CODE',
        start_date: '2023-01-15'
      }
    ];

    expect(await prepareAuthorizations(authorizations)).toEqual([
      {
        id: 0,
        code: 'UNKNOWN_CODE',
        name: undefined,
        startDate: '01/15/2023'
      }
    ]);
  });

  it('should handle mixed valid and invalid authorizations', async () => {
    const authorizations = [
      {
        code: 'AUTH001',
        start_date: '2023-01-15'
      },
      {
        code: 'UNKNOWN_CODE',
        start_date: '2023-02-20'
      },
      {
        code: 'AUTH002'
      }
    ];

    expect(await prepareAuthorizations(authorizations)).toEqual([
      {
        id: 0,
        code: 'AUTH001',
        name: 'General Trading License',
        startDate: '01/15/2023'
      },
      {
        id: 1,
        code: 'UNKNOWN_CODE',
        name: undefined,
        startDate: '02/20/2023'
      },
      {
        id: 2,
        code: 'AUTH002',
        name: 'Import/Export License',
        startDate: null
      }
    ]);
  });
});
