import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  findMainActivity,
  prepareActivities
} from '@/utils/companies/activities';
import i18n from '@/plugins/i18n';

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getActivityCodes: vi.fn(() =>
    Promise.resolve([
      {
        A: [
          {
            fr: {
              label: 'SECTION A -- AGRICULTURE, SYLVICULTURE ET PÊCHE',
              code: 'A'
            }
          },
          {
            nl: {
              label: 'SECTIE A -- LANDBOUW, BOSBOUW EN VISSERIJ',
              code: 'A'
            }
          }
        ]
      },
      {
        '01': [
          {
            fr: {
              label:
                'Culture et production animale, chasse et services annexes',
              code: '01'
            }
          },
          {
            nl: {
              label:
                '<PERSON><PERSON><PERSON> van gewassen, veeteelt, jacht en diensten in verband met deze activiteiten',
              code: '01'
            }
          }
        ]
      },
      {
        '011': [
          {
            fr: {
              label: 'Cultures non permanentes',
              code: '011'
            }
          },
          {
            nl: {
              label: '<PERSON><PERSON><PERSON> van eenjarige gewassen',
              code: '011'
            }
          }
        ]
      },
      {
        '0111': [
          {
            fr: {
              label:
                "Culture de céréales (à l'exception du riz), de légumineuses et de graines oléagineuses",
              code: '0111'
            }
          },
          {
            nl: {
              label:
                'Teelt van granen (m.u.v. rijst), peulgewassen en oliehoudende zaden',
              code: '0111'
            }
          }
        ]
      }
    ])
  )
}));

beforeEach(() => {
  i18n.global.locale.value = 'nl';
});

describe('findMainActivity', () => {
  it('should return null is nothing provided', async () => {
    expect(await findMainActivity()).toEqual(null);
  });

  it('should return the activity name base on code', async () => {
    const activities = [
      {
        code: 'A',
        active: true,
        primary: true
      }
    ];
    expect(await findMainActivity(activities)).toEqual(
      'SECTIE A -- LANDBOUW, BOSBOUW EN VISSERIJ'
    );
  });

  it('should return the main activity from an array of activities', async () => {
    const activities = [
      {
        code: '01',
        primary: false
      },
      {
        code: '011',
        primary: false
      },
      {
        code: '0111',
        primary: true
      }
    ];

    expect(await findMainActivity(activities)).toEqual(
      'Teelt van gewassen, veeteelt, jacht en diensten in verband met deze activiteiten'
    );
  });

  it('should return the active primary activity from an array of activities', async () => {
    i18n.global.locale.value = 'en';
    const activities = [
      {
        code: '01',
        active: true,
        primary: false
      },
      {
        code: '011',
        active: true,
        primary: false
      },
      {
        code: 'A',
        active: true,
        primary: true
      }
    ];

    expect(await findMainActivity(activities, true)).toEqual(
      'SECTIE A -- LANDBOUW, BOSBOUW EN VISSERIJ'
    );
  });
});

describe('prepareActivities', () => {
  it('should return an empty array if nothing provided', async () => {
    expect(await prepareActivities()).toEqual([]);
  });

  it('should return the prepared activities', async () => {
    const activities = [
      {
        id: 1,
        code: '01',
        primary: false,
        start_date: '12-01-2024'
      },
      {
        id: 2,
        code: '011',
        primary: false,
        start_date: '12-02-2024'
      },
      {
        id: 3,
        code: '0111',
        primary: true,
        start_date: null
      }
    ];

    expect(await prepareActivities(activities)).toEqual([
      {
        code: '011',
        id: 1,
        name: 'Teelt van eenjarige gewassen',
        primary: false,
        startDate: '12/2/2024'
      },
      {
        code: '01',
        id: 0,
        name: 'Teelt van gewassen, veeteelt, jacht en diensten in verband met deze activiteiten',
        primary: false,
        startDate: '12/1/2024'
      },
      {
        code: '0111',
        id: 2,
        name: 'Teelt van granen (m.u.v. rijst), peulgewassen en oliehoudende zaden',
        primary: true,
        startDate: null
      }
    ]);
  });
});
