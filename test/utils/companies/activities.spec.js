import { describe, it, expect, beforeEach } from 'vitest';
import {
  findMainActivity,
  prepareActivities
} from '@/utils/companies/activities';
import i18n from '@/plugins/i18n';

beforeEach(() => {
  i18n.global.locale.value = 'nl';
});

describe('findMainActivity', () => {
  it('should return null is nothing provided', () => {
    expect(findMainActivity()).toEqual(null);
  });

  it('should return the activity name base on code', () => {
    const activities = [
      {
        code: 'A',
        active: true,
        primary: true
      }
    ];
    expect(findMainActivity(activities)).toEqual(
      'SECTIE A -- LANDBOUW, BOSBOUW EN VISSERIJ'
    );
  });

  it('should return the main activity from an array of activities', () => {
    const activities = [
      {
        code: '01',
        primary: false
      },
      {
        code: '011',
        primary: false
      },
      {
        code: '0111',
        primary: true
      }
    ];

    expect(findMainActivity(activities)).toEqual(
      '<PERSON><PERSON><PERSON> van <PERSON>, ve<PERSON><PERSON><PERSON>, jacht en diensten in verband met deze activiteiten'
    );
  });

  it('should return the active primary activity from an array of activities', () => {
    i18n.global.locale.value = 'en';
    const activities = [
      {
        code: '01',
        active: true,
        primary: false
      },
      {
        code: '011',
        active: true,
        primary: false
      },
      {
        code: 'A',
        active: true,
        primary: true
      }
    ];

    expect(findMainActivity(activities, true)).toEqual(
      'SECTIE A -- LANDBOUW, BOSBOUW EN VISSERIJ'
    );
  });
});

describe('prepareActivities', () => {
  it('should return an empty array if nothing provided', () => {
    expect(prepareActivities()).toEqual([]);
  });

  it('should return the prepared activities', () => {
    const activities = [
      {
        id: 1,
        code: '01',
        primary: false,
        start_date: '12-01-2024'
      },
      {
        id: 2,
        code: '011',
        primary: false,
        start_date: '12-02-2024'
      },
      {
        id: 3,
        code: '0111',
        primary: true,
        start_date: null
      }
    ];

    expect(prepareActivities(activities)).toEqual([
      {
        code: '011',
        id: 1,
        name: 'Teelt van eenjarige gewassen',
        primary: false,
        startDate: '12/2/2024'
      },
      {
        code: '01',
        id: 0,
        name: 'Teelt van gewassen, veeteelt, jacht en diensten in verband met deze activiteiten',
        primary: false,
        startDate: '12/1/2024'
      },
      {
        code: '0111',
        id: 2,
        name: 'Teelt van granen (m.u.v. rijst), peulgewassen en oliehoudende zaden',
        primary: true,
        startDate: null
      }
    ]);
  });
});
