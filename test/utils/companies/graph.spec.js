import { describe, it, expect } from 'vitest';
import {
  prepareSeries,
  prepareAxis,
  prepareOptions,
  formatter
} from '@/utils/companies/graph';

describe('prepareSeries', () => {
  it('should return an empty array if nothing provided', () => {
    expect(prepareSeries()).toEqual([]);
  });

  it('should handle direct path access without nested properties', () => {
    const selectedSeries = ['revenue'];
    const options = [
      {
        id: 'revenue',
        type: 'line',
        path: 'value'
      }
    ];
    const statements = [{ value: 100 }, { value: 200 }, { value: null }];

    const result = prepareSeries({ selectedSeries, options, statements });
    expect(result[0].data).toEqual([100, 200, null]);
  });

  it('should handle missing or invalid options', () => {
    const selectedSeries = ['missing'];
    const options = [];
    const statements = [{ value: 100 }];

    const result = prepareSeries({ selectedSeries, options, statements });
    expect(result[0].type).toBeUndefined();
  });

  it('should handle invalid paths gracefully', () => {
    const selectedSeries = ['test'];
    const options = [
      {
        id: 'test',
        type: 'line',
        path: 'invalid.path'
      }
    ];
    const statements = [{ value: 100 }];

    const result = prepareSeries({ selectedSeries, options, statements });
    expect(result[0].data).toEqual([]);
  });

  it('should properly handle currency from statements', () => {
    const selectedSeries = ['test'];
    const options = [
      {
        id: 'test',
        type: 'bars',
        path: 'financial'
      }
    ];
    const statements = [
      {
        currency: 'USD',
        financial: {
          current_value: 100
        }
      },
      {
        currency: 'EUR',
        financial: {
          current_value: 200
        }
      }
    ];

    const result = prepareSeries({ selectedSeries, options, statements });
    expect(result[0].currency).toEqual('USD');
  });

  it('should return the prepared series', () => {
    const selectedSeries = ['test'];
    const options = [
      {
        id: 'test',
        type: 'bars',
        path: 'data_path'
      }
    ];
    const statements = [
      {
        data_path: {
          current_value: 1
        }
      },
      {
        data_path: {
          current_value: 2
        }
      },
      {
        data_path: {
          current_value: null
        }
      },
      {
        data_path: {
          current_value: -123,
          currency: 'EUR'
        }
      }
    ];

    expect(
      prepareSeries({
        selectedSeries,
        options,
        statements
      })
    ).toEqual([
      {
        currency: null,
        data: [
          {
            current_value: 1
          },
          {
            current_value: 2
          },
          {
            current_value: null
          },
          {
            current_value: -123,
            currency: 'EUR'
          }
        ],
        id: 'test',
        name: 'test',
        type: 'bars'
      }
    ]);
  });
});

describe('prepareAxis', () => {
  it('should return an empty array if nothing provided', () => {
    expect(prepareAxis()).toEqual([]);
  });

  it('should return the prepared axis', () => {
    const statements = [{ year: 2020 }, { year: 2021 }];
    expect(prepareAxis(statements)).toEqual([2020, 2021]);
  });
});

describe('formatter', () => {
  it('should return null if nothing provided', () => {
    expect(formatter()).toEqual('N/A');
  });

  it('should return N/A for null or undefined values', () => {
    expect(formatter(null)).toEqual('N/A');
    expect(formatter(undefined)).toEqual('N/A');
  });

  it('should format numbers with different notations', () => {
    expect(formatter(1234567, {}, 'en')).toEqual('1.2M');
    expect(formatter(-1234567, {}, 'en')).toEqual('-1.2M');
    expect(formatter(1234, {}, 'en')).toEqual('1.2K');
  });

  it('should format currency values with different locales', () => {
    const serie = {
      series: {
        currency: 'EUR'
      }
    };
    expect(formatter(1234.56, serie, 'en')).toEqual('€1.2K');
    expect(formatter(1234.56, serie, 'fr')).toEqual('1,2 k €');
  });

  it('should handle zero and small values', () => {
    expect(formatter(0, {}, 'en')).toEqual('0');
    expect(formatter(0.123, {}, 'en')).toEqual('0.1');
  });

  it('should return the formatted number', () => {
    const serie = {
      series: {
        currency: 'EUR'
      }
    };
    expect(formatter(-1204.4, serie, 'en')).toEqual('-€1.2K');
  });
});

describe('prepareOptions', () => {
  it('should return the default chart options', () => {
    const options = prepareOptions();
    delete options.legend.formatter;
    expect(options.chart).toEqual({
      fontFamily: 'Open Sans, sans-serif',
      redrawOnParentResize: true
    });
    expect(options.colors).toEqual([
      '#2E93fA',
      '#66DA26',
      '#546E7A',
      '#E91E63',
      '#FF9800'
    ]);
    expect(options.fill).toEqual({
      opacity: []
    });
    expect(options.legend).toEqual({
      horizontalAlign: 'left',
      offsetX: 40
    });
    expect(options.plotOptions).toEqual(
      {
        area: {
          fillTo: 'end'
        },
        bar: {
          columnWidth: '55%',
          endingShape: 'rounded',
          horizontal: false
        }
      },
      {}
    );
    expect(options.stroke).toEqual({
      width: 4
    });
    // In dynamic i18n, translation keys may not be loaded in test environment
    expect(options.title).toEqual({
      align: 'left',
      text: expect.any(String) // Could be 'Graphs' or 'graphs' depending on locale loading
    });
    expect(options.xaxis).toEqual({
      categories: [],
      title: {
        text: expect.any(String) // Could be 'Year' or 'year' depending on locale loading
      }
    });
    expect(options.yaxis).toEqual([]);
    expect(options.fill).toEqual({
      opacity: []
    });
    expect(options.stroke).toEqual({
      width: 4
    });
    expect(options.title).toEqual({
      align: 'left',
      text: expect.any(String) // Could be 'Graphs' or 'graphs' depending on locale loading
    });
  });

  it('should handle multiple series with different types', () => {
    const series = [
      {
        id: 'revenue',
        name: 'Revenue',
        type: 'bar',
        data: [100, 200, 300]
      },
      {
        id: 'profit',
        name: 'Profit',
        type: 'area',
        data: [50, 75, 100]
      }
    ];
    const categories = ['2020', '2021', '2022'];

    const result = prepareOptions(series, categories);

    expect(result.fill.opacity).toEqual([1, 0.5]);
    expect(result.yaxis[1].opposite).toBe(true);
    expect(result.xaxis.categories).toEqual(categories);
  });

  it('should configure chart appearance options correctly', () => {
    const series = [
      {
        id: 'test',
        name: 'Test',
        type: 'line',
        data: [1, 2, 3]
      }
    ];

    const result = prepareOptions(series, []);

    expect(result.chart.redrawOnParentResize).toBe(true);
    expect(result.chart.fontFamily).toBe('Open Sans, sans-serif');
    expect(result.plotOptions.bar.columnWidth).toBe('55%');
    expect(result.plotOptions.bar.endingShape).toBe('rounded');
    expect(result.stroke.width).toBe(4);
  });

  it('should return the chart options for a serie', () => {
    const series = [
      {
        currency: null,
        data: [1, 2, 3],
        id: 'test',
        name: 'test',
        type: 'area'
      },
      {
        currency: 'EUR',
        data: [4, 5, 6],
        id: 'test',
        name: 'test',
        type: 'bars'
      }
    ];
    const categories = ['area'];
    const preparedOptions = prepareOptions(series, categories);

    expect(preparedOptions.yaxis[0].seriesName).toEqual('test');
    expect(preparedOptions.yaxis[0].title).toEqual({
      text: 'financialsKeys.overview.test'
    });
  });
});
