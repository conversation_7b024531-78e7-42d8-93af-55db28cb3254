import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  loadGraphComponents,
  areGraphComponentsLoaded,
  getLoadedGraphComponents,
  preloadGraphComponents
} from '@/utils/graph-loader';

// Mock the dynamic imports at the top level
vi.mock('vis-network', () => ({
  Network: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    on: vi.fn(),
    setData: vi.fn()
  }))
}));

vi.mock('vis-data/peer', () => ({
  DataSet: vi.fn().mockImplementation((data) => ({
    add: vi.fn(),
    remove: vi.fn(),
    update: vi.fn(),
    get: vi.fn(() => data || [])
  })),
  DataView: vi.fn().mockImplementation((_dataset, _options) => ({
    refresh: vi.fn(),
    get: vi.fn(() => []),
    on: vi.fn()
  }))
}));

describe('graph-loader', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  describe('loadGraphComponents', () => {
    it('should successfully load graph components', async () => {
      const components = await loadGraphComponents();

      expect(components).toHaveProperty('Network');
      expect(components).toHaveProperty('DataSet');
      expect(components).toHaveProperty('DataView');
      expect(typeof components.Network).toBe('function');
      expect(typeof components.DataSet).toBe('function');
      expect(typeof components.DataView).toBe('function');
    });

    it('should return the same components on subsequent calls', async () => {
      const components1 = await loadGraphComponents();
      const components2 = await loadGraphComponents();

      expect(components1.Network).toBe(components2.Network);
      expect(components1.DataSet).toBe(components2.DataSet);
      expect(components1.DataView).toBe(components2.DataView);
    });
  });

  describe('areGraphComponentsLoaded', () => {
    it('should return true when components are loaded', async () => {
      await loadGraphComponents();
      expect(areGraphComponentsLoaded()).toBe(true);
    });
  });

  describe('getLoadedGraphComponents', () => {
    it('should return components when they are loaded', async () => {
      await loadGraphComponents();
      const components = getLoadedGraphComponents();

      expect(components).toHaveProperty('Network');
      expect(components).toHaveProperty('DataSet');
      expect(components).toHaveProperty('DataView');
    });

    it('should return the same instances as loadGraphComponents', async () => {
      const loadedComponents = await loadGraphComponents();
      const retrievedComponents = getLoadedGraphComponents();

      expect(retrievedComponents.Network).toBe(loadedComponents.Network);
      expect(retrievedComponents.DataSet).toBe(loadedComponents.DataSet);
      expect(retrievedComponents.DataView).toBe(loadedComponents.DataView);
    });
  });

  describe('preloadGraphComponents', () => {
    it('should not throw errors when called', () => {
      expect(() => preloadGraphComponents()).not.toThrow();
    });
  });

  describe('integration tests', () => {
    it('should work with realistic usage pattern', async () => {
      // Load components
      const components = await loadGraphComponents();
      expect(areGraphComponentsLoaded()).toBe(true);

      // Use components
      const retrievedComponents = getLoadedGraphComponents();
      expect(retrievedComponents).toEqual(components);

      // Create instances
      const network = new components.Network();
      const dataSet = new components.DataSet([]);
      const dataView = new components.DataView(dataSet);

      expect(network).toBeDefined();
      expect(dataSet).toBeDefined();
      expect(dataView).toBeDefined();
    });
  });
});
