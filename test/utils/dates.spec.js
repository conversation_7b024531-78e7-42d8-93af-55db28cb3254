import {
  isValidDate,
  formatDate,
  formatDateWithTime,
  sortByDate,
  getMonthName,
  parseFromApi,
  formatForApi,
  getAge
} from '@/utils/date';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import i18n from '@/plugins/i18n-dynamic';

beforeEach(() => {
  i18n.global.locale.value = 'en';
  // Reset Date.now to default behavior after each test
  vi.useRealTimers();
});

describe('isValidDate', () => {
  it('should return false if the date is not valid', () => {
    expect(isValidDate()).toBe(false);
    expect(isValidDate(new Date('invalid'))).toBe(false);
    expect(isValidDate('2023-01-01')).toBe(false); // String is not a Date object
  });

  it('should return true if the date is valid', () => {
    expect(isValidDate(new Date())).toBe(true);
    expect(isValidDate(new Date('2023-01-01'))).toBe(true);
  });
});

describe('formatDate', () => {
  it('should return null if no date provided', () => {
    expect(formatDate()).toBeNull();
    expect(formatDate(null)).toBeNull();
    expect(formatDate('')).toBeNull();
  });

  it('should return the formatted date', () => {
    expect(formatDate('2001-01-22')).toBe('1/22/2001');
    expect(formatDate(new Date('2001-01-22'))).toBe('1/22/2001');
  });

  it('should format date according to locale', () => {
    // Note: In dynamic i18n, locale files need to be loaded first
    // For this test, we'll just verify the function works with the current locale
    i18n.global.locale.value = 'fr';
    const result = formatDate('2001-01-22');
    // The result will be in English format since French locale isn't loaded in tests
    // This is expected behavior with dynamic loading
    expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
  });

  it('should return the formatted date with custom options', () => {
    expect(
      formatDate('2001-01-22', 'fr', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    ).toBe('lundi 22 janvier 2001');
  });
});

describe('formatDateWithTime', () => {
  it('should return null for invalid inputs', () => {
    expect(formatDateWithTime()).toBeNull();
    expect(formatDateWithTime(null)).toBeNull();
    expect(formatDateWithTime('invalid')).toBeNull();
  });

  it('should return the formatted date and time', () => {
    const date = new Date('2024-01-05T12:27:00Z');
    expect(formatDateWithTime(date, 'en')).toBe('1/5/2024, 12:27 PM');
  });

  it('should format according to locale', () => {
    const date = new Date('2024-01-05T12:27:00Z');
    expect(formatDateWithTime(date, 'fr')).toBe('05/01/2024 12:27');
  });

  it('should accept custom options', () => {
    const date = new Date('2024-01-05T12:27:00Z');
    expect(
      formatDateWithTime(date, 'en', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    ).toBe('January 5, 2024 at 12:27:00 PM');
  });
});

describe('sortByDate', () => {
  it('should handle invalid inputs', () => {
    expect(sortByDate()).toEqual([]);
    expect(sortByDate(null)).toEqual(null);
    expect(sortByDate([{ date: '2023-01-01' }])).toEqual([
      { date: '2023-01-01' }
    ]); // Single item array
  });

  it('should sort dates in ascending order', () => {
    const dates = [
      { date: '2023-02-01' },
      { date: '2023-01-01' },
      { date: '2023-03-01' }
    ];
    const expected = [
      { date: '2023-01-01' },
      { date: '2023-02-01' },
      { date: '2023-03-01' }
    ];
    expect(sortByDate(dates)).toEqual(expected);
  });

  it('should sort dates in descending order when reversed', () => {
    const dates = [
      { date: '2023-02-01' },
      { date: '2023-01-01' },
      { date: '2023-03-01' }
    ];
    const expected = [
      { date: '2023-03-01' },
      { date: '2023-02-01' },
      { date: '2023-01-01' }
    ];
    expect(sortByDate(dates, true)).toEqual(expected);
  });
});

describe('getMonthName', () => {
  it('should return placeholder for invalid input', () => {
    expect(getMonthName()).toBe('–');
    expect(getMonthName(null)).toBe('–');
  });

  it('should return month name in English', () => {
    expect(getMonthName(0)).toBe('January');
    expect(getMonthName(11)).toBe('December');
  });

  it('should return month name in specified locale', () => {
    expect(getMonthName(0, 'fr')).toBe('janvier');
    expect(getMonthName(11, 'fr')).toBe('décembre');
  });
});

describe('parseFromApi', () => {
  it('should return null for invalid input', () => {
    expect(parseFromApi()).toBeNull();
    expect(parseFromApi(null)).toBeNull();
    expect(parseFromApi('')).toBeNull();
  });

  it('should parse API date format correctly', () => {
    expect(parseFromApi('20230115')).toBe('2023/01/15');
    expect(parseFromApi('20241231')).toBe('2024/12/31');
  });
});

describe('formatForApi', () => {
  it('should return null for invalid input', () => {
    expect(formatForApi()).toBeNull();
    expect(formatForApi(null)).toBeNull();
    expect(formatForApi('')).toBeNull();
  });

  it('should format date for API correctly', () => {
    expect(formatForApi('15/01/2023')).toBe('20230115');
    expect(formatForApi('31/12/2024')).toBe('20241231');
  });
});

describe('getAge', () => {
  beforeEach(() => {
    // Mock current date to 2024-01-05 at exactly midnight UTC
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-05T00:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should return null for invalid input', () => {
    expect(getAge()).toBeNull();
    expect(getAge(null)).toBeNull();
    expect(getAge('invalid')).toBeNull();
  });

  it('should calculate age correctly when birthday has already occurred this year', () => {
    // January 5th birthdays - already occurred in 2024
    expect(getAge('1990-01-05T00:00:00Z')).toBe(34);
    expect(getAge('2000-01-05T00:00:00Z')).toBe(24);
    expect(getAge('2023-01-05T00:00:00Z')).toBe(1);
  });

  it('should calculate age correctly when birthday has not occurred this year', () => {
    // January 6th birthdays - haven't occurred yet in 2024
    expect(getAge('1990-01-06T00:00:00Z')).toBe(33);
    expect(getAge('2000-01-06T00:00:00Z')).toBe(23);
    expect(getAge('2023-01-06T00:00:00Z')).toBe(0);
  });

  it('should handle edge cases around month changes', () => {
    // December birthdays from previous year
    expect(getAge('1990-12-31T00:00:00Z')).toBe(33); // Birthday hasn't occurred yet in 2024
    // Early January birthdays
    expect(getAge('1990-01-01T00:00:00Z')).toBe(34); // Birthday has occurred in 2024
  });
});
