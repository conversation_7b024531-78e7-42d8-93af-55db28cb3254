import { describe, it, expect, vi, beforeEach } from 'vitest';
import { config, mount } from '@vue/test-utils';
import { Quasar, Notify } from 'quasar';
import quasarOptions from '@/plugins/quasar';
import { notify, notifyError } from '@/utils/notifications';
import i18n from '@/plugins/i18n-dynamic';

vi.mock(import('quasar'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    Notify: {
      create: vi.fn()
    }
  };
});

beforeEach(() => {
  // Quasar is needed to use Notify plugin in the notification util
  config.plugins.VueWrapper.install(() => Quasar, quasarOptions);
  mount({ template: '<div></div>' });
  // Clear mock calls between tests
  vi.clearAllMocks();
});

describe('notify', () => {
  it('should handle empty/undefined parameters', () => {
    const result = notify();
    expect(result).toEqual({ message: undefined, type: undefined });
    expect(Notify.create).toHaveBeenCalledWith({
      message: undefined,
      type: undefined
    });
  });

  it('should create notification with basic message and type', () => {
    const result = notify({
      message: 'Test message',
      type: 'positive'
    });

    expect(result).toEqual({
      message: 'Test message',
      type: 'positive'
    });
    expect(Notify.create).toHaveBeenCalledWith({
      message: 'Test message',
      type: 'positive'
    });
  });

  it('should handle callback with action', () => {
    const callback = vi.fn();
    const result = notify({
      message: 'Test with callback',
      type: 'warning',
      callback
    });

    expect(result.actions).toBeDefined();
    expect(result.actions[0].label).toBe('close');
    expect(result.actions[0].color).toBe('white');

    // Test callback execution
    result.actions[0].handler();
    expect(callback).toHaveBeenCalledTimes(1);

    expect(Notify.create).toHaveBeenCalledWith({
      message: 'Test with callback',
      type: 'warning',
      actions: result.actions
    });
  });
});

describe('notifyError', () => {
  beforeEach(() => {
    vi.spyOn(i18n.global, 't').mockImplementation((key) => key);
  });

  it('should handle error with message in data object', () => {
    const errorData = {
      error: {
        message: 'API Error Message'
      }
    };

    notifyError(errorData);

    expect(Notify.create).toHaveBeenCalledWith({
      message: 'API Error Message',
      type: 'negative'
    });
  });

  it('should use provided fallback message when no error in data', () => {
    notifyError(null, 'Fallback Error Message');

    expect(Notify.create).toHaveBeenCalledWith({
      message: 'Fallback Error Message',
      type: 'negative'
    });
  });

  it('should use generic error message when no message provided', () => {
    notifyError();

    expect(Notify.create).toHaveBeenCalledWith({
      message: 'genericError',
      type: 'negative'
    });
  });

  it('should handle undefined data with no fallback', () => {
    notifyError(undefined);

    expect(Notify.create).toHaveBeenCalledWith({
      message: 'genericError',
      type: 'negative'
    });
  });

  it('should handle data object without error message', () => {
    const data = { someOtherField: 'value' };
    const fallbackMessage = 'Custom Error Message';

    notifyError(data, fallbackMessage);

    expect(Notify.create).toHaveBeenCalledWith({
      message: fallbackMessage,
      type: 'negative'
    });
  });
});
