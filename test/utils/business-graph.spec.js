import { describe, it, expect, vi } from 'vitest';
import formatGraph from '@/utils/formatters/business-graph';

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getJuridicalFormCodes: vi.fn(() =>
    Promise.resolve([
      {
        100: [{ nl: { code: '100', label: 'Private limited company' } }]
      }
    ])
  ),
  getJuridicalSituationCodes: vi.fn(() =>
    Promise.resolve([
      {
        '000': [{ nl: { code: '000', label: 'Normal situation' } }]
      }
    ])
  )
}));

// Mock date formatter
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => {
    if (date === '2020-01-01') return '1/1/2020';
    if (date === '1980-01-01') return '1/1/1980';
    return date;
  })
}));

describe('formatGraph', () => {
  it('should return null if no graph data provided', async () => {
    expect(await formatGraph()).toEqual(null);
  });

  it('should return basic graph with empty data', async () => {
    const graphData = { nodes: [] };
    expect(await formatGraph(graphData)).toEqual({ edges: [], nodes: [] });
  });

  it('should format company node with complete data', async () => {
    const graphData = {
      nodes: [
        {
          type: 'company',
          id: 'COMPANY_001',
          level: 0,
          main: true,
          external: false,
          label: 'Example Corp',
          sources: ['BE_AAA_JS', 'BE_BBB_JSON', 'BE_CCC_TXT', 'BE_DDD_PDF'],
          extra_information: {
            company_form: '100',
            company_name: 'Example Corp',
            company_situation: '000',
            company_employees: '25',
            company_address: '123 Main Street, 1000 City, Country',
            company_start: '2020-01-01',
            company_turnover: '1000000'
          },
          has_active_edge: true
        }
      ],
      edges: [
        {
          type: 'address',
          id: 'edge_001',
          from: 'COMPANY_001',
          to: 'address|1000|city|main street|123',
          label: '123 Main Street, 1000 City, Country',
          external: false,
          start_date: '2020-01-01',
          end_date: '2023-12-31',
          first_appearance: '2020-01-02',
          last_appearance: '2023-12-31',
          sources: ['BE_AAA_JS', 'BE_BBB_JSON', 'BE_CCC_TXT', 'BE_DDD_PDF']
        }
      ]
    };

    const result = await formatGraph(graphData);
    expect(result.nodes[0]).toEqual(
      expect.objectContaining({
        id: 'COMPANY_001',
        label: '<i></i><i> </i> Example Corp',
        type: 'company',
        icon: 'fa-regular fa-building',
        group: 'company-main',
        isActive: true,
        isExternal: false,
        isMain: true,
        originalLabel: 'Example Corp',
        sources: ['BE_AAA', 'BE_BBB', 'BE_CCC', 'BE_DDD'],
        infos: [
          { label: 'juridicalForm', value: 'Private limited company' },
          { label: 'juridicalSituation', value: 'Normal situation' },
          { label: 'employees', value: '25' },
          { label: 'address', value: '123 Main Street, 1000 City, Country' },
          { label: 'startDate', value: '1/1/2020' },
          { label: 'turnover', value: '1000000' }
        ]
      })
    );

    // Verify infos array contains all expected information
    const infos = result.nodes[0].infos;
    expect(infos).toContainEqual({
      label: 'juridicalForm',
      value: 'Private limited company'
    });
    expect(infos).toContainEqual({
      label: 'juridicalSituation',
      value: 'Normal situation'
    });
    expect(infos).toContainEqual({ label: 'employees', value: '25' });
    expect(infos).toContainEqual({
      label: 'address',
      value: '123 Main Street, 1000 City, Country'
    });
    expect(infos).toContainEqual({ label: 'startDate', value: '1/1/2020' });
    expect(infos).toContainEqual({ label: 'turnover', value: '1000000' });
  });

  it('should format person node correctly', async () => {
    const graphData = {
      nodes: [
        {
          type: 'person',
          id: 'PERSON_001',
          level: 0,
          main: false,
          external: false,
          label: 'John Doe',
          has_active_edge: true,
          sources: ['BE_AAA_JS'],
          extra_information: {
            person_address: '123 Main Street, 1000 City, Country',
            person_birthdate: '1980-01-01',
            person_companies: 'COMP1,COMP2',
            person_uuids: ['uuid1', 'uuid2']
          }
        }
      ],
      edges: []
    };

    const result = await formatGraph(graphData);
    expect(result.nodes[0]).toEqual(
      expect.objectContaining({
        id: 'PERSON_001',
        label: '<i></i><i> </i> John Doe',
        type: 'person',
        icon: 'fa-regular fa-user',
        group: 'person-active',
        isActive: true,
        isExternal: false,
        isMain: false,
        originalLabel: 'John Doe',
        personUUIDs: ['uuid1', 'uuid2'],
        sources: ['BE_AAA']
      })
    );

    // Verify person-specific infos
    const infos = result.nodes[0].infos;
    expect(infos).toContainEqual({
      label: 'address',
      value: '123 Main Street, 1000 City, Country'
    });
    expect(infos).toContainEqual({ label: 'birthdate', value: '1/1/1980' });
    expect(infos).toContainEqual({
      label: 'companies',
      value: expect.arrayContaining([
        { id: 'COMP1', name: 'COMP1' },
        { id: 'COMP2', name: 'COMP2' }
      ])
    });
  });

  it('should handle external nodes and edges correctly', async () => {
    const graphData = {
      nodes: [
        {
          type: 'company',
          id: 'EXT_COMP_001',
          level: 0,
          main: false,
          external: true,
          label: 'External Corp',
          has_active_edge: true,
          sources: ['BE_AAA_JS'],
          extra_information: {}
        }
      ],
      edges: [
        {
          type: 'address',
          id: 'edge_001',
          from: 'COMPANY_001',
          to: 'EXT_COMP_001',
          label: 'External Connection',
          external: true,
          reversed: true
        }
      ]
    };

    const result = await formatGraph(graphData);

    // Verify external node formatting
    expect(result.nodes[0]).toEqual(
      expect.objectContaining({
        id: 'EXT_COMP_001',
        icon: 'fa-regular fa-building',
        infos: [],
        isActive: true,
        isMain: false,
        label: '<i></i><i> </i> External Corp',
        originalLabel: 'External Corp',
        isExternal: true,
        group: 'company-active',
        sources: ['BE_AAA'],
        type: 'company'
      })
    );

    // Verify external edge formatting
    expect(result.edges[0]).toEqual(
      expect.objectContaining({
        dashes: true,
        length: 400,
        arrows: {
          from: true,
          to: false
        }
      })
    );
  });

  it('should handle inactive nodes correctly', async () => {
    const graphData = {
      nodes: [
        {
          type: 'company',
          id: 'INACTIVE_001',
          level: 0,
          main: false,
          external: false,
          label: 'Inactive Corp',
          has_active_edge: false,
          sources: ['BE_AAA_JS'],
          extra_information: {}
        }
      ],
      edges: []
    };

    const result = await formatGraph(graphData);
    expect(result.nodes[0]).toEqual(
      expect.objectContaining({
        id: 'INACTIVE_001',
        group: 'company-inactive',
        isActive: false
      })
    );
  });

  it('should handle address nodes correctly', async () => {
    const graphData = {
      nodes: [
        {
          type: 'address',
          id: 'ADDR_001',
          level: 0,
          main: false,
          external: false,
          label: '123 Main Street',
          has_active_edge: true,
          sources: ['BE_AAA_JS'],
          extra_information: {}
        }
      ],
      edges: []
    };

    const result = await formatGraph(graphData);
    expect(result.nodes[0]).toEqual(
      expect.objectContaining({
        id: 'ADDR_001',
        type: 'address',
        icon: 'fa-regular fa-location-dot',
        group: 'address-active'
      })
    );
  });

  it('should handle duplicate sources correctly', async () => {
    const graphData = {
      nodes: [
        {
          type: 'company',
          id: 'COMPANY_001',
          label: 'Example Corp',
          sources: ['BE_AAA_JS', 'BE_AAA_PDF', 'BE_AAA_JS'],
          extra_information: {},
          has_active_edge: true
        }
      ],
      edges: []
    };

    const result = await formatGraph(graphData);
    expect(result.nodes[0].sources).toEqual(['BE_AAA']);
  });
});
