import { describe, it, expect, vi } from 'vitest';
import { formatCompanyEvents } from '@/utils/formatters/company-events';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => {
    const [year, month, day] = date.split('-');
    return `${month}/${day}/${year}`;
  })
}));

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code, source) => {
    const mockCodes = {
      62010: 'Computer programming activities',
      70220: 'Business consulting',
      '000': 'Normal situation',
      '015': 'Dissolution',
      100: 'Private limited company',
      200: 'Public limited company'
    };
    return mockCodes[code];
  })
}));

// Mock the activities code utility
vi.mock('@/utils/activities-code', () => ({
  getActivitiesCode: vi.fn(() =>
    Promise.resolve({
      62010: { code: '62010', description: 'Computer programming activities' },
      70220: { code: '70220', description: 'Business consulting' }
    })
  )
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getJuridicalFormCodes: vi.fn(() =>
    Promise.resolve([
      {
        100: [{ nl: { code: '100', label: 'Private limited company' } }]
      },
      {
        200: [{ nl: { code: '200', label: 'Public limited company' } }]
      }
    ])
  ),
  getJuridicalSituationCodes: vi.fn(() =>
    Promise.resolve([
      {
        '000': [{ nl: { code: '000', label: 'Normal situation' } }]
      },
      {
        '001': [{ nl: { code: '001', label: 'Legal incorporation' } }]
      }
    ])
  )
}));

describe('formatCompanyEvents', () => {
  it('should return null if no events provided', async () => {
    expect(await formatCompanyEvents()).toBe(null);
    expect(await formatCompanyEvents(null)).toBe(null);
  });

  it('should format single event correctly', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: {
          from: 'Old Corp',
          to: 'New Corp'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result).toEqual({
      events: [
        {
          year: '2023',
          count: 1,
          events: [
            {
              identifier: 'EVENT_001',
              date: '01/15/2023',
              severity: 'high',
              type: 'COMPANY_NAME_CHANGE',
              from: 'Old Corp',
              to: 'New Corp'
            }
          ]
        }
      ],
      max: 1
    });
  });

  it('should handle multiple events in same year', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: {
          from: 'Old Corp',
          to: 'New Corp'
        }
      },
      {
        identifier: 'EVENT_002',
        date: '2023-06-20',
        event_severity: 'medium',
        event_type: 'ADDRESS_CHANGE',
        diff: {
          from: '123 Old St',
          to: '456 New St'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events[0]).toEqual({
      year: '2023',
      count: 2,
      events: expect.arrayContaining([
        {
          identifier: 'EVENT_001',
          date: '01/15/2023',
          severity: 'high',
          type: 'COMPANY_NAME_CHANGE',
          from: 'Old Corp',
          to: 'New Corp'
        },
        {
          identifier: 'EVENT_002',
          date: '06/20/2023',
          severity: 'medium',
          type: 'ADDRESS_CHANGE',
          from: '123 Old St',
          to: '456 New St'
        }
      ])
    });
    expect(result.max).toBe(2);
  });

  it('should handle events across multiple years', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: {
          from: 'Old Corp',
          to: 'New Corp'
        }
      },
      {
        identifier: 'EVENT_002',
        date: '2022-06-20',
        event_severity: 'medium',
        event_type: 'ADDRESS_CHANGE',
        diff: {
          from: '123 Old St',
          to: '456 New St'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events).toHaveLength(2);
    expect(result.events[0].year).toBe('2023');
    expect(result.events[1].year).toBe('2022');
    expect(result.max).toBe(1);
  });

  it('should format NACE codes with descriptions', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NACE_CODES',
        diff: {
          from: '62010',
          to: '70220'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events[0].events[0]).toEqual({
      identifier: 'EVENT_001',
      date: '01/15/2023',
      severity: 'high',
      type: 'COMPANY_NACE_CODES',
      from: 'Computer programming activities (62010)',
      to: 'Business consulting (70220)'
    });
  });

  it('should format legal situation changes with descriptions', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_LEGAL_SITUATION',
        diff: {
          from: '000',
          to: '015'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events[0].events[0]).toEqual({
      identifier: 'EVENT_001',
      date: '01/15/2023',
      severity: 'high',
      type: 'COMPANY_LEGAL_SITUATION',
      from: 'Normal situation (000)',
      to: 'Dissolution (015)'
    });
  });

  it('should format legal form changes with descriptions', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_LEGAL_FORM',
        diff: {
          from: '100',
          to: '200'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events[0].events[0]).toEqual({
      identifier: 'EVENT_001',
      date: '01/15/2023',
      severity: 'high',
      type: 'COMPANY_LEGAL_FORM',
      from: 'Private limited company (100)',
      to: 'Public limited company (200)'
    });
  });

  it('should handle unknown codes gracefully', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_LEGAL_FORM',
        diff: {
          from: '999',
          to: '888'
        }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events[0].events[0]).toEqual({
      identifier: 'EVENT_001',
      date: '01/15/2023',
      severity: 'high',
      type: 'COMPANY_LEGAL_FORM',
      from: '999',
      to: '888'
    });
  });

  it('should sort events by year in descending order', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2021-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'A', to: 'B' }
      },
      {
        identifier: 'EVENT_002',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'B', to: 'C' }
      },
      {
        identifier: 'EVENT_003',
        date: '2022-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'C', to: 'D' }
      }
    ];

    const result = await formatCompanyEvents(events);
    expect(result.events.map((e) => e.year)).toEqual(['2023', '2022', '2021']);
  });

  it('should sort events by date in descending order', async () => {
    const events = [
      {
        identifier: 'EVENT_001',
        date: '2021-01-01',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'A', to: 'B' }
      },
      {
        identifier: 'EVENT_002',
        date: '2021-01-03',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'B', to: 'C' }
      },
      {
        identifier: 'EVENT_003',
        date: '2021-01-02',
        event_severity: 'high',
        event_type: 'COMPANY_NAME_CHANGE',
        diff: { from: 'C', to: 'D' }
      }
    ];

    const result = await formatCompanyEvents(events);

    expect(result.events[0].events[0].date).toEqual('01/03/2021');
    expect(result.events[0].events[1].date).toEqual('01/02/2021');
    expect(result.events[0].events[2].date).toEqual('01/01/2021');
  });

  it('should handle activities code loading errors gracefully', async () => {
    // Mock getActivitiesCode to throw an error
    const { getActivitiesCode } = await import('@/utils/activities-code');
    vi.mocked(getActivitiesCode).mockRejectedValueOnce(
      new Error('Failed to load activities')
    );

    const events = [
      {
        identifier: 'EVENT_001',
        date: '2023-01-15',
        event_severity: 'high',
        event_type: 'COMPANY_NACE_CODES',
        diff: {
          from: '62010',
          to: '70220'
        }
      }
    ];

    // Should throw an error when activities code can't be loaded
    await expect(formatCompanyEvents(events)).rejects.toThrow(
      'Failed to load activities'
    );
  });
});
