import formatAlert from '@/utils/formatters/alert';
import { describe, it, expect, beforeEach } from 'vitest';
import alert from '../../__mocks__/alert.json';
import i18n from '@/plugins/i18n-dynamic';

beforeEach(() => {
  i18n.global.locale.value = 'fr';
});

describe('formatAlert', () => {
  it('should return null if no alert provided', () => {
    expect(formatAlert()).toEqual(null);
  });

  it('should return the formatted alert', () => {
    expect(formatAlert({})).toEqual({
      comments: [],
      auditTrails: [],
      assignations: [],
      eventType: '',
      severity: ''
    });

    expect(formatAlert(alert)).toEqual({
      alertShortId: 'alertshortid-example',
      watchlistShortId: 'watchlist-shortid-example',
      eventShortId: 'event-shortid-example',
      alertLastStatus: 'CREATED',
      comments: [
        {
          commentShortId: '123',
          customerShortId: '456',
          comment: 'test <b>bold</b> <i>italic</i>,<div>line break</div>',
          commentDate: '29/11/2023 13:07'
        },
        {
          commentShortId: '456',
          customerShortId: '789',
          comment:
            'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
          commentDate: '29/11/2023 13:07'
        }
      ],
      updatedAt: '2023-11-29T13:07:24.998Z',
      createdAt: '2023-11-29T13:07:24.998Z',
      eventType: '',
      severity: '',
      assignations: [],
      auditTrails: [],
      companyIdentifier: 'BE_0403045985'
    });
  });
});
