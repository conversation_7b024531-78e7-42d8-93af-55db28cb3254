import { describe, it, expect, vi } from 'vitest';
import { formatSearchPerson, formatPerson } from '@/utils/formatters/person';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => `formatted_${date}`),
  getAge: vi.fn(() => 42)
}));

vi.mock('@/utils/avatar', () => ({
  getAvatarColor: vi.fn((id) => `color_${id}`)
}));

vi.mock('@/utils/companies/addresses', () => ({
  prepareAllAddresses: vi.fn((addresses) => addresses)
}));

describe('formatSearchPerson', () => {
  it('should return null for null input', () => {
    expect(formatSearchPerson(null)).toBe(null);
  });

  it('should return null for undefined input', () => {
    expect(formatSearchPerson(undefined)).toBe(null);
  });

  it('should format person with primary address', () => {
    const input = {
      identifier: 'PERSON1',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      full_name: '<PERSON>',
      title: 'CEO',
      birth_date: '1980-01-01',
      last_appearance: '2023-01-01',
      company_identifiers: ['COMP1', 'COMP2'],
      addresses: [
        {
          primary: true,
          postal_code: '12345',
          locality: 'City',
          country: 'country'
        },
        {
          primary: false,
          postal_code: '67890',
          locality: 'OtherCity',
          country: 'othercountry'
        }
      ]
    };

    const result = formatSearchPerson(input);

    expect(result).toEqual({
      id: 'PERSON1',
      initials: 'JD',
      color: 'color_PERSON1',
      fullName: 'John Doe',
      lastAppearance: 'formatted_2023-01-01',
      title: 'CEO',
      birthDate: 'formatted_1980-01-01',
      companiesIdentifiers: ['COMP1', 'COMP2'],
      age: 42,
      companiesCount: 2,
      address: '12345, City, COUNTRY'
    });
  });

  it('should format person without primary address', () => {
    const input = {
      identifier: 'PERSON1',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      title: 'CEO',
      birth_date: '1980-01-01',
      last_appearance: '2023-01-01',
      company_identifiers: ['COMP1'],
      addresses: [
        {
          primary: false,
          postal_code: '67890',
          locality: 'City',
          country: 'country'
        }
      ]
    };

    const result = formatSearchPerson(input);

    expect(result).toEqual({
      id: 'PERSON1',
      initials: 'JD',
      color: 'color_PERSON1',
      fullName: 'John Doe',
      lastAppearance: 'formatted_2023-01-01',
      title: 'CEO',
      birthDate: 'formatted_1980-01-01',
      companiesIdentifiers: ['COMP1'],
      age: 42,
      companiesCount: 1,
      address: null
    });
  });

  it('should handle empty addresses array', () => {
    const input = {
      identifier: 'PERSON1',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      title: 'CEO',
      birth_date: '1980-01-01',
      last_appearance: '2023-01-01',
      company_identifiers: [],
      addresses: []
    };

    const result = formatSearchPerson(input);

    expect(result).toEqual({
      id: 'PERSON1',
      initials: 'JD',
      color: 'color_PERSON1',
      fullName: 'John Doe',
      lastAppearance: 'formatted_2023-01-01',
      title: 'CEO',
      birthDate: 'formatted_1980-01-01',
      companiesIdentifiers: [],
      age: 42,
      companiesCount: 0,
      address: null
    });
  });
});

describe('formatPerson', () => {
  it('should return null for null input', () => {
    expect(formatPerson(null)).toBe(null);
  });

  it('should return null for undefined input', () => {
    expect(formatPerson(undefined)).toBe(null);
  });

  it('should format complete person data', () => {
    const input = {
      identifier: 'PERSON1',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      title: 'CEO',
      gender: 'M',
      birth_date: '1980-01-01',
      first_appearance: '2020-01-01',
      last_appearance: '2023-01-01',
      company_identifiers: ['COMP1', 'COMP2'],
      addresses: [
        {
          primary: true,
          postal_code: '12345',
          locality: 'City',
          country: 'country'
        }
      ],
      profession: 'Executive',
      phone_number: '+1234567890',
      fax_number: '+0987654321',
      email: '<EMAIL>',
      website: 'www.example.com',
      is_or_was_independent: true
    };

    const result = formatPerson(input);

    expect(result).toEqual({
      id: 'PERSON1',
      initials: 'JD',
      identifier: 'john_doe',
      color: 'color_PERSON1',
      fullName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      firstAppearance: 'formatted_2020-01-01',
      lastAppearance: 'formatted_2023-01-01',
      title: 'CEO',
      gender: 'M',
      birthDate: 'formatted_1980-01-01',
      age: 42,
      companiesCount: 2,
      companiesIdentifiers: ['COMP1', 'COMP2'],
      addresses: [
        {
          primary: true,
          postal_code: '12345',
          locality: 'City',
          country: 'country'
        }
      ],
      profession: 'Executive',
      phone: '+1234567890',
      fax: '+0987654321',
      email: '<EMAIL>',
      website: 'www.example.com',
      isOrWasIndependent: true
    });
  });

  it('should handle minimal person data', () => {
    const input = {
      identifier: 'PERSON1',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      birth_date: '1980-01-01',
      first_appearance: '2020-01-01',
      last_appearance: '2023-01-01',
      company_identifiers: [],
      addresses: []
    };

    const result = formatPerson(input);

    expect(result).toEqual({
      id: 'PERSON1',
      initials: 'JD',
      identifier: 'john_doe',
      color: 'color_PERSON1',
      fullName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      firstAppearance: 'formatted_2020-01-01',
      lastAppearance: 'formatted_2023-01-01',
      title: undefined,
      gender: undefined,
      birthDate: 'formatted_1980-01-01',
      age: 42,
      companiesCount: 0,
      companiesIdentifiers: [],
      addresses: [],
      profession: undefined,
      phone: undefined,
      fax: undefined,
      email: undefined,
      website: undefined,
      isOrWasIndependent: undefined
    });
  });
});
