import { vi, describe, it, expect, beforeAll } from 'vitest';
import { formatCompany, formatAutocomplete } from '@/utils/formatters/company';

// Mock dependencies
vi.mock('@/utils/avatar', () => ({
  getAvatarColor: vi.fn(() => 'hsl(120, 50%, 50%)'),
  getInitials: vi.fn(() => 'TC')
}));

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code) => `Mock ${code}`)
}));

vi.mock('@/utils/companies/addresses', () => ({
  prepareAllAddresses: vi.fn(() => []),
  prepareAddresses: vi.fn(() => [])
}));

vi.mock('@/utils/companies/activities', () => ({
  findMainActivity: vi.fn(() => Promise.resolve(null)),
  prepareActivities: vi.fn(() => Promise.resolve([]))
}));

vi.mock('@/utils/companies/characteristics', () => ({
  prepareCharacteristics: vi.fn(() => [])
}));

vi.mock('@/utils/companies/authorizations', () => ({
  prepareAuthorizations: vi.fn(() => Promise.resolve([]))
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getJuridicalFormCodes: vi.fn(() =>
    Promise.resolve([
      {
        LLC: [{ nl: { code: 'LLC', label: 'Limited Liability Company' } }]
      }
    ])
  ),
  getJuridicalSituationCodes: vi.fn(() =>
    Promise.resolve([
      {
        ACTIVE: [{ nl: { code: 'ACTIVE', label: 'Active situation' } }]
      }
    ])
  )
}));

beforeAll(() => {
  const date = new Date(2024, 1, 1);
  vi.useFakeTimers();
  vi.setSystemTime(date);
});

describe('formatCompany', () => {
  const mockBasicCompany = {
    identifier: 'BE_0123456789',
    company_name: 'Test Company',
    vat_formatted: 'BE 0123.456.789',
    vat_clean: '0123456789',
    active: true,
    language: 'en',
    start_date: '2013-01-01'
  };

  it('should return null if no company provided', async () => {
    expect(await formatCompany()).toEqual(null);
    expect(await formatCompany(null)).toEqual(null);
    expect(await formatCompany(undefined)).toEqual(null);
  });

  it('should format basic company information', async () => {
    const result = await formatCompany(mockBasicCompany);

    expect(result).toEqual({
      active: true,
      age: '11 years ago',
      color: 'hsl(120, 50%, 50%)',
      formattedName: 'BE 0123.456.789 - Test Company',
      id: 'BE_0123456789',
      identifier: 'BE_0123456789',
      initials: 'TC',
      isActive: true,
      language: 'en',
      name: 'Test Company',
      vat: 'BE_0123456789',
      vatFormatted: 'BE 0123.456.789'
    });
  });

  it('should handle company with abbreviation', async () => {
    const companyWithAbbreviation = {
      ...mockBasicCompany,
      company_names: [
        { code: 'ABB', name: 'TCA' },
        { code: 'OTH', name: 'Other Name' }
      ]
    };

    const result = await formatCompany(companyWithAbbreviation, true);
    expect(result.abbreviation).toBe('TCA');
  });

  it('should handle company with legal person type', async () => {
    const companyWithLegalType = {
      ...mockBasicCompany,
      legal_person_type: {
        code: 'CORP'
      }
    };

    const result = await formatCompany(companyWithLegalType, true);
    expect(result.legalPersonType).toBe('Mock CORP');
  });

  it('should handle company with juridical form', async () => {
    const companyWithJuridicalForm = {
      ...mockBasicCompany,
      juridical_form: {
        code: 'LLC'
      }
    };

    const result = await formatCompany(companyWithJuridicalForm, true);
    expect(result.juridicalForm).toBe('Mock LLC');
  });

  it('should handle company with juridical situation', async () => {
    const companyWithJuridicalSituation = {
      ...mockBasicCompany,
      juridical_situation: {
        code: 'ACTIVE'
      }
    };

    const result = await formatCompany(companyWithJuridicalSituation, true);
    expect(result.juridicalSituation).toBe('Mock ACTIVE');
  });

  it('should handle inactive company', async () => {
    const inactiveCompany = {
      ...mockBasicCompany,
      active: false
    };

    const result = await formatCompany(inactiveCompany);
    expect(result.active).toBe(false);
    expect(result.isActive).toBe(false);
  });

  it('should handle company without creation date', async () => {
    const companyWithoutDate = {
      ...mockBasicCompany,
      start_date: null
    };

    const result = await formatCompany(companyWithoutDate);
    expect(result.age).toBeNull();
  });

  it('should format complete company information', async () => {
    const completeCompany = {
      ...mockBasicCompany,
      company_names: [{ code: 'ABB', name: 'TCA' }],
      legal_person_type: { code: 'CORP' },
      juridical_form: { code: 'LLC' },
      juridical_situation: { code: 'ACTIVE' },
      addresses: [],
      activities: [],
      characteristics: [],
      authorizations: []
    };

    const result = await formatCompany(completeCompany, true);

    expect(result).toMatchObject({
      abbreviation: 'TCA',
      legalPersonType: 'Mock CORP',
      juridicalForm: 'Mock LLC',
      juridicalSituation: 'Mock ACTIVE',
      addresses: [],
      activities: [],
      characteristics: [],
      authorizations: []
    });
  });
});

describe('formatAutocomplete', () => {
  it('should return null if no company provided', async () => {
    expect(await formatAutocomplete()).toBeNull();
    expect(await formatAutocomplete(null)).toBeNull();
  });

  it('should format company for autocomplete', async () => {
    const company = {
      identifier: 'BE_0123456789',
      company_name: 'Test Company',
      vat_formatted: 'BE 0123.456.789',
      active: true
    };

    const result = await formatAutocomplete(company);

    expect(result).toEqual({
      address: null,
      id: 'BE_0123456789',
      identifier: 'BE_0123456789',
      entity: null,
      name: 'Test Company',
      formattedName: 'BE 0123.456.789 - Test Company',
      color: 'hsl(120, 50%, 50%)',
      initials: 'TC',
      isActive: true,
      juridicalForm: null,
      mainActivity: null,
      type: null,
      vatFormatted: 'BE 0123.456.789'
    });
  });

  it('should handle inactive company in autocomplete', async () => {
    const inactiveCompany = {
      identifier: 'BE_0123456789',
      company_name: 'Test Company',
      vat_formatted: 'BE 0123.456.789',
      active: false
    };

    const result = await formatAutocomplete(inactiveCompany);
    expect(result.isActive).toBe(false);
  });
});
