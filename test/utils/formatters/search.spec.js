import { describe, it, expect, vi } from 'vitest';
import { formatSearchAggs } from '@/utils/formatters/search';

// Mock dependencies
vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code) => (code ? `Mock ${code}` : null))
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getJuridicalFormCodes: vi.fn(() =>
    Promise.resolve([
      {
        FORM1: [{ nl: { code: 'FORM1', label: 'Test Form 1' } }]
      },
      {
        FORM2: [{ nl: { code: 'FORM2', label: 'Test Form 2' } }]
      }
    ])
  ),
  getLegalPersonTypeCodes: vi.fn(() =>
    Promise.resolve([
      {
        TYPE1: [{ nl: { code: 'TYPE1', label: 'Test Type 1' } }]
      },
      {
        TYPE2: [{ nl: { code: 'TYPE2', label: 'Test Type 2' } }]
      }
    ])
  )
}));

describe('formatSearchAggs', () => {
  it('should return empty arrays if no aggregations provided', async () => {
    const result = await formatSearchAggs();

    expect(result).toEqual({
      startDates: [],
      legalPersonTypes: [],
      postalCodes: [],
      juridicalForms: []
    });
  });

  it('should format all aggregation types', async () => {
    const aggs = {
      start_date: [
        { key: '2024', doc_count: 10 },
        { key: '2023', doc_count: 20 }
      ],
      legal_person_type: [
        { key: 'TYPE1', doc_count: 5 },
        { key: 'TYPE2', doc_count: 15 }
      ],
      postal_code: [
        { key: '1000', doc_count: 30 },
        { key: '2000', doc_count: 25 }
      ],
      juridical_form: [
        { key: 'FORM1', doc_count: 8 },
        { key: 'FORM2', doc_count: 12 }
      ]
    };

    const result = await formatSearchAggs(aggs);

    expect(result.startDates).toEqual([
      { value: '2024', label: '2024', count: 10 },
      { value: '2023', label: '2023', count: 20 }
    ]);

    expect(result.legalPersonTypes).toEqual([
      { value: 'TYPE1', label: 'Mock TYPE1', count: 5 },
      { value: 'TYPE2', label: 'Mock TYPE2', count: 15 }
    ]);

    expect(result.postalCodes).toEqual([
      { value: 1000, label: 1000, count: 30 },
      { value: 2000, label: 2000, count: 25 }
    ]);

    expect(result.juridicalForms).toEqual([
      { value: 'FORM1', label: 'Mock FORM1', count: 8 },
      { value: 'FORM2', label: 'Mock FORM2', count: 12 }
    ]);
  });

  it('should handle empty arrays for each aggregation type', async () => {
    const aggs = {
      start_date: [],
      legal_person_type: [],
      postal_code: [],
      juridical_form: []
    };

    const result = await formatSearchAggs(aggs);

    expect(result.startDates).toEqual([]);
    expect(result.legalPersonTypes).toEqual([]);
    expect(result.postalCodes).toEqual([]);
    expect(result.juridicalForms).toEqual([]);
  });

  it('should handle missing aggregation types', async () => {
    const aggs = {
      start_date: [{ key: '2024', doc_count: 10 }]
      // other aggregations missing
    };

    const result = await formatSearchAggs(aggs);

    expect(result.startDates).toEqual([
      { value: '2024', label: '2024', count: 10 }
    ]);
    expect(result.legalPersonTypes).toEqual([]);
    expect(result.postalCodes).toEqual([]);
    expect(result.juridicalForms).toEqual([]);
  });

  it('should handle invalid postal codes', async () => {
    const aggs = {
      postal_code: [
        { key: 'invalid', doc_count: 5 },
        { key: '1000', doc_count: 10 }
      ]
    };

    const result = await formatSearchAggs(aggs);

    expect(result.postalCodes).toEqual([
      { value: NaN, label: NaN, count: 5 },
      { value: 1000, label: 1000, count: 10 }
    ]);
  });

  it('should handle null values in aggregations', async () => {
    const aggs = {
      start_date: [{ key: null, doc_count: 5 }],
      legal_person_type: [{ key: null, doc_count: 3 }],
      postal_code: [{ key: null, doc_count: 2 }],
      juridical_form: [{ key: null, doc_count: 4 }]
    };

    const result = await formatSearchAggs(aggs);

    expect(result.startDates).toEqual([{ value: null, label: null, count: 5 }]);
    expect(result.legalPersonTypes).toEqual([
      { value: null, label: null, count: 3 }
    ]);
    expect(result.postalCodes).toEqual([{ value: NaN, label: NaN, count: 2 }]);
    expect(result.juridicalForms).toEqual([
      { value: null, label: null, count: 4 }
    ]);
  });
});
