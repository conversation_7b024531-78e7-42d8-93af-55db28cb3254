import { describe, it, expect, vi } from 'vitest';
import formatPublication from '@/utils/formatters/publication';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => date)
}));

describe('formatPublication', () => {
  it('should return null if no publication provided', () => {
    expect(formatPublication()).toBeNull();
    expect(formatPublication(null)).toBeNull();
    expect(formatPublication(undefined)).toBeNull();
  });

  it('should format basic publication information', () => {
    const publication = {
      identifier: 'PUB123',
      publication_date: '2024-01-15',
      language: 'en',
      publication_type: 'TYPE1',
      uri: 'test-uri'
    };

    const result = formatPublication(publication);

    expect(result).toEqual({
      identifier: 'PUB123',
      originalDate: '2024-01-15',
      publicationDate: '2024-01-15',
      language: 'en',
      type: 'TYPE1',
      title: '',
      tags: [],
      hasURI: true
    });
  });

  it('should handle publication without URI', () => {
    const publication = {
      identifier: 'PUB123',
      publication_date: '2024-01-15',
      language: 'fr',
      publication_type: 'TYPE2',
      uri: null
    };

    const result = formatPublication(publication);

    expect(result).toEqual({
      identifier: 'PUB123',
      originalDate: '2024-01-15',
      publicationDate: '2024-01-15',
      language: 'fr',
      type: 'TYPE2',
      title: '',
      tags: [],
      hasURI: false
    });
  });

  it('should handle publication with missing optional fields', () => {
    const publication = {
      identifier: 'PUB123',
      publication_date: null,
      uri: 'test-uri'
    };

    const result = formatPublication(publication);

    expect(result).toEqual({
      identifier: 'PUB123',
      originalDate: null,
      publicationDate: null,
      language: undefined,
      type: undefined,
      title: '',
      tags: [],
      hasURI: true
    });
  });

  it('should handle publication with empty string values', () => {
    const publication = {
      identifier: 'PUB123',
      publication_date: '2024-01-15',
      language: '',
      publication_type: '',
      uri: ''
    };

    const result = formatPublication(publication);

    expect(result).toEqual({
      identifier: 'PUB123',
      originalDate: '2024-01-15',
      publicationDate: '2024-01-15',
      language: '',
      type: '',
      title: '',
      tags: [],
      hasURI: false
    });
  });
});
