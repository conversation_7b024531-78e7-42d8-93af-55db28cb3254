import { describe, it, expect, vi } from 'vitest';
import {
  formatPublicationSearch,
  formatAggs
} from '@/utils/formatters/publication-search';
import { findCodeName } from '@/utils/companies/codes';

// Mock dependencies
vi.mock('@/utils/date', () => ({
  formatDate: vi.fn((date) => date)
}));

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn((code) => (code ? `Mock ${code}` : null))
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getJuridicalSituationCodes: vi.fn(() =>
    Promise.resolve([
      {
        LLC: [{ nl: { code: 'LLC', label: 'Limited Liability Company' } }]
      }
    ])
  ),
  getRolesCodes: vi.fn(() =>
    Promise.resolve([
      {
        ROLE1: [{ nl: { code: 'ROLE1', label: 'Test Role 1' } }]
      },
      {
        ROLE2: [{ nl: { code: 'ROLE2', label: 'Test Role 2' } }]
      }
    ])
  )
}));

describe('formatPublicationSearch', () => {
  it('should return null if no publication provided', async () => {
    expect(await formatPublicationSearch()).toBeNull();
    expect(await formatPublicationSearch(null)).toBeNull();
  });

  it('should format basic publication information', async () => {
    const publication = {
      content_date: '2024-01-15',
      company_name: {
        name: 'Test Company'
      },
      company_identifier: 'BE_0123456789',
      uri: 'test-uri',
      high_lights: {
        title: ['Test Title'],
        content: 'Test Content'
      },
      title: 'Publication Title',
      tags: [{ keywords: ['keyword1'] }, { keywords: ['keyword2', 'keyword3'] }]
    };

    const result = await formatPublicationSearch(publication);

    expect(result).toEqual({
      date: '2024-01-15',
      companyName: 'Test Company',
      companyVat: 'BE_0123456789',
      companyVatFormatted: 'BE 0123456789',
      companyJuridicalSituation: null,
      highlightsTitle: 'Test Title',
      highlights: 'Test Content',
      uri: 'test-uri',
      title: 'Publication Title',
      tags: ['keyword1', 'keyword2', 'keyword3'],
      address: null
    });
  });

  it('should handle publication with address', async () => {
    const publication = {
      content_date: '2024-01-15',
      company_name: {
        name: 'Test Company'
      },
      company_identifier: 'BE_0123456789',
      company_address: {
        postal_code: '1000',
        locality: 'Brussels'
      }
    };

    const result = await formatPublicationSearch(publication);
    expect(result.address).toBe('1000 - Brussels');
  });

  it('should handle publication with juridical form', async () => {
    const publication = {
      content_date: '2024-01-15',
      company_name: {
        name: 'Test Company'
      },
      company_identifier: 'BE_0123456789',
      company_juridical_form: {
        code: 'LLC'
      }
    };

    const result = await formatPublicationSearch(publication);
    expect(result.companyJuridicalSituation).toBe('Mock LLC');
    expect(findCodeName).toHaveBeenCalledWith('LLC', expect.any(Object));
  });

  it('should handle missing optional fields', async () => {
    const publication = {
      content_date: '2024-01-15',
      company_name: {
        name: 'Test Company'
      },
      company_identifier: 'BE_0123456789'
    };

    const result = await formatPublicationSearch(publication);

    expect(result).toEqual({
      date: '2024-01-15',
      companyName: 'Test Company',
      companyVat: 'BE_0123456789',
      companyVatFormatted: 'BE 0123456789',
      companyJuridicalSituation: null,
      highlightsTitle: undefined,
      highlights: undefined,
      uri: undefined,
      title: '',
      tags: [],
      address: null
    });
  });
});

describe('formatAggs', () => {
  it('should return empty arrays if no aggregations provided', async () => {
    const result = await formatAggs({});

    expect(result).toEqual({
      notaries: [],
      auditors: [],
      functions: [],
      postalCodes: [],
      companyIdentifiers: []
    });
  });

  it('should format all aggregation types', async () => {
    const aggs = {
      notary: [
        { key: 'Notary1', doc_count: 5 },
        { key: 'Notary2', doc_count: 3 }
      ],
      auditor: [{ key: 'Auditor1', doc_count: 2 }],
      function: [
        { key: 'ROLE1', doc_count: 4 },
        { key: 'ROLE2', doc_count: 1 }
      ],
      postal_code: [{ key: '1000', doc_count: 10 }],
      company_identifier: [{ key: 'BE_0123456789', doc_count: 7 }]
    };

    const result = await formatAggs(aggs);

    expect(result.notaries).toEqual([
      { label: 'Notary1', count: 5, value: 'Notary1' },
      { label: 'Notary2', count: 3, value: 'Notary2' }
    ]);

    expect(result.auditors).toEqual([
      { label: 'Auditor1', count: 2, value: 'Auditor1' }
    ]);

    expect(result.functions).toEqual([
      { label: 'Mock ROLE1', count: 4, value: 'ROLE1' },
      { label: 'Mock ROLE2', count: 1, value: 'ROLE2' }
    ]);

    expect(result.postalCodes).toEqual([
      { label: '1000', count: 10, value: '1000' }
    ]);

    expect(result.companyIdentifiers).toEqual([
      { label: 'BE_0123456789', count: 7, value: 'BE_0123456789' }
    ]);
  });

  it('should handle empty arrays for each aggregation type', async () => {
    const aggs = {
      notary: [],
      auditor: [],
      function: [],
      postal_code: [],
      company_identifier: []
    };

    const result = await formatAggs(aggs);

    expect(result.notaries).toEqual([]);
    expect(result.auditors).toEqual([]);
    expect(result.functions).toEqual([]);
    expect(result.postalCodes).toEqual([]);
    expect(result.companyIdentifiers).toEqual([]);
  });
});
