import { describe, it, expect, vi } from 'vitest';
import { formatFunction } from '@/utils/formatters/functions';
import { findCodeName } from '@/utils/companies/codes';
import { formatDate } from '@/utils/date';

vi.mock('@/utils/companies/codes', () => ({
  findCodeName: vi.fn()
}));

vi.mock('@/utils/date', () => ({
  formatDate: vi.fn()
}));

// Mock the code loader
vi.mock('@/utils/code-loader', () => ({
  getRolesCodes: vi.fn(() =>
    Promise.resolve([
      {
        MD: [{ nl: { code: 'MD', label: 'Managing Director' } }]
      },
      {
        DIR: [{ nl: { code: 'DIR', label: 'Director' } }]
      }
    ])
  )
}));

describe('formatFunction', () => {
  it('should return null if no company function is provided', async () => {
    expect(await formatFunction(null)).toBeNull();
    expect(await formatFunction(undefined)).toBeNull();
  });

  it('should format company function data correctly', async () => {
    // Mock dependencies
    findCodeName.mockReturnValue('Managing Director');
    formatDate.mockReturnValue('2023-01-01');

    const mockCompanyFunction = {
      role_code: 'MD',
      role_start_date: '2023-01-01T00:00:00Z',
      additional_field: 'some value'
    };

    const result = await formatFunction(mockCompanyFunction);

    expect(result).toEqual({
      role_code: 'MD',
      role_start_date: '2023-01-01T00:00:00Z',
      additional_field: 'some value',
      codeName: 'Managing Director',
      roleStartDate: '2023-01-01'
    });

    // Verify mocks were called with correct arguments
    expect(findCodeName).toHaveBeenCalledWith('MD', expect.any(Object));
    expect(formatDate).toHaveBeenCalledWith(
      '2023-01-01T00:00:00Z',
      expect.any(Object)
    );
  });

  it('should preserve all original fields while adding formatted ones', async () => {
    findCodeName.mockReturnValue('Director');
    formatDate.mockReturnValue('2023-06-15');

    const mockCompanyFunction = {
      role_code: 'DIR',
      role_start_date: '2023-06-15T00:00:00Z',
      is_active: true,
      department: 'Board',
      notes: 'Important role'
    };

    const result = await formatFunction(mockCompanyFunction);

    expect(result).toEqual({
      role_code: 'DIR',
      role_start_date: '2023-06-15T00:00:00Z',
      is_active: true,
      department: 'Board',
      notes: 'Important role',
      codeName: 'Director',
      roleStartDate: '2023-06-15'
    });
  });

  it('should handle missing optional fields', async () => {
    findCodeName.mockReturnValue('Unknown Role');
    formatDate.mockReturnValue(null);

    const mockCompanyFunction = {
      role_code: 'UNKNOWN',
      is_active: false
    };

    const result = await formatFunction(mockCompanyFunction);

    expect(result).toEqual({
      role_code: 'UNKNOWN',
      is_active: false,
      codeName: 'Unknown Role',
      roleStartDate: null
    });
  });
});
