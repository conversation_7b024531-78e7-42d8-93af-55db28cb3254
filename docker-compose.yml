services:
  node:
    build:
      context: .
      args:
        - build_number_ci
        - fontawesome_npm_auth_token=${FONTAWESOME_NPM_AUTH_TOKEN}
    command: npm run dev
    environment:
      APP_PORT: 19301
      VITE_GOOGLE_API_KEY: ${GOOGLE_API_KEY}
      VITE_APP_VERSION: ${build_number_ci}
      VITE_COOKIE_SECURE: false
    ports:
      - '19301:19301'
      - '24678:24678' # hot reload when developing
    volumes:
      - ./package.json:/var/www/package.json
      - ./package-lock.json:/var/www/package-lock.json
      - ./vite.config.js:/var/www/vite.config.js
      - ./index.html:/var/www/index.html
      - ./src:/var/www/src
      - ./test:/var/www/test
      - ./public:/var/www/public
