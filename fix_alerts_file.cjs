#!/usr/bin/env node

const fs = require('fs');

const filePath = 'src/pages/companies/components/network-search.vue';

try {
  console.log(`Processing ${filePath}...`);

  const content = fs.readFileSync(filePath, 'utf8');

  // Extract template, script, and style sections with more precise regex
  const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/);
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
  const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/);

  if (!templateMatch || !scriptMatch) {
    console.log(`Error: Missing template or script section in ${filePath}`);
    process.exit(1);
  }

  console.log(
    'Template section found:',
    templateMatch[0].substring(0, 100) + '...'
  );
  console.log(
    'Script section found:',
    scriptMatch[0].substring(0, 100) + '...'
  );
  if (styleMatch) {
    console.log(
      'Style section found:',
      styleMatch[0].substring(0, 100) + '...'
    );
  }

  // Reconstruct the file with script first, then template, then style
  let newContent = '';

  // Add script section first
  const scriptAttrs = scriptMatch[0].match(/<script([^>]*)>/)[1] || '';
  newContent += `<script${scriptAttrs}>${scriptMatch[1]}</script>\n\n`;

  // Add template section
  const templateAttrs = templateMatch[0].match(/<template([^>]*)>/)[1] || '';
  newContent += `<template${templateAttrs}>${templateMatch[1]}</template>`;

  // Add style section if it exists
  if (styleMatch) {
    const styleAttrs = styleMatch[0].match(/<style([^>]*)>/)[1] || '';
    newContent += `\n\n<style${styleAttrs}>${styleMatch[1]}</style>`;
  }

  // Add final newline
  newContent += '\n';

  // Write the new content back to the file
  fs.writeFileSync(filePath, newContent, 'utf8');
  console.log(`Successfully reorganized ${filePath}`);
} catch (error) {
  console.error(`Error processing ${filePath}:`, error.message);
  process.exit(1);
}
