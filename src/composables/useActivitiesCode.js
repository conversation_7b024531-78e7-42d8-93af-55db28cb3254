import { ref, onMounted } from 'vue';
import {
  getActivitiesCode,
  isActivitiesCodeLoaded
} from '@/utils/activities-code';

/**
 * Composable for managing activities code data
 * @param {boolean} autoLoad - Whether to automatically load data on mount
 * @returns {Object} Reactive data and methods
 */
export const useActivitiesCode = (autoLoad = true) => {
  const activitiesCode = ref(null);
  const loading = ref(false);
  const error = ref(null);

  /**
   * Load activities code data
   */
  const loadData = async () => {
    // Don't load if already loading
    if (loading.value) return;

    // Return cached data if available
    if (isActivitiesCodeLoaded() && activitiesCode.value) {
      return activitiesCode.value;
    }

    loading.value = true;
    error.value = null;

    try {
      const data = await getActivitiesCode();
      activitiesCode.value = data;
      return data;
    } catch (err) {
      error.value = err.message || 'Failed to load activities code';
      console.error('Error in useActivitiesCode:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Search activities by code or description
   * @param {string} query - Search query
   * @returns {Array} Filtered activities
   */
  const searchActivities = (query) => {
    if (!activitiesCode.value || !query) return activitiesCode.value || [];

    const searchTerm = query.toLowerCase();

    // Assuming your JSON structure has 'code' and 'description' fields
    // Adjust this based on your actual JSON structure
    return activitiesCode.value.filter(
      (activity) =>
        activity.code?.toLowerCase().includes(searchTerm) ||
        activity.description?.toLowerCase().includes(searchTerm)
    );
  };

  /**
   * Get activity by code
   * @param {string} code - Activity code
   * @returns {Object|null} Activity object or null if not found
   */
  const getActivityByCode = (code) => {
    if (!activitiesCode.value || !code) return null;

    return (
      activitiesCode.value.find((activity) => activity.code === code) || null
    );
  };

  /**
   * Get activities by category
   * @param {string} category - Category name
   * @returns {Array} Activities in the category
   */
  const getActivitiesByCategory = (category) => {
    if (!activitiesCode.value || !category) return [];

    return activitiesCode.value.filter(
      (activity) => activity.category === category
    );
  };

  // Auto-load data on mount if requested
  if (autoLoad) {
    onMounted(() => {
      loadData();
    });
  }

  return {
    // Reactive data
    activitiesCode,
    loading,
    error,

    // Methods
    loadData,
    searchActivities,
    getActivityByCode,
    getActivitiesByCategory
  };
};
