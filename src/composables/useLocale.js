import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  setLocale,
  loadLocaleMessages,
  getAvailableLocales,
  isLocaleLoaded,
  preloadLocales,
  AVAILABLE_LOCALES
} from '@/plugins/i18n-dynamic';

// Global loading state
const isLoadingLocale = ref(false);

/**
 * Composable for managing locales dynamically
 * @returns {Object} Locale management utilities
 */
export function useLocale() {
  const { locale, availableLocales, t } = useI18n();

  /**
   * Change the current locale
   * @param {string} newLocale - Locale to switch to
   * @returns {Promise<boolean>} - Success status
   */
  const changeLocale = async (newLocale) => {
    if (locale.value === newLocale) {
      return true; // Already set
    }

    isLoadingLocale.value = true;

    try {
      const success = await setLocale(newLocale);
      return success;
    } catch (error) {
      console.error('Failed to change locale:', error);
      return false;
    } finally {
      isLoadingLocale.value = false;
    }
  };

  /**
   * Preload a specific locale
   * @param {string} locale - Locale to preload
   * @returns {Promise<boolean>} - Success status
   */
  const preloadLocale = async (locale) => {
    return await loadLocaleMessages(locale);
  };

  /**
   * Preload all available locales
   * @returns {Promise<boolean[]>} - Array of success statuses
   */
  const preloadAllLocales = async () => {
    return await preloadLocales();
  };

  /**
   * Get locale options for UI components
   * @returns {Array} - Array of {label, value} objects
   */
  const localeOptions = computed(() => {
    return AVAILABLE_LOCALES.map((locale) => ({
      label: getLocaleDisplayName(locale),
      value: locale
    }));
  });

  /**
   * Get display name for a locale
   * @param {string} locale - Locale code
   * @returns {string} - Display name
   */
  const getLocaleDisplayName = (locale) => {
    const names = {
      en: 'English',
      fr: 'Français',
      nl: 'Nederlands'
    };
    return names[locale] || locale;
  };

  /**
   * Check if a locale is currently loading
   * @returns {boolean} - Loading status
   */
  const isLoading = computed(() => isLoadingLocale.value);

  /**
   * Get current locale
   * @returns {string} - Current locale code
   */
  const currentLocale = computed(() => locale.value);

  /**
   * Check if a locale is loaded
   * @param {string} locale - Locale to check
   * @returns {boolean} - Whether the locale is loaded
   */
  const checkLocaleLoaded = (locale) => {
    return isLocaleLoaded(locale);
  };

  /**
   * Get all available locales
   * @returns {string[]} - Array of available locale codes
   */
  const getAllAvailableLocales = () => {
    return getAvailableLocales();
  };

  return {
    // State
    currentLocale,
    isLoading,
    localeOptions,

    // Actions
    changeLocale,
    preloadLocale,
    preloadAllLocales,

    // Utilities
    getLocaleDisplayName,
    checkLocaleLoaded,
    getAllAvailableLocales,

    // Vue i18n utilities
    t,
    availableLocales
  };
}
