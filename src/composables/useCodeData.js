import { ref, onMounted } from 'vue';
import { getCodeData, isCodeDataLoaded } from '@/utils/code-loader';

/**
 * Generic composable for managing code data
 * @param {string} fileName - Name of the code file to load
 * @param {boolean} autoLoad - Whether to automatically load data on mount
 * @returns {Object} Reactive data and methods
 */
export const useCodeData = (fileName, autoLoad = true) => {
  const codeData = ref(null);
  const loading = ref(false);
  const error = ref(null);

  /**
   * Load code data
   */
  const loadData = async () => {
    // Don't load if already loading
    if (loading.value) return;

    // Return cached data if available
    if (isCodeDataLoaded(fileName) && codeData.value) {
      return codeData.value;
    }

    loading.value = true;
    error.value = null;

    try {
      const data = await getCodeData(fileName);
      codeData.value = data;
      return data;
    } catch (err) {
      error.value = err.message || `Failed to load ${fileName}`;
      console.error(`Error in useCodeData for ${fileName}:`, err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Search codes by code or label
   * @param {string} query - Search query
   * @returns {Array} Filtered codes
   */
  const searchCodes = (query) => {
    if (!codeData.value || !query) return codeData.value || [];

    const searchTerm = query.toLowerCase();

    return codeData.value.filter((codeItem) => {
      const codeKey = Object.keys(codeItem)[0];
      const translations = Object.values(codeItem)[0];

      // Search in code
      if (codeKey.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // Search in labels across all languages
      return translations.some((translation) => {
        const langData = Object.values(translation)[0];
        return langData.label?.toLowerCase().includes(searchTerm);
      });
    });
  };

  /**
   * Get code item by code
   * @param {string} code - Code to find
   * @returns {Object|null} Code object or null if not found
   */
  const getCodeByCode = (code) => {
    if (!codeData.value || !code) return null;

    return (
      codeData.value.find((codeItem) => {
        const codeKey = Object.keys(codeItem)[0];
        return codeKey.toUpperCase() === code.toUpperCase();
      }) || null
    );
  };

  /**
   * Get all codes as a flat array with code and label
   * @param {string} locale - Language locale (defaults to 'nl')
   * @returns {Array} Array of {code, label} objects
   */
  const getFlatCodes = (locale = 'nl') => {
    if (!codeData.value) return [];

    return codeData.value.map((codeItem) => {
      const codeKey = Object.keys(codeItem)[0];
      const translations = Object.values(codeItem)[0];

      // Find translation for the requested locale
      let translation = translations.find((t) => Object.keys(t)[0] === locale);

      // Fallback to Dutch if locale not found
      if (!translation) {
        translation = translations.find((t) => Object.keys(t)[0] === 'nl');
      }

      // Fallback to first available translation
      if (!translation) {
        translation = translations[0];
      }

      const langData = Object.values(translation)[0];

      return {
        code: codeKey,
        label: langData.label || codeKey,
        short: langData.short || null
      };
    });
  };

  // Auto-load data on mount if requested
  if (autoLoad) {
    onMounted(() => {
      loadData();
    });
  }

  return {
    // Reactive data
    codeData,
    loading,
    error,

    // Methods
    loadData,
    searchCodes,
    getCodeByCode,
    getFlatCodes
  };
};

// Specific composables for each code type (for convenience)
export const useActivityCodes = (autoLoad = true) =>
  useCodeData('activity-codes', autoLoad);
export const useAuthorizationCodes = (autoLoad = true) =>
  useCodeData('authorization-codes', autoLoad);
export const useJuridicalFormCodes = (autoLoad = true) =>
  useCodeData('juridical-form-codes', autoLoad);
export const useJuridicalSituationCodes = (autoLoad = true) =>
  useCodeData('juridical-situation-codes', autoLoad);
export const useCharacteristicCodes = (autoLoad = true) =>
  useCodeData('characteristic-codes', autoLoad);
export const useRolesCodes = (autoLoad = true) =>
  useCodeData('roles-codes', autoLoad);
export const useLegalPersonTypeCodes = (autoLoad = true) =>
  useCodeData('legal-person-type-codes', autoLoad);

// Backward compatibility alias
export const useActivitiesCode = useActivityCodes;
