<template>
  <q-layout class="application">
    <router-view />
  </q-layout>
</template>

<script setup>
import '@/styles/settings.css';
// import { computed } from 'vue';
import { useQuasar } from 'quasar';
import { AppStore } from '@/store';
import 'quasar/dist/quasar.addon.css';

const appStore = AppStore();
const $q = useQuasar();
// deactivate dark mode for now
$q.dark.set(false);
// const theme = computed(() => localStorage.getItem('theme'));
const isNavCollapsed = JSON.parse(localStorage.getItem('nav'));
appStore.setNavCollapse(isNavCollapsed);

// cannot use vue-router here
// do not apply theme changes on report
// const isReport = document.location.pathname.includes('/report/');

// Apply saved theme (manual selection)
// if (theme.value !== null) {
//   appStore.setTheme(theme.value);
//   $q.dark.set(theme.value === 'dark');
// }

// set base on preferences if not saved (auto selection)
// if (!theme.value && window.matchMedia && !isReport) {
//   const userPreferedScheme = window.matchMedia(
//     '(prefers-color-scheme: dark)'
//   ).matches;
//   appStore.setTheme(userPreferedScheme ? 'dark' : 'light');
//   $q.dark.set(userPreferedScheme);
// }

// watch for changes
// if (window.matchMedia && !isReport) {
//   window
//     .matchMedia('(prefers-color-scheme: dark)')
//     .addEventListener('change', (event) => {
//       const newColorScheme = event.matches ? 'dark' : 'light';
//       appStore.setTheme(newColorScheme);
//       $q.dark.set(newColorScheme === 'dark');
//     });
// }
</script>
