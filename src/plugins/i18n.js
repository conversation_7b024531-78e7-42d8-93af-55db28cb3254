import { createI18n } from 'vue-i18n';
import en from '@/locales/en.json';
import fr from '@/locales/fr.json';
import nl from '@/locales/nl.json';
import enFinancials from '@/locales/en.financials.json';
import frFinancials from '@/locales/fr.financials.json';
import nlFinancials from '@/locales/nl.financials.json';

const browserDefault = window?.navigator?.language || 'en';
const savedLocale = localStorage.getItem('locale');

const i18n = createI18n({
  locale: savedLocale || browserDefault,
  legacy: false,
  messages: {
    en: {
      ...en,
      financialsKeys: enFinancials
    },
    fr: {
      ...fr,
      financialsKeys: frFinancials
    },
    nl: {
      ...nl,
      financialsKeys: nlFinancials
    }
  }
});

export default i18n;
