import { createI18n } from 'vue-i18n';

// Available locales
export const AVAILABLE_LOCALES = ['en', 'fr', 'nl'];
export const DEFAULT_LOCALE = 'en';

// Cache for loaded locales
const loadedLocales = new Set();

// Determine initial locale
function getInitialLocale() {
  const savedLocale = localStorage.getItem('locale');
  const browserDefault =
    window?.navigator?.language?.split('-')[0] || DEFAULT_LOCALE;

  const preferredLocale = savedLocale || browserDefault;

  // Return default if preferred locale is not available
  return AVAILABLE_LOCALES.includes(preferredLocale)
    ? preferredLocale
    : DEFAULT_LOCALE;
}

// Create i18n instance with minimal initial setup
const i18n = createI18n({
  locale: DEFAULT_LOCALE, // Start with default, will be updated after loading
  legacy: false,
  fallbackLocale: DEFAULT_LOCALE,
  messages: {
    // Start with empty messages, will be populated dynamically
  }
});

/**
 * Load locale messages dynamically
 * @param {string} locale - Locale to load (e.g., 'en', 'fr', 'nl')
 * @returns {Promise<boolean>} - Success status
 */
export async function loadLocaleMessages(locale) {
  // Validate locale
  if (!AVAILABLE_LOCALES.includes(locale)) {
    console.warn(
      `Locale "${locale}" is not available. Available locales: ${AVAILABLE_LOCALES.join(
        ', '
      )}`
    );
    return false;
  }

  // Skip if already loaded
  if (loadedLocales.has(locale)) {
    return true;
  }

  try {
    // Load both main and financial translations in parallel from public directory
    const [mainResponse, financialResponse] = await Promise.all([
      fetch(`/locales/${locale}.json`),
      fetch(`/locales/${locale}.financials.json`)
    ]);

    // Check if responses are ok
    if (!mainResponse.ok || !financialResponse.ok) {
      throw new Error(`Failed to fetch locale files for "${locale}"`);
    }

    // Parse JSON responses
    const [mainMessages, financialMessages] = await Promise.all([
      mainResponse.json(),
      financialResponse.json()
    ]);

    // Combine messages
    const messages = {
      ...mainMessages,
      financialsKeys: financialMessages
    };

    // Set messages for the locale
    i18n.global.setLocaleMessage(locale, messages);

    // Mark as loaded
    loadedLocales.add(locale);

    console.log(`✅ Loaded locale: ${locale}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to load locale "${locale}":`, error);
    return false;
  }
}

/**
 * Set the current locale, loading it if necessary
 * @param {string} locale - Locale to set
 * @returns {Promise<boolean>} - Success status
 */
export async function setLocale(locale) {
  // Validate locale
  if (!AVAILABLE_LOCALES.includes(locale)) {
    console.warn(
      `Cannot set locale "${locale}". Available locales: ${AVAILABLE_LOCALES.join(
        ', '
      )}`
    );
    return false;
  }

  try {
    // Load locale if not already loaded
    const loaded = await loadLocaleMessages(locale);

    if (loaded) {
      // Update current locale
      i18n.global.locale.value = locale;

      // Save to localStorage
      localStorage.setItem('locale', locale);

      console.log(`🌐 Locale set to: ${locale}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Failed to set locale "${locale}":`, error);
    return false;
  }
}

/**
 * Initialize i18n with the preferred locale
 * @returns {Promise<void>}
 */
export async function initializeI18n() {
  const initialLocale = getInitialLocale();

  console.log(`🚀 Initializing i18n with locale: ${initialLocale}`);

  // Load and set the initial locale
  await setLocale(initialLocale);
}

/**
 * Get list of available locales
 * @returns {string[]} - Array of available locale codes
 */
export function getAvailableLocales() {
  return [...AVAILABLE_LOCALES];
}

/**
 * Check if a locale is loaded
 * @param {string} locale - Locale to check
 * @returns {boolean} - Whether the locale is loaded
 */
export function isLocaleLoaded(locale) {
  return loadedLocales.has(locale);
}

/**
 * Preload multiple locales
 * @param {string[]} locales - Array of locales to preload
 * @returns {Promise<boolean[]>} - Array of success statuses
 */
export async function preloadLocales(locales = AVAILABLE_LOCALES) {
  console.log(`📦 Preloading locales: ${locales.join(', ')}`);

  const results = await Promise.all(
    locales.map((locale) => loadLocaleMessages(locale))
  );

  const successCount = results.filter(Boolean).length;
  console.log(`✅ Preloaded ${successCount}/${locales.length} locales`);

  return results;
}

export default i18n;
