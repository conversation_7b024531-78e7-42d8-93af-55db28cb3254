import { Quasar } from 'quasar';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createHead } from '@vueuse/head';
import { loadFonts } from '@/plugins/webfontloader';
import i18n from '@/plugins/i18n';
import quasarOptions from '@/plugins/quasar';
import { checkIfToken } from './plugins/checkIfToken';
import router from '@/router';
import App from '@/app.vue';

import '@fortawesome/fontawesome-pro/css/fontawesome.min.css';
import '@fortawesome/fontawesome-pro/css/regular.min.css';
import '@fortawesome/fontawesome-pro/css/solid.min.css';

checkIfToken();

const app = createApp(App);
const store = createPinia();

const head = createHead({
  title: 'Data.be',
  titleTemplate: (title) => `${title} - Data.be`
});

loadFonts();

app.use(head).use(store).use(router).use(Quasar, quasarOptions).use(i18n);

app.mount('#app');
