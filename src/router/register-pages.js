import AuthenticatedLayout from '@/components/layouts/authenticated.vue';
import { auth } from '@/pages/auth/routes';
import { dashboard } from '@/pages/dashboard/routes';
import { companies } from '@/pages/companies/routes';
import { persons } from '@/pages/persons/routes';
import { settings } from '@/pages/settings/routes';
import { search } from '@/pages/search/routes';
import { monitoring } from '@/pages/monitoring/routes';
import { mandates } from '@/pages/mandates/routes';
import { publications } from '@/pages/publications/routes';
import { lists } from '@/pages/lists/routes';
import { errors } from '@/pages/errors/routes';

const healthcheck = {
  path: '/healthcheck',
  component: () => import('@/pages/healthcheck.vue')
};

const networkGraph = {
  path: '/business-graph',
  name: 'global-business-graph',
  props: {
    noHeader: true,
    fullPage: true,
    withSearch: true
  },
  meta: {
    noPadding: true
  },
  component: () => import('@/pages/companies/children/network.vue')
};

const authenticatedRoutes = {
  path: '/',
  component: AuthenticatedLayout,
  meta: { authRequired: true },
  children: [
    dashboard,
    companies,
    persons,
    networkGraph,
    monitoring,
    settings,
    search,
    mandates,
    publications,
    lists,
    errors
  ]
};

const reportRoutes = {
  path: '/companies/report/:id',
  name: 'company-report',
  meta: { authRequired: true },
  component: () => import('@/pages/companies/report.vue')
};

export const pages = [auth, healthcheck, authenticatedRoutes, reportRoutes];
