<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useHeadSafe } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { SearchCompaniesStore } from '@/store/search-companies';
import { CompaniesStore } from '@/pages/companies/store';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import Autocomplete from '@/pages/companies/components/autocomplete.vue';
import RecentyViewed from '@/pages/companies/components/recenty-viewed.vue';
import WatchlistsLinks from '@/pages/companies/components/watchlists-links.vue';
import OfficialSources from '@/pages/companies/components/official-sources.vue';

const props = defineProps({ isDashboard: Boolean });

useHeadSafe({ title: 'Companies' });

const authStore = AuthStore();
const { user } = storeToRefs(authStore);

const companiesStore = CompaniesStore();
const {
  loading: companyLoading,
  recentlyViewed,
  stats
} = storeToRefs(companiesStore);

if (props.isDashboard) {
  companiesStore.getStats();
}

const monitoringStore = MonitoringStore();
const { loading: watchlistLoading, watchlists } = storeToRefs(monitoringStore);

const sortedWatchlists = computed(() =>
  [...(watchlists.value || [])].sort((a, b) => {
    const aValue = String(a.lastestAlert || '');
    const bValue = String(b.lastestAlert || '');
    return bValue.localeCompare(aValue);
  })
);

const searchCompaniesStore = SearchCompaniesStore();
const {
  loading,
  autocompleteCompaniesTotal,
  autocompleteCompaniesCount,
  autocompleteCompaniesResults
} = storeToRefs(searchCompaniesStore);

searchCompaniesStore.prepareAutocomplete();

const autocompleteInput = ref('');
const selectedType = ref('COMPANIES');

function onSelectType(value) {
  selectedType.value = value;
}

function autocomplete(input) {
  autocompleteInput.value = input;
  searchCompaniesStore.autocompleteCompanies(input);
}

const router = useRouter();

function goToSearchPage(searchInput) {
  router.push({ name: 'search-companies', query: { q: searchInput } });
}

onMounted(() => {
  if (!props.isDashboard) return;

  if (authStore.getUserOrganisation) {
    monitoringStore.loadWatchlists(authStore.getUserOrganisation);
  }

  if (user.value?.customerShortId) {
    companiesStore.loadRecentlyViewed(user.value.customerShortId);
  }
});

watch(
  () => authStore.getUserOrganisation,
  (organisationShortId) => monitoringStore.loadWatchlists(organisationShortId)
);

watch(
  () => user.value?.customerShortId,
  (customerShortId) => companiesStore.loadRecentlyViewed(customerShortId)
);
</script>

<template>
  <div>
    <div :class="['companies-search', { isExtended: !isDashboard }]">
      <div class="companies-search-inner q-pa-md shadow-10">
        <!-- FILTERS -->
        <Autocomplete
          :loading="loading"
          :companies="autocompleteCompaniesResults"
          :total="autocompleteCompaniesTotal"
          :count="autocompleteCompaniesCount"
          @select-type="onSelectType"
          @clear="searchCompaniesStore.clearAutocomplete"
          @submit="autocomplete"
          @direct-submit="goToSearchPage"
        />
      </div>
    </div>

    <!-- EMPTY PAGE DEFAULTS -->
    <div v-if="isDashboard" class="row q-pt-xl q-px-md q-px-lg-xl q-pb-md">
      <div class="col col-12 col-md-4 q-pt-xl q-pr-md-xl">
        <div class="text-h6 q-pb-sm">
          {{ $t('recentlyViewed') }}
          ({{ recentlyViewed.length }})
        </div>
        <RecentyViewed
          :recently-viewed="recentlyViewed"
          @clear="companiesStore.clearRentlyViewed(user.customerShortId)"
        />
      </div>

      <div class="col col-12 col-md-4 q-pt-xl">
        <div class="text-h6 q-pb-sm">
          {{ $t('myWatchlist') }}
          ({{ sortedWatchlists.length }})
        </div>
        <WatchlistsLinks
          :loading="watchlistLoading"
          :watchlists="sortedWatchlists"
        />
      </div>

      <div class="col col-12 col-md-4 q-pt-xl q-pl-md-xl">
        <div class="text-h6 q-pb-sm">
          {{ $t('officialSources') }}
        </div>
        <OfficialSources
          :loading="companyLoading"
          :companies="stats.companies"
          :publications="stats.publications"
          :statements="stats.financial_statements"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  max-width: calc(100% - 6rem);
  margin: 0 auto;
}

.autocomplete-filters {
  position: absolute;
  left: 3rem;
  right: 3rem;
  bottom: 0;
}

.companies-search {
  position: relative;
  background: url('/company-bg.jpg'),
    radial-gradient(circle at 25%, var(--primary), var(--primaryDark));
  background-size: cover;
  background-blend-mode: multiply;
  height: 20dvh;
  margin-top: -24px;
  transition: height 300ms ease-in-out;
}

.companies-search.isExtended {
  height: 20rem;
}

.companies-search-inner {
  position: absolute;
  left: 1rem;
  right: 1rem;
  bottom: 0;
  transform: translateY(50%);
  background: var(--white);
  border: 1px solid var(--border);
  z-index: 1;
  border-radius: 0.5rem;
}

@media (min-width: 1440px) {
  .companies-search-inner {
    left: 3rem;
    right: 3rem;
    padding: 1rem 3rem;
  }
}
</style>
