import i18n from '@/plugins/i18n-dynamic';
import { formatDate } from '@/utils/date';

export default [
  {
    align: 'center',
    name: 'icon'
  },
  {
    name: 'name',
    field: 'name',
    label: i18n.global.t('name'),
    align: 'left',
    sortable: true
  },
  {
    name: 'identifier',
    field: 'identifier',
    label: i18n.global.t('vat'),
    align: 'left',
    format: (value) => value?.replace('_', ' '),
    style: 'white-space: nowrap',
    sortable: true
  },
  {
    name: 'mainActivity',
    field: 'mainActivity',
    label: i18n.global.t('mainActivity'),
    align: 'left',
    sortable: true
  },
  {
    name: 'language',
    field: 'language',
    label: i18n.global.t('language'),
    align: 'center',
    style: 'text-transform: uppercase',
    sortable: true
  },
  {
    name: 'mainAddress',
    field: 'mainAddress',
    label: i18n.global.t('mainAddress'),
    align: 'left',
    sortable: true
  },
  {
    name: 'startDate',
    field: 'startDate',
    label: i18n.global.t('startDate'),
    format: (value) => formatDate(value, i18n.global.locale),
    align: 'right',
    sortable: true
  }
];
