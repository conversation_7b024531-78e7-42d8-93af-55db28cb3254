import i18n from '@/plugins/i18n-dynamic';
import { formatDate } from '@/utils/date';

export default [
  {
    name: 'start_date',
    label: i18n.global.t('date'),
    field: 'start_date',
    format: (value) => formatDate(value, i18n.global.locale) || '—',
    align: 'left',
    sortable: true
  },
  {
    name: 'company_name',
    label: i18n.global.t('name'),
    field: 'name',
    align: 'left',
    style: 'white-space: normal',
    sortable: true
  },
  {
    name: 'name_code',
    label: i18n.global.t('code'),
    field: 'code',
    align: 'center',
    sortable: true,
    format: (value) => i18n.global.t(value)
  },
  {
    name: 'name_language',
    label: i18n.global.t('language'),
    field: 'language',
    align: 'center',
    style: 'text-transform: uppercase',
    sortable: true
  }
];
