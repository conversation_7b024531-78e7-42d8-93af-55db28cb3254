import i18n from '@/plugins/i18n-dynamic';
import { formatDate } from '@/utils/date';

export default [
  {
    name: 'start_date',
    label: i18n.global.t('date'),
    field: 'start_date',
    format: (value) => formatDate(value, i18n.global.locale) || '—',
    align: 'left',
    sortable: true
  },
  {
    name: 'previous_address',
    label: i18n.global.t('previousAddress'),
    field: 'formatted_address',
    align: 'left',
    style: 'white-space: normal',
    sortable: true
  },
  {
    name: 'address_type',
    label: i18n.global.t('addressType'),
    field: 'primary',
    format: (value) =>
      value ? i18n.global.t('mainAddress') : i18n.global.t('otherAddress'),
    align: 'center',
    sortable: true
  },
  {
    label: i18n.global.t('googleMaps'),
    field: 'formated_address',
    name: 'map'
  }
];
