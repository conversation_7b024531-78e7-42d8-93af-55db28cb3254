import i18n from '@/plugins/i18n';
import { formatDate } from '@/utils/date';

export default [
  {
    name: 'date',
    field: 'publicationDate',
    label: i18n.global.t('date'),
    format: (value) => formatDate(value, i18n.global.locale) || '—',
    align: 'left',
    sortable: true
  },
  {
    name: 'type',
    field: 'type',
    label: 'type',
    align: 'left',
    format: (value) => i18n.global.t(value),
    sortable: true
  },
  {
    name: 'title',
    field: 'title',
    label: i18n.global.t('title'),
    align: 'left',
    sortable: true
  },
  {
    name: 'language',
    field: 'language',
    label: i18n.global.t('language'),
    align: 'left',
    format: (value) => i18n.global.t(value),
    sortable: true
  },
  {
    name: 'link',
    field: 'identifier',
    label: 'link',
    align: 'right'
  }
];
