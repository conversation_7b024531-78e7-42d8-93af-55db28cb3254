import i18n from '@/plugins/i18n-dynamic';

const { t } = i18n.global;
const keys = [
  'employees',
  'turnover',
  'gross_operating_margin',
  'current_ratio'
];
const othersKeys = [
  'current_assets',
  'tangible_fixed_assets',
  'equity',
  'gain_loss_period',
  'net_cash',
  'self_financing_degree',
  'return_on_equity',
  'added_value'
];
const icons = ['users', 'money-bill-transfer', 'chart-line', 'percent'];
const colors = ['primary', 'blue-grey', 'green', 'orange'];

export const keyFinancialsTeasing = {
  top: Array.from({ length: 4 }, (_, index) => ({
    isTop: true,
    key: keys[index],
    label: t(keys[index]),
    icon: `fa-regular fa-${icons[index]}`,
    color: colors[index],
    value: `${Math.round(Math.random() * 100)}${index === 3 ? '' : 'K'}`
  })),
  others: Array.from({ length: 8 }, (_, otherIndex) => ({
    isTop: false,
    key: othersKeys[othersKeys.length],
    label: t(othersKeys[otherIndex]),
    icon: 'fa-regular fa-circle-info',
    color: 'grey',
    value: `${Math.round(Math.random() * 100)}K`
  }))
};
