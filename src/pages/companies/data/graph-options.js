import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    id: 'employees',
    path: 'employees',
    label: i18n.global.t('financialsKeys.overview.employees'),
    type: 'line'
  },
  {
    id: 'turnover',
    path: 'turnover',
    label: i18n.global.t('financialsKeys.overview.turnover'),
    type: 'line'
  },
  {
    id: 'currentAssets',
    path: 'currentAssets',
    label: i18n.global.t('financialsKeys.overview.current_assets'),
    type: 'line'
  },
  {
    id: 'grossOperatingMargin',
    path: 'grossOperatingMargin',
    label: i18n.global.t('financialsKeys.overview.gross_operating_margin'),
    type: 'line'
  },
  {
    id: 'tangibleFixedAssets',
    path: 'tangibleFixedAssets',
    label: i18n.global.t('financialsKeys.overview.tangible_fixed_assets'),
    type: 'line'
  },
  {
    id: 'equity',
    path: 'equity',
    label: i18n.global.t('financialsKeys.overview.equity'),
    type: 'line'
  },
  {
    id: 'gainLossPeriod',
    path: 'gainLossPeriod',
    label: i18n.global.t('financialsKeys.overview.gain_loss_period'),
    type: 'line'
  },
  {
    id: 'currentRatio',
    path: 'currentRatio',
    label: i18n.global.t('financialsKeys.overview.current_ratio'),
    type: 'line'
  },
  {
    id: 'netCash',
    path: 'netCash',
    label: i18n.global.t('financialsKeys.overview.net_cash'),
    type: 'line'
  },
  {
    id: 'selfFinancingDegree',
    path: 'selfFinancingDegree',
    label: i18n.global.t('financialsKeys.overview.self_financing_degree'),
    type: 'line'
  },
  {
    id: 'returnOnEquity',
    path: 'returnOnEquity',
    label: i18n.global.t('financialsKeys.overview.return_on_equity'),
    type: 'line'
  },
  {
    id: 'addedValue',
    path: 'addedValue',
    label: i18n.global.t('financialsKeys.overview.added_value'),
    type: 'line'
  },
  {
    id: 'cashFlowToDebt',
    path: 'cashFlowToDebt',
    label: i18n.global.t('financialsKeys.ratios.solvency.cash_flow_to_debt'),
    type: 'line'
  }
];
