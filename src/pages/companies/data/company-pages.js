import i18n from '@/plugins/i18n-dynamic';
import { COMPANY_FULL_FINANCIALS } from '@/utils/features';

export const companyPageData = (features) => {
  const isAllowedForFullFinancials = features.includes(COMPANY_FULL_FINANCIALS);

  return [
    {
      title: i18n.global.t('company'),
      id: 'company',
      icon: 'fa-regular fa-building',
      children: [
        // {
        //   title: i18n.global.t('overview'),
        //   id: 'overview'
        // },
        {
          title: i18n.global.t('profile'),
          id: 'profile'
        },
        {
          title: i18n.global.t('establishments'),
          id: 'establishments'
        }
      ]
    },
    {
      title: i18n.global.t('events'),
      id: 'events',
      icon: 'fa-regular fa-newspaper',
      children: [
        {
          title: i18n.global.t('timeline'),
          id: 'timeline'
        },
        {
          title: i18n.global.t('publications'),
          id: 'publications'
        }
      ]
    },
    {
      title: i18n.global.t('structure'),
      id: 'structure',
      icon: 'fa-regular fa-diagram-project',
      children: [
        {
          title: i18n.global.t('businessGraph'),
          id: 'business-graph'
        },
        {
          title: i18n.global.t('legalRepresentatives'),
          id: 'legal-representatives'
        }
        // {
        //   title: i18n.global.t('stakeholders'),
        //   id: 'stakeholders'
        // }
      ]
    },
    {
      title: i18n.global.t('financials'),
      id: 'financials',
      icon: 'fa-regular fa-display-chart-up-circle-dollar',
      children: [
        {
          title: i18n.global.t('overview'),
          id: 'financials-overview'
        },
        {
          title: i18n.global.t('graphs'),
          id: 'graphs',
          disabled: !isAllowedForFullFinancials
        },
        {
          title: i18n.global.t('accountingFigures'),
          id: 'accounting-figures',
          disabled: !isAllowedForFullFinancials
        },
        {
          title: i18n.global.t('socialBalance'),
          id: 'social-balance',
          disabled: !isAllowedForFullFinancials
        },
        {
          title: i18n.global.t('keyRatios'),
          id: 'key-ratios',
          disabled: !isAllowedForFullFinancials
        }
      ]
    }
  ];
};
