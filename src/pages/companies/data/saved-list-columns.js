import i18n from '@/plugins/i18n-dynamic';
import { formatDateWithTime } from '@/utils/date';

export default [
  {
    label: i18n.global.t('name'),
    field: 'name',
    name: 'name',
    align: 'left'
  },
  {
    label: i18n.global.t('subscribers'),
    field: 'subscribersCount',
    align: 'center',
    format: (count) => i18n.global.t('subscribersPlural', { count })
  },
  {
    label: i18n.global.t('alerts'),
    field: 'alertLevels',
    align: 'left',
    format: (value) => value.join(', ')
  },
  {
    label: i18n.global.t('tags'),
    field: 'tags',
    align: 'left',
    name: 'tags'
  },
  {
    label: i18n.global.t('latestAlert'),
    field: 'lastestAlert',
    align: 'right',
    format: (value) => (value ? formatDateWithTime(value) : '–')
  }
];
