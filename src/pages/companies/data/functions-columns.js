import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    name: 'company',
    field: 'company_name',
    label: i18n.global.t('companyName'),
    align: 'left'
  },
  {
    name: 'company_identifier',
    field: 'company_identifier',
    label: i18n.global.t('vat'),
    align: 'left',
    format: (value) => (value ? value.replace('_', ' ') : '–')
  },
  {
    name: 'person_name',
    field: 'person_name',
    label: i18n.global.t('personName'),
    align: 'left',
    format: (value) => value || '–'
  },
  {
    name: 'codeName',
    field: 'codeName',
    label: i18n.global.t('role'),
    align: 'left',
    format: (value, col) => {
      const code = value || col.role_code;
      return code.toUpperCase().replace('_', ' ');
    }
  },
  {
    name: 'roleStartDate',
    field: 'roleStartDate',
    label: i18n.global.t('startDate'),
    align: 'left',
    format: (value) => value || '–'
  }
];
