<script setup>
// import { Dark } from 'quasar';
import { useRoute } from 'vue-router';
import CompanyReport from '@/pages/companies/components/company-report.vue';
import { Previewer } from 'pagedjs';

const route = useRoute();

const ready = async function () {
  if (route.query?.debug) return;

  await new Promise((resolve) => setTimeout(resolve, 500));
  const previewer = new Previewer();
  await previewer.preview();
  window.print();
};

// if (Dark.isActive) {
//   Dark.set(false);
// }
</script>

<template>
  <CompanyReport @ready="ready" />
</template>
