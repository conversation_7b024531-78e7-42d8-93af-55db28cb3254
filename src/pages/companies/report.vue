<template>
  <CompanyReport @ready="ready" />
</template>

<script setup>
import { Dark } from 'quasar';
import { useRoute } from 'vue-router';
import CompanyReport from '@/pages/companies/components/company-report.vue';

const route = useRoute();

if (Dark.isActive) {
  Dark.set(false);
}

const onFinishedPrint = function (printed) {
  window.onfocus = function () {
    window.close();
  };
};

const ready = function () {
  if (route.query?.debug) return;

  onFinishedPrint(window.print());
};
</script>
