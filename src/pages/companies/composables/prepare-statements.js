import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import i18n from '@/plugins/i18n';

const { t, locale } = i18n.global;
const companiesStore = CompaniesStore();
const { latestStatement } = storeToRefs(companiesStore);

const topKeys = [
  'employees',
  'turnover',
  'current_ratio',
  'gross_operating_margin'
];

const iconMapping = {
  employees: 'fa-regular fa-users',
  turnover: 'fa-regular fa-money-bill-transfer',
  current_ratio: 'fa-regular fa-percent',
  gross_operating_margin: 'fa-regular fa-chart-line',
  default: 'fa-regular fa-circle-info'
};

const colorMapping = {
  employees: 'primary',
  turnover: 'blue-grey',
  current_ratio: 'orange',
  gross_operating_margin: 'green',
  default: 'grey'
};

export const statements = computed(() => {
  if (!latestStatement.value) {
    return {
      top: topKeys.map((key) => {
        return {
          isTop: true,
          key,
          icon: iconMapping?.[key] ?? iconMapping.default,
          label: t(key),
          value: 'N/A',
          color: colorMapping?.[key] ?? colorMapping.default
        };
      }),
      others: []
    };
  }

  const overview = latestStatement.value?.overview ?? {};

  const cards = Object.keys(overview)
    .map((key) => {
      const value = overview[key];
      const numberFormat = new Intl.NumberFormat(locale, {
        notation: 'compact',
        maximumFractionDigits: 1
      });
      const isTop = topKeys.includes(key);

      if (typeof value !== 'undefined' && key !== 'date') {
        return {
          isTop,
          key,
          icon: iconMapping?.[key] ?? iconMapping.default,
          label: t(key),
          value: value === null ? 'N/A' : numberFormat.format(value),
          color: colorMapping?.[key] ?? colorMapping.default
        };
      }

      return null;
    })
    .filter(Boolean);

  return {
    top: cards.filter((card) => card.isTop),
    others: cards.filter((card) => !card.isTop)
  };
});
