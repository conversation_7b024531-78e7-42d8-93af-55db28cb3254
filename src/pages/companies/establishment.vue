<script setup>
import { onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useHeadSafe } from '@vueuse/head';
import { useRoute } from 'vue-router';
import { CompaniesStore } from '@/pages/companies/store';
import CompanyHeader from '@/pages/companies/components/company-header.vue';
import CompanyOverview from '@/pages/companies/components/company-overview.vue';
import CompanyContact from '@/pages/companies/components/company-contact.vue';
import CompanyMap from '@/pages/companies/components/company-map.vue';
import CompanySectors from '@/pages/companies/components/company-sectors.vue';
import CompanyNameHistory from '@/pages/companies/components/company-name-history.vue';
import CompanyAddressHistory from '@/pages/companies/components/company-address-history.vue';
import AbstractCard from '@/pages/companies/components/abstract-card.vue';
import Sources from '@/components/commons/sources.vue';

const { t } = useI18n();
const route = useRoute();
const id = route.params?.id;

useHeadSafe({
  title: [t('establishment'), id].join(' - ')
});

const companiesStore = CompaniesStore();
const { loading, establishment } = storeToRefs(companiesStore);

onBeforeMount(() => {
  if (
    establishment.value?.id === null ||
    establishment.value?.id !== route.params.id
  ) {
    companiesStore.loadEstablishment(route.params.id);
  }
});
</script>

<template>
  <div>
    <CompanyHeader
      v-if="establishment"
      :loading="loading"
      :active="establishment.active"
      :identifier="establishment.identifier"
      :company-id="establishment.id"
      :company-name="establishment.name"
      :company-vat="establishment.vatFormatted"
      :parent-identifier="establishment.parentIdentifier"
      :parent-company="establishment.parentCompany"
      :juridical-situation="establishment.juridicalSituation"
      :color="establishment.color"
      :initials="establishment.initials"
      :updated-at="establishment.updatedAt"
      is-establishment
      show-actions
      extended
    />

    <div v-if="establishment" class="row q-col-gutter-md">
      <div class="col-12 col-md-8">
        <CompanyOverview
          :address="establishment.mainAddress.formatted_address"
          :start-date="establishment.orginialStartDate"
          :juridical-situation="establishment.juridicalSituation"
          :main-activity="
            establishment.mainActivity || establishment.firstActivity
          "
          :name="establishment.name"
          :abbreviation="establishment.abbreviation"
          :vat="establishment.vatFormatted"
          :juridical-form="establishment.juridicalForm"
          :legal-person-type="establishment.legalPersonType"
          is-establishment
          class="q-mb-md"
        />
        <CompanyContact
          :address="establishment.mainAddress"
          :addresses="establishment.otherAddresses"
          :website="establishment.contacts.website"
          :email="establishment.contacts.email"
          :mobile="establishment.contacts.phone"
          :phone="establishment.contacts.faxNumber"
          show-actions
          class="q-mb-md"
        />
        <CompanySectors
          :activities="establishment.activities"
          :characteristics="establishment.characteristics"
          :authorizations="establishment.authorizations"
        />
      </div>
      <div class="col">
        <CompanyMap :address="establishment.mainAddress" class="q-mb-md" />

        <CompanyAddressHistory
          :addresses="establishment.addresses"
          class="q-mb-md"
        />

        <CompanyNameHistory :names="establishment.names" class="q-mb-md" />

        <!-- SOURCES -->
        <AbstractCard inline is-flat :title="$t('sources')" no-value>
          <Sources />
        </AbstractCard>
      </div>
    </div>
  </div>
</template>
