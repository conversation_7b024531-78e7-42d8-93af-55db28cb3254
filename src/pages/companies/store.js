import uniq from 'lodash/uniq';
import { defineStore } from 'pinia';
import Api from '@/api';
import router from '@/router';
import { notifyError } from '@/utils/notifications';
import { sortByDate } from '@/utils/date';
import { prepareColumns, prepareRows } from '@/utils/companies/table';
import {
  prepareSeries,
  prepareAxis,
  prepareOptions
} from '@/utils/companies/graph';
import graphOptions from '@/pages/companies/data/graph-options';

const STATEMENT_YEARS_TO_GET = 4;
const STATEMENT_YEARS_TO_GET_REPORT = 8;

export const CompaniesStore = defineStore('companies', {
  state: () => ({
    loading: false,
    company: null,
    establishment: null,
    events: {
      id: null,
      list: [],
      count: 0,
      max: 0,
      params: {}
    },
    selectedPublicationType: null,
    statementIdentifiers: {
      id: null,
      identifiers: [],
      total: 0
    },
    statements: new Map(),
    latestStatement: {
      overview: {},
      data: {}
    },
    recentlyViewed: [],
    companyWatchlists: [],

    socials: {
      companyIdentifier: null,
      columns: [],
      rows: []
    },
    financials: {
      companyIdentifier: null,
      columns: [],
      rows: []
    },
    ratios: {
      companyIdentifier: null,
      columns: [],
      rows: []
    },
    graphs: {
      isReady: false,
      options: graphOptions,
      types: ['line', 'column', 'area'],
      selectedSeries: [],
      series: [],
      settings: {}
    },
    stats: {
      loaded: false,
      companies: 0,
      publications: 0,
      financial_statements: 0
    },
    functions: { id: null, list: [] },
    representatives: { id: null, list: [] },
    establishments: { id: null, list: [] },
    publications: { loading: false, id: null, list: [] },
    publication: { id: null, file: null },
    businessGraph: {
      loading: false,
      companies: [],
      nodes: [],
      edges: []
    }
  }),

  getters: {
    getStatementsSize: (state) => state.statements.size || 0
  },

  actions: {
    loadRecentlyViewed(customerShortId) {
      if (!customerShortId) return;

      const viewed = localStorage.getItem('recently-viewed') || '[]';
      const sorted = sortByDate(JSON.parse(viewed), true);

      this.recentlyViewed = sorted.filter(
        (viewedCompany) => viewedCompany.customerShortId === customerShortId
      );
    },

    addRecentlyViewed({ company, customerShortId }) {
      if (!customerShortId) return;

      this.loadRecentlyViewed(customerShortId);

      const index = this.recentlyViewed.findIndex(
        (viewedCompany) => viewedCompany.id === company.id
      );

      if (index !== -1) {
        // update date of previous recently viewed
        this.recentlyViewed[index].date = new Date().toISOString();
      } else {
        this.recentlyViewed.unshift({
          id: company.id,
          name: company.name,
          customerShortId,
          date: new Date().toISOString(),
          color: company.color
        });
      }

      this.recentlyViewed = sortByDate(this.recentlyViewed.slice(0, 10), true);

      localStorage.setItem(
        'recently-viewed',
        JSON.stringify(this.recentlyViewed)
      );
    },

    clearRentlyViewed(customerShortId) {
      this.loadRecentlyViewed(customerShortId);
      this.recentlyViewed = this.recentlyViewed.filter(
        (viewedCompany) => viewedCompany.customerShortId !== customerShortId
      );
      localStorage.setItem(
        'recently-viewed',
        JSON.stringify(this.recentlyViewed)
      );
    },

    async loadCompany({ id, customerShortId }) {
      if (!id) return;

      this.loading = true;

      const company = await Api.companies.byId(id);

      if (!company || company.isError) {
        notifyError(company);
        this.company = null;
        router.push({ name: 'companies' }); // back to index
      } else {
        this.addRecentlyViewed({ company, customerShortId });
        this.company = company;
      }

      this.loading = false;
    },

    async loadCompanyWatchlists(id) {
      if (!id) return;

      this.loading = true;

      const data = await Api.watchlists.byCompanyId(id);

      if (data.isError) {
        notifyError(data);
      } else {
        this.companyWatchlists = data;
      }

      this.loading = false;
    },

    async loadCompanyPublications(companyIdentifier, publicationType) {
      this.publications.loading = true;
      this.selectedPublicationType = publicationType;

      const data = await Api.companies.publicationsByCompanyIdentifier(
        companyIdentifier,
        publicationType
      );

      if (!data || data.isError) {
        notifyError(data);
      }

      this.publications.id = companyIdentifier;
      this.publications.list = data.isError ? [] : data;
      this.publications.loading = false;
    },

    async loadStatementIdentifiers({
      companyIdentifier,
      context,
      type,
      selection
    }) {
      if (!companyIdentifier) return;

      this.loading = true;

      // avoid multiple identical load
      if (this.statementIdentifiers.id !== companyIdentifier) {
        const data = await Api.companies.statementIdentifiers(
          companyIdentifier
        );

        if (!data || data.isError) {
          notifyError(data);
          this.loading = false;
          this.statementIdentifiers = {
            id: companyIdentifier,
            identifiers: [],
            total: 0
          };
          return;
        }

        this.statementIdentifiers = data;
      }

      const lastThree = this.statementIdentifiers.identifiers
        .slice(-STATEMENT_YEARS_TO_GET)
        .map((statement) => statement.identifier);

      for (const statementIdentifier of lastThree) {
        // avoid load again
        if (!this.statements.get(statementIdentifier)) {
          await this.loadStatement(
            companyIdentifier,
            statementIdentifier,
            true
          );
        }
      }

      if (type === 'table') {
        this[context].columns = prepareColumns(this.statements);
        this[context].rows = this.statements.size
          ? prepareRows(this.statements, context)
          : [];
      }

      // prepare the first selected graph if type === 'graph'
      if (type === 'graph') {
        const selectedSerieId = selection || this.graphs.options[0].id;
        this.selectGraphSerie(selectedSerieId);
        this.graphs.isReady = true;
      }

      this.loading = false;
    },

    selectGraphSerie(id) {
      if (this.graphs.selectedSeries.indexOf(id) !== -1) {
        this.graphs.selectedSeries = this.graphs.selectedSeries.filter(
          (graphId) => graphId !== id
        );
      } else {
        this.graphs.selectedSeries.push(id);
      }

      // re-calculate series
      this.graphs.series = prepareSeries({
        selectedSeries: this.graphs.selectedSeries,
        options: this.graphs.options,
        statements: this.statements
      });

      this.prepareSettings();
    },

    clearSelectionGraphSerie() {
      this.graphs.selectedSeries = [];
    },

    prepareSettings() {
      // re-calculate settings
      this.graphs.settings = prepareOptions(
        this.graphs.series,
        prepareAxis(this.statements)
      );
    },

    updateGraphType(serieId, optionType) {
      this.graphs.series = this.graphs.series.map((serie) => {
        if (serie.id === serieId) {
          serie.type = optionType;
        }
        return serie;
      });
      this.graphs.options = this.graphs.options.map((option) => {
        if (option.id === serieId) {
          option.type = optionType;
        }
        return option;
      });

      // re-calculate settings
      this.prepareSettings();
    },

    async loadStatement(companyIdentifier, statementIdentifier, isExtended) {
      this.loading = true;

      const data = await Api.companies.statementById(
        companyIdentifier,
        statementIdentifier,
        isExtended
      );

      if (!data || data.isError) {
        notifyError(data);
        this.statements.delete(statementIdentifier);
        this.loading = false;
        return;
      }

      this.statements.set(statementIdentifier, data);
      this.loading = false;
    },

    async loadLatestStatement(companyIdentifier) {
      this.loading = true;

      const data = await Api.companies.latestStatement(companyIdentifier);

      if (data.isError) {
        // Note: no notification for this 404 error
        this.latestStatement = null;
      } else {
        this.latestStatement = data;
      }

      this.loading = false;
    },

    async getStats() {
      if (this.stats.loaded) return;

      this.loading = true;
      const data = await Api.stats.getCount();

      if (data.isError) {
        notifyError(data);
        this.stats = {
          loaded: false,
          companies: 0,
          publications: 0,
          financial_statements: 0
        };
      } else {
        this.stats = {
          loaded: true,
          ...data
        };
      }

      this.loading = false;
    },

    async loadFunctions(companyShortId) {
      this.loading = true;
      const data = await Api.companies.getFunctions(companyShortId);

      if (data.isError) {
        notifyError(data);
        this.functions = {
          id: companyShortId,
          list: []
        };
      } else {
        this.functions = {
          id: companyShortId,
          list: data
        };
      }

      this.loading = false;
    },

    async loadRepresentatives(companyShortId) {
      this.loading = true;
      const data = await Api.companies.getRepresentatives(companyShortId);

      if (data.isError) {
        notifyError(data);
        this.representatives = {
          id: companyShortId,
          list: []
        };
      } else {
        this.representatives = {
          id: companyShortId,
          list: data
        };
      }

      this.loading = false;
    },

    async loadEstablishments(companyShortId) {
      this.loading = true;
      const data = await Api.companies.getEstablishments(companyShortId);

      if (data.isError) {
        notifyError(data);
        this.establishments = {
          id: companyShortId,
          list: []
        };
      } else {
        this.establishments = {
          id: companyShortId,
          list: data
        };
      }

      this.loading = false;
    },

    clearList(context) {
      if (
        [
          'functions',
          'establishments',
          'representatives',
          'publications'
        ].includes(context)
      ) {
        this[context].list = [];
      }
    },

    async loadEstablishment(establishmentId) {
      this.loading = true;
      const data = await Api.establishments.byId(establishmentId);

      let company = null;

      if (data.parentIdentifier) {
        company = await Api.companies.byId(data.parentIdentifier);
        this.company = company;
      }

      if (data.isError) {
        notifyError(data);
        this.establishment = null;
      } else {
        this.establishment = data;
      }

      this.loading = false;
    },

    async loadEvents({ companyId, params }) {
      this.loading = true;
      const preparedParams = {};

      if (params.to) {
        preparedParams.to = params.to;
      }

      if (params.from) {
        preparedParams.from = params.from;
      }

      if (Array.isArray(params.eventTypes) && params.eventTypes.length) {
        preparedParams.event_types = params.eventTypes.join(',');
      }

      if (Array.isArray(params.severities) && params.severities.length) {
        preparedParams.event_severities = params.severities.join(',');
      }

      const data = await Api.companies.getEvents({
        companyId,
        params: preparedParams
      });
      this.events.id = companyId;
      this.events.params = params;

      if (data.isError) {
        this.events.list = [];
        this.events.max = 0;
        this.events.count = 0;
        notifyError(data);
      } else {
        this.events.list = data.events;
        this.events.max = data.max;
        this.events.count = data.count;
      }

      this.loading = false;
    },

    async loadPublicationPDF(publicationIdentifier) {
      this.loading = true;
      const data = await Api.companies.getPublicationFile(
        publicationIdentifier
      );

      if (data.isError) {
        notifyError(data);
        this.publication = {
          id: publicationIdentifier,
          file: null
        };
      } else {
        this.publication = {
          id: publicationIdentifier,
          file: data
        };
      }

      this.loading = false;
    },

    async loadCompanyGraph(companyId) {
      this.loading = true;
      const data = await Api.businessGraph.byCompanyId(companyId);

      if (data.isError) {
        notifyError(data);
      } else {
        this.businessGraph = data;
      }

      this.loading = false;
    },

    async loadPersonGraph(params) {
      this.loading = true;
      const data = await Api.businessGraph.byPersonId(params);

      if (data.isError) {
        notifyError(data);
      } else {
        this.businessGraph = data;
      }

      this.loading = false;
    },

    clearGraph() {
      this.businessGraph = {
        businessGraph: {
          loading: false,
          companies: [],
          nodes: [],
          edges: []
        }
      };
    },

    async searchCompanies(companiesIds = []) {
      this.businessGraph.loading = true;

      const companies = await Api.companies.searchByIdentifers(
        uniq(companiesIds)
      );

      if (companies.isError) {
        notifyError(companies);
      } else {
        this.businessGraph.companies = companies;
      }

      this.businessGraph.loading = false;
    },

    async loadReport(companyIdentifier) {
      this.loading = true;

      const data = await Api.companies.getReport(companyIdentifier);

      if (data.isError) {
        notifyError(data);
      } else {
        const {
          company,
          publications,
          establishments,
          statements,
          representatives,
          functions
        } = data;

        this.company = company;
        this.publications = {
          id: companyIdentifier,
          list: publications
        };

        this.graphs = {
          isReady: false,
          options: graphOptions,
          types: ['line'],
          selectedSeries: [],
          series: [],
          settings: {}
        };

        this.latestStatement = {
          overview: statements.overview,
          data: statements.data
        };

        statements.list.forEach((statement) => {
          this.statements.set(statement.identifier, statement);
        });

        // tables
        ['socials', 'ratios', 'financials'].forEach((context) => {
          this[context].companyIdentifier = companyIdentifier;
          this[context].columns = prepareColumns(
            this.statements,
            STATEMENT_YEARS_TO_GET_REPORT
          );
          this[context].rows = this.statements.size
            ? prepareRows(this.statements, context)
            : [];
        });

        // graphs
        graphOptions
          .map((option) => option.id)
          .forEach((selectedGraphSerie) => {
            this.selectGraphSerie(selectedGraphSerie);
          });

        this.graphs.isReady = true;

        this.establishments = {
          id: companyIdentifier,
          list: establishments
        };
        this.representatives = {
          id: companyIdentifier,
          list: representatives
        };
        this.functions = {
          id: companyIdentifier,
          list: functions
        };
      }

      this.loading = false;
    }
  }
});
