<script setup>
import addressesHistoryColumns from '@/pages/companies/data/addresses-history-columns';

defineProps({
  addresses: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
  <q-card flat bordered>
    <q-table
      :columns="addressesHistoryColumns"
      :rows="addresses"
      :no-data-label="$t('noAddressesHistory')"
      :title="$t('addressHistory')"
      title-class="text-uppercase"
      hide-bottom
      flat
    >
      <template #body-cell-map="props">
        <q-td align="center">
          <q-btn
            color="primary"
            outline
            dense
            :href="`https://www.google.com/maps/search/?api=1&query=${props.row.encodedAddress}`"
            target="_blank"
            class="q-pa-sm"
          >
            <q-icon name="fa-regular fa-map-location-dot" size="xs" />
          </q-btn>
        </q-td>
      </template>
    </q-table>
  </q-card>
</template>
