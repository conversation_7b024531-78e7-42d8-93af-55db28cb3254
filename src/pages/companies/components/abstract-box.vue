<script setup>
defineProps({
  title: String,
  icon: String
});
</script>

<template>
  <div class="abstract-box">
    <div class="abstract-box-title">
      <q-icon :name="icon" color="primary" class="q-pr-md" />
      <span class="text-uppercase text-bold">{{ title }}</span>
    </div>

    <div>
      <slot />
    </div>
  </div>
</template>

<style scoped>
.abstract-box {
  position: relative;
  border: 2px solid var(--primaryLight);
  border-radius: 0.5rem;
  padding: 2rem 1rem 1rem;
}

.abstract-box-title {
  display: block;
  position: absolute;
  top: 0;
  left: 1rem;
  transform: translateY(-1rem);
  background-color: var(--primaryLight);
  color: var(--primary);
  padding: 0.5rem 1.5rem;
  font-size: var(--xsmall);
  border-radius: 0.5rem;
}
</style>
