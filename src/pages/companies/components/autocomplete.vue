<script setup>
import { ref, watch } from 'vue';
import { clickOutSide as vClickOutSide } from '@mahdikhashan/vue3-click-outside';
import CompanyAutocompleteItem from '@/components/commons/company-autocomplete-item.vue';
import AutocompleteFilters from '@/pages/companies/components/autocomplete-filters.vue';

const emit = defineEmits(['submit', 'clear', 'toggle-inactive', 'select-type']);

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  companies: {
    type: Array,
    default: () => []
  },
  count: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  }
});

const autocompleteInput = ref('');
const showResults = ref(false);

function closeAutocompleteResults() {
  showResults.value = false;
}

function openAutocompleteResults() {
  showResults.value = true;
}

function toggleFilters() {
  closeAutocompleteResults();
}

function onSubmit() {
  emit('submit', autocompleteInput.value);
}

function directSubmitSearch() {
  emit('direct-submit', autocompleteInput.value);
}

function update() {
  if (props.loading || !autocompleteInput.value) return;
  onSubmit();
}

function onClear() {
  emit('clear');
  closeAutocompleteResults();
}

watch(
  () => props.companies,
  () => {
    showResults.value = props.companies.length > 0;
  }
);
</script>

<template>
  <div
    class="autocomplete q-px-md q-py-xs"
    v-click-out-side="() => closeAutocompleteResults()"
  >
    <q-form class="autocomplete-search" @submit="onSubmit">
      <div class="row items-center justify-between">
        <q-input
          v-model="autocompleteInput"
          :loading="loading"
          :label="$t('searchForEntities')"
          borderless
          debounce="500"
          class="col-grow"
          @update:model-value="update"
          @focus="openAutocompleteResults"
          @keydown.enter="directSubmitSearch"
        >
          <template v-slot:prepend>
            <q-icon
              name="fa-regular fa-magnifying-glass"
              color="primary"
              size="xs"
              class="q-mr-sm"
            />
          </template>
        </q-input>
        <q-btn
          :label="$q.screen.lt.md ? '' : $t('filters')"
          icon-right="fa-regular fa-sliders"
          color="primary"
          flat
          rounded
          tabindex="-1"
          @click.stop="toggleFilters"
        >
          <!-- FILTERS -->
          <q-menu anchor="bottom right" self="top right">
            <AutocompleteFilters @submit="onSubmit" />
          </q-menu>
        </q-btn>
      </div>

      <!-- RESULTS -->
      <q-list
        v-show="showResults"
        class="shadow-24 rounded-borders autocomplete-results"
        no-parent-event
        no-focus
        fit
      >
        <q-list separator>
          <q-item dense class="autocomplete-results-header shadow-3">
            <q-item-section>
              <q-item-label header>
                <span v-if="count">
                  {{ $t('topResultsOf', [count, total]) }}
                </span>
                <span v-else>
                  {{ $t('typeToSearchCompanies') }}
                </span>
              </q-item-label>
            </q-item-section>

            <q-item-section v-if="$q.screen.gt.sm" side>
              <q-btn tabindex="-1" :label="$t('clear')" flat @click="onClear" />
            </q-item-section>

            <q-item-section side>
              <q-btn
                icon="fa-regular fa-xmark"
                round
                flat
                size="sm"
                tabindex="-1"
                @click="closeAutocompleteResults"
              />
            </q-item-section>
          </q-item>

          <q-item v-show="total === 0">
            {{ $t('typeToSearchCompanies') }}
          </q-item>

          <CompanyAutocompleteItem
            v-for="company in companies"
            :key="company.id"
            :id="company.id"
            :name="company.name"
            :vat="company.vatFormatted"
            :is-active="company.isActive"
            :juridical-form="company.juridicalForm"
            :main-activity="company.mainActivity"
            :address="company.address"
            :entity="company.entity"
            :type="company.type"
            with-link
          />

          <q-item>
            <q-item-section>{{ $t('moreCompaniesTooltip') }}</q-item-section>
            <q-item-section side>
              <q-btn
                :to="
                  autocompleteInput
                    ? `/search/companies?q=${autocompleteInput}`
                    : '/search'
                "
                :label="$q.screen.lt.lg ? '' : $t('search')"
                icon="fa-regular fa-search"
                color="primary"
                outline
              />
            </q-item-section>
          </q-item>
        </q-list>
      </q-list>
    </q-form>
  </div>
</template>

<style scoped>
.autocomplete {
  display: flex;
  border: 3px solid var(--primary);
  border-radius: 0.75rem;
}

.icon-wrapper {
  display: flex;
  background-color: var(--primaryLight);
}

.autocomplete-select {
  position: relative;
  padding-right: 1rem;
}

.autocomplete-select::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background-color: var(--primaryLight);
}

.autocomplete-selected {
  font-weight: bold;
  color: var(--primary);
}

.autocomplete-search {
  flex-grow: 1;
}

.autocomplete-search :deep(.q-field .q-field__inner) {
  border-radius: 4px;
}

.autocomplete-results {
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  top: 100%;
  max-height: 400px;
  background-color: var(--white);
  overflow-y: auto;
  margin-top: 0.5rem;
}

.autocomplete-results-header {
  position: sticky;
  top: 0;
  z-index: 1;
  height: 70px;
  backdrop-filter: blur(10px);
  background-color: rgba(var(--whiteRGB), 0.5);
  border-bottom: 1px solid var(--white);
}
</style>
