<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_EXPORT } from '@/utils/features';
import CompanyExport from '@/pages/companies/components/company-export.vue';

const props = defineProps({
  company: Object,
  address: Object,
  addresses: Array,
  website: String,
  email: String,
  mobile: String,
  phone: String,
  showActions: Boolean
});

const { t } = useI18n();

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForExport = computed(() =>
  features.value.includes(COMPANY_EXPORT)
);

const toggleMoreAddresses = ref(false);
const contacts = computed(() => [
  {
    id: 'website',
    label: t('website'),
    icon: 'fa-regular fa-laptop',
    value: props.website || '—',
    formatted: props.website ? props.website.replace(/(^\w+:|^)\/\//, '') : null
  },
  {
    id: 'email',
    label: t('email'),
    icon: 'fa-regular fa-envelope',
    value: props.email || '—'
  },
  {
    id: 'mobile',
    label: t('mobile'),
    icon: 'fa-regular fa-mobile',
    value: props.mobile || '—'
  },
  {
    id: 'phone',
    label: t('phone'),
    icon: 'fa-regular fa-fax',
    value: props.phone || '—'
  }
]);
</script>

<template>
  <q-card flat bordered>
    <q-card-section class="q-px-lg flex align-center justify-between">
      <h2 class="text-h6 text-bold q-ma-none text-uppercase">
        {{ $t('contact') }}
      </h2>
      <CompanyExport
        v-if="isAllowedForExport"
        v-show="showActions"
        :company="company"
      />
    </q-card-section>

    <q-separator />

    <q-card-section>
      <div class="row">
        <div class="col-4 q-pr-sm">
          <h4 class="text-body2 q-my-xs text-bold">{{ $t('address') }}</h4>
          <p>{{ address.formatted_address }}</p>
          <q-btn
            v-if="addresses.length"
            flat
            color="primary"
            @click="toggleMoreAddresses = !toggleMoreAddresses"
          >
            <q-icon
              name="fa-regular fa-arrow-turn-down-right"
              class="q-pr-sm"
              size="xs"
            />
            {{ $t('moreAddresses', addresses.length) }}
          </q-btn>
        </div>

        <!-- CONTACTS -->
        <div class="col-8">
          <div class="flex">
            <q-item
              v-for="contact in contacts"
              :key="contact.icon"
              class="contact-item"
            >
              <q-item-section avatar class="contact-icon">
                <q-icon :name="contact.icon" color="primary" size="xs" />
              </q-item-section>
              <q-item-section class="contact-info">
                <q-item-label class="text-bold">
                  {{ contact.label }}
                </q-item-label>

                <q-item-label
                  v-if="contact.id === 'website'"
                  class="contact-value"
                >
                  <a
                    v-if="contact.formatted"
                    :href="contact.value"
                    target="_blank"
                  >
                    {{ contact.formatted }}
                  </a>
                  <span v-else>{{ contact.value }}</span>
                </q-item-label>

                <q-item-label v-else class="contact-value">
                  {{ contact.value }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </div>
        </div>
      </div>

      <!-- Other addresses -->
      <div class="q-mt-sm" v-show="toggleMoreAddresses">
        <q-list bordered separator>
          <q-item v-for="address of addresses" :key="address.id">
            <q-item-section avatar>
              <q-btn
                color="primary"
                outline
                dense
                :href="`https://www.google.com/maps/search/?api=1&query=${address.encodedAddress}`"
                target="_blank"
                class="q-pa-sm"
              >
                <q-icon name="fa-regular fa-map-location-dot" size="xs" />
              </q-btn>
            </q-item-section>
            <q-item-section>{{ address.formatted_address }}</q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-card-section>
  </q-card>
</template>

<style scoped>
.contact-item {
  width: 50%;
}

.contact-icon {
  min-width: 0;
}

.contact-info {
  font-size: var(--small);
}

.contact-value {
  word-break: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
