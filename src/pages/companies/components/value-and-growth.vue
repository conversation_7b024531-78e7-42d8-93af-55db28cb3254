<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  currency: String,
  value: [Number, String],
  ratio: Number,
  dense: Boolean
});

const { locale } = useI18n();

const formattedValue = computed(() => {
  const options = {
    notation: 'compact',
    maximumFractionDigits: 1
  };

  if (props.currency && props.currency !== 'PURE') {
    options.style = 'currency';
    options.currency = props.currency;
  }

  return typeof props.value === 'number'
    ? Intl.NumberFormat(locale.value, options).format(props.value)
    : 'N/A';
});
</script>

<style scoped>
.source-value {
  display: flex;
  font-size: var(--normal);
  white-space: nowrap;
  flex-direction: column;
}

.source-value.dense {
  font-size: var(--xsmall);
}

.source-value.dense .source-value {
  font-size: var(--xsmall);
}

@media screen and (min-width: 600px) {
  .source-value {
    flex-direction: row;
    align-items: center;
  }
}

.source-ratio {
  font-size: var(--small);
}

@media screen and (min-width: 600px) {
  .source-ratio {
    padding-left: 1rem;
  }
}
</style>
