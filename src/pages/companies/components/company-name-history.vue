<script setup>
import namesHistoryColumns from '@/pages/companies/data/names-history-columns';

defineProps({
  names: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
  <q-card flat bordered>
    <q-table
      :columns="namesHistoryColumns"
      :rows="names"
      :no-data-label="$t('noNamesHistory')"
      :title="$t('nameHistory')"
      title-class="text-uppercase"
      hide-bottom
      flat
    />
  </q-card>
</template>
