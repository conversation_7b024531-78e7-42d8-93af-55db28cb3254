<script setup>
import { formatDate } from '@/utils/date';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';

defineProps({
  isMobile: Boolean,
  severity: String,
  type: String,
  diffFrom: String,
  diffTo: String,
  date: String
});
</script>

<template>
  <q-timeline-entry :color="severityColors(severity)" class="timeline-entry">
    <template #title>
      {{ $t(type) }}
    </template>

    <template #subtitle>
      <div class="date">{{ formatDate(date, $i18n.locale) }}</div>
      <q-chip :color="severityColors(severity)" class="text-white" size="sm">
        <div class="ellipsis">{{ $t(severity) }}</div>
      </q-chip>
    </template>

    <q-card class="row q-pa-sm timeline-diff" flat>
      <div v-if="diffFrom" class="timeline-diff-from">
        <div class="text-caption text-blue-grey">{{ $t('before') }}</div>
        <div class="font-size-small">{{ diffFrom }}</div>
      </div>

      <div v-if="diffFrom" class="timeline-diff-separator">
        <q-icon
          :name="`fa-regular fa-chevron-${isMobile ? 'down' : 'right'}`"
        />
      </div>

      <div class="timeline-diff-to">
        <div class="text-caption text-blue-grey">
          {{ diffFrom ? $t('after') : $t('newEntry') }}
        </div>
        <div class="font-size-small">{{ diffTo || '/' }}</div>
      </div>
    </q-card>
  </q-timeline-entry>
</template>

<style scoped>
.timeline-entry {
  padding-bottom: 1.25rem;
}
.timeline-entry :deep(.q-timeline__title) {
  margin-bottom: 0.5rem;
}

.timeline-entry :deep(.q-timeline__subtitle) {
  width: 33%;
}

.timeline-entry :deep(.q-timeline__dot) {
  left: 33%;
}

.timeline-entry :deep(.q-timeline__content) {
  width: 66%;
}

.timeline-diff {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

@media (min-width: 600px) {
  .timeline-diff {
    flex-wrap: nowrap;
  }
}

.timeline-diff-separator {
  opacity: 0.5;
  padding: 0 2rem;
}

.timeline-diff-from,
.timeline-diff-to {
  width: 100%;
  text-align: center;
}

@media (min-width: 600px) {
  .timeline-diff-from,
  .timeline-diff-to {
    width: auto;
  }
}

.timeline-diff-to:only-child {
  width: 100%;
}

.date {
  line-height: 1.3;
  padding-top: 0.1rem;
}
</style>
