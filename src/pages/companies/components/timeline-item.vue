<script setup>
import { formatDate } from '@/utils/date';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';

defineProps({
  isMobile: Boolean,
  severity: String,
  type: String,
  diffFrom: String,
  diffTo: String,
  date: String
});
</script>

<template>
  <q-timeline-entry :color="severityColors(severity)" class="timeline-entry">
    <template #title>
      {{ $t(type) }}
    </template>

<style scoped>
.timeline-entry {
  padding-bottom: 1.25rem;
}
.timeline-entry :deep(.q-timeline__title) {
  margin-bottom: 0.5rem;
}

.timeline-entry :deep(.q-timeline__subtitle) {
  width: 33%;
}

.timeline-entry :deep(.q-timeline__dot) {
  left: 33%;
}

.timeline-entry :deep(.q-timeline__content) {
  width: 66%;
}

.timeline-diff {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

@media (min-width: 600px) {
  .timeline-diff {
    flex-wrap: nowrap;
  }
}

.timeline-diff-separator {
  opacity: 0.5;
  padding: 0 2rem;
}

.timeline-diff-from,
.timeline-diff-to {
  width: 100%;
  text-align: center;
}

@media (min-width: 600px) {
  .timeline-diff-from,
  .timeline-diff-to {
    width: auto;
  }
}

.timeline-diff-to:only-child {
  width: 100%;
}

.date {
  line-height: 1.3;
  padding-top: 0.1rem;
}
</style>
