<script setup>
defineProps({
  isMobile: Boolean,
  events: Array,
  max: Number,
  inView: Array
});
</script>

<template>
  <div
    v-if="events"
    ref="summary"
    class="years-summary row justify-en"
    :style="`top: calc(50% - ${16 * (events.length || 0)}px)`"
  >
    <q-list>
      <q-item
        v-for="{ year, count } in events"
        :key="year"
        :to="{ hash: `#year-${year}` }"
        :class="inView.includes(year) ? 'text-primary' : 'text-grey'"
        :title="count"
        dense
      >
        <q-item-section v-if="!isMobile" avatar class="text-right">
          <div
            class="years-summary-bar"
            :style="`width: ${(count / max) * 100}%`"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ year }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<style scoped>
.years-summary {
  display: flex;
  justify-content: end;
  position: sticky;
  top: 1rem;
}

.years-summary-bar {
  display: flex;
  align-self: end;
  height: 0.25rem;
  border-radius: 0.125rem;
  background-color: currentColor;
}
</style>
