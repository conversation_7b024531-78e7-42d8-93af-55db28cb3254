<script setup>
import { ref } from 'vue';

const activeTab = ref('userActivity');
</script>

<template>
  <section class="q-pa-md">
    <q-tabs
      align="left"
      dense
      :breakpoint="0"
      v-model="activeTab"
      active-color="primary"
    >
      <q-tab name="userActivity">
        {{ $t('userActivity') }}
      </q-tab>
      <q-tab name="systemEvents">
        {{ $t('systemEvents') }}
      </q-tab>
    </q-tabs>
    <q-separator />

    <q-tab-panels v-model="activeTab" class="timelines q-pt-md">
      <q-tab-panel name="userActivity">
        <q-timeline color="primary">
          <q-timeline-entry :title="$t('noActivityYet')" />
        </q-timeline>
      </q-tab-panel>
    </q-tab-panels>
  </section>
</template>

<style scoped>
.timelines {
  background-color: transparent;
}
</style>
