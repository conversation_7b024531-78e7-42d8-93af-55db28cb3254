<script setup>
const props = defineProps({
  company: Object
});

function downloadCSV() {
  const preparedCompanyData = {
    legalName: props.company.name,
    formattedVat: props.company.vatFormatted,
    creationDate: props.company.startDate,
    juridicalFormLong: props.company.juridicalForm,
    juridicalSituation: props.company.juridicalSituation,
    typeOfEnterprise:
      props.company.legalRepresentativePersons > 0 ? 'Legal person' : 'other',
    mainNace: props.company.mainActivity,
    streetAddress: props.company.mainAddress.streets[0].value,
    houseNumber: props.company.mainAddress.street_number,
    postOfficeBoxNumber: props.company.mainAddress.box,
    postalCode: props.company.mainAddress.postal_code,
    locality: props.company.mainAddress.locality,
    country: props.company.mainAddress.country,
    email: props.company.contacts.email,
    phone: props.company.contacts.phone,
    fax: props.company.contacts.faxNumber,
    website: props.company.contacts.website,
    dataUrl: `https://data.be/companies/${props.company.identifier}`
  };
  const headers = Object.keys(preparedCompanyData);
  const replacer = (_key, value) => (value === null ? '' : value);
  const csv = [
    headers.join(';'),
    Object.keys(preparedCompanyData)
      .map((fieldName) =>
        JSON.stringify(preparedCompanyData[fieldName], replacer)
      )
      .join(';')
  ].join('/r/n');

  const blob = new Blob([csv], { type: 'text' });
  const fileUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('download', `${props.company.name}.csv`);
  link.href = fileUrl;
  link.click();
}
</script>

<template>
  <q-btn color="primary" rounded unelevated>
    <q-icon name="fa-regular fa-download" size="xs" />
    <span class="q-px-sm">{{ $t('export') }}</span>
    <q-icon name="fa-regular fa-chevron-down" size="xs" />
    <q-menu
      max-height="200px"
      transition-show="jump-down"
      transition-hide="jump-up"
    >
      <q-list style="min-width: 100px">
        <q-item clickable v-close-popup @click="downloadCSV">
          <q-item-section avatar>
            <q-icon name="fa-regular fa-file-csv" />
          </q-item-section>
          <q-item-section>{{ $t('exportToCSV') }}</q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>
