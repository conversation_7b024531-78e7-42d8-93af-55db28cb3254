<script setup>
import { computed, onMounted, defineAsyncComponent } from 'vue';
import { useI18n } from 'vue-i18n';
import { formatter } from '@/utils/companies/graph';

const VueApexCharts = defineAsyncComponent(() => import('vue3-apexcharts'));

const { locale } = useI18n();

const props = defineProps({
  settings: Object,
  series: Array,
  chartId: String,
  name: String
});

const updatedSettings = computed(() => {
  if (!props.name) return props.settings;

  return {
    ...props.settings,

    chart: {
      ...props.settings.chart,
      id: props.chartId
    },

    title: {
      text: props.name
    },

    yaxis: {
      labels: {
        formatter: (value) => formatter(value, props.series[0], locale)
      }
    }
  };
});

onMounted(() => {
  window.dispatchEvent(new Event('resize'));
});
</script>

<template>
  <div>
    <VueApexCharts
      :key="name"
      :options="updatedSettings"
      :series="series"
      :height="400"
      type="line"
    />
  </div>
</template>
