<template>
  <q-card class="selection-sidebar">
    <q-list dense>
      <q-item :to="makeLink(selectedNode)">
        <!-- ICON -->
        <q-item-section avatar>
          <q-item-label>
            <q-avatar
              :class="['graph-bg', selectedNode.type]"
              :icon="selectedNode.icon"
              text-color="white"
              size="md"
            />
          </q-item-label>
        </q-item-section>
        <!-- LABEL -->
        <q-item-section>
          <q-item-label :class="['graph-color', selectedNode.type]">
            {{ selectedNode.originalLabel }}
          </q-item-label>
        </q-item-section>

        <q-item-section avatar>
          <q-btn
            icon="fa-regular fa-close"
            round
            flat
            @click.stop.prevent="$emit('deselect')"
          />
        </q-item-section>
      </q-item>

      <!-- Infos -->
      <q-separator v-if="selectedNode.infos.length" spaced />
      <q-linear-progress v-if="loading" indeterminate />

      <div v-if="!loading">
        <q-item v-for="{ label, value } in selectedNode.infos" :key="label">
          <q-item-section>
            <q-item-label caption>{{ $t(label) }}</q-item-label>

            <q-item-label v-if="label !== 'companies'">
              {{ value }}
            </q-item-label>

            <!-- Companies links -->
            <template v-else>
              <q-list dense class="q-pt-sm">
                <q-item
                  v-for="val in value"
                  :key="val.id"
                  :to="`/companies/${val.id}`"
                  style="padding-left: 0.25rem"
                >
                  <q-item-section avatar style="min-width: 0">
                    <q-avatar
                      :style="{ backgroundColor: getAvatarColor(val.id) }"
                      text-color="white"
                      size="sm"
                      rounded
                    >
                      {{ getInitials(val.name || val.id) }}
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label caption>
                      <strong>
                        {{ val.name !== val.id ? val.name : '' }}
                      </strong>
                      ({{ val.id.replace('_', '') }})
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </template>
          </q-item-section>
        </q-item>
      </div>

      <!-- Sources -->
      <q-separator v-if="selectedNode.sources.length" spaced />
      <q-item>
        <q-item-section>
          <q-item-label caption>{{ $t('sources') }}</q-item-label>
        </q-item-section>
      </q-item>
      <q-item dense>
        <q-item-section>
          <q-item-label caption>
            {{ selectedNode.sources.map((source) => $t(source)).join(', ') }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-card>
</template>

<script setup>
import { getAvatarColor, getInitials } from '@/utils/avatar';

defineProps({
  loading: Boolean,
  selectedNode: Object
});

defineEmits(['deselect']);

function makeLink(selectedNode) {
  if (selectedNode?.type === 'company') {
    return `/companies/${selectedNode.id}`;
  }

  if (selectedNode.type === 'person') {
    const companies = selectedNode.infos
      .filter((info) => info.label === 'companies')
      .map((info) => info.value.map((company) => company.id))
      .flat();

    return `/persons/${selectedNode.originalLabel}?companies=${companies}`;
  }

  return null;
}
</script>

<style scoped>
.selection-sidebar {
  --graph-company: #1f72c1;
  --graph-person: #6ab759;
  --graph-address: #fdcc62;
  --graph-inactive: #283d50;

  position: absolute;
  top: 8rem;
  right: 4rem;
  max-width: 20rem;
  z-index: 1;
  padding: 1rem;
  max-height: calc(100vh - 10rem);
  overflow-y: auto;
}

.graph-bg.company {
  background-color: var(--graph-company);
}

.graph-bg.person {
  background-color: var(--graph-person);
}

.graph-bg.address {
  background-color: var(--graph-address);
}

.graph-bg.external {
  background-color: var(--graph-inactive);
}

.graph-color.company {
  color: var(--graph-company);
}

.graph-color.person {
  color: var(--graph-person);
}

.graph-color.address {
  color: var(--graph-address);
}

.graph-color.external {
  color: var(--graph-inactive);
}
</style>
