<script setup>
import { ref } from 'vue';

const activeTab = ref('notes');
const notes = ref('');
</script>

<template>
  <section class="company-notes q-pa-md">
    <q-tabs
      align="left"
      dense
      :breakpoint="0"
      v-model="activeTab"
      active-color="primary"
    >
      <q-tab name="notes">
        {{ $t('notes') }}
      </q-tab>
    </q-tabs>
    <q-separator />

    <q-form class="q-pa-md">
      <q-input
        v-model="notes"
        type="textarea"
        :placeholder="$t('notePlaceholder')"
        outlined
      />
      <div class="row justify-end q-pt-md q-gutter-md">
        <q-btn outline>{{ $t('cancel') }}</q-btn>
        <q-btn outline color="primary">{{ $t('save') }}</q-btn>
      </div>
    </q-form>
  </section>
</template>

<style scoped>
.company-notes {
  background-color: var(--white);
  border: 1px solid var(--grey);
  border-radius: 0.5rem;
}
</style>
