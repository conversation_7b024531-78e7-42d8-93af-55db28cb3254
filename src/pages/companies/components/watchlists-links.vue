<template>
  <q-card>
    <q-card-section>
      <q-linear-progress v-show="loading" indeterminate color="primary" />

      <div v-if="watchlists.length === 0" class="text-center">
        <q-icon
          name="fa-regular fa-bell-on"
          size="lg"
          color="grey"
          class="q-ma-md"
        />

        <p>{{ $t('noWatchlistCreated') }}</p>

        <q-btn
          to="/monitoring/new"
          :label="$t('newWatchlist')"
          color="primary"
          outline
        />
      </div>

      <q-list separator class="links-list">
        <q-item
          v-for="watchlist in watchlists"
          :key="watchlist.id"
          :to="`/monitoring/${watchlist.id}/alerts`"
        >
          <q-item-section avatar>
            <q-icon name="fa-regular fa-list-ul" color="primary" />
          </q-item-section>
          <q-item-section>
            <q-item-label>
              {{ watchlist.name }}
            </q-item-label>
            <q-item-label v-if="watchlist.lastestAlert" caption>
              {{ $t('latestAlert') }}:
              {{ format(watchlist.lastestAlert, $i18n.locale) }}
            </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon name="fa-regular fa-chevron-right" color="grey" />
          </q-item-section>
        </q-item>
      </q-list>

      <q-btn
        v-if="watchlists.length"
        :label="$t('accessToAllWatchlists')"
        icon="fa-regular fa-monitor-waveform"
        to="/monitoring"
        class="q-mt-md"
        color="grey"
        size="sm"
        no-caps
        flat
      />
    </q-card-section>
  </q-card>
</template>

<script setup>
import { format } from 'timeago.js';

defineProps({
  loading: Boolean,
  watchlists: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
.links-list {
  max-height: 330px;
  overflow: auto;
}
</style>
