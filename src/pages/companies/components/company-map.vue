<script setup>
import { computed } from 'vue';

const props = defineProps({
  address: Object
});

const apiKey = import.meta.env.VITE_GOOGLE_API_KEY;
const encodedAddress = computed(() =>
  encodeURI(props.address.formatted_address)
);
</script>

<template>
  <q-card bordered class="google-map-widget">
    <iframe
      title="google-map"
      class="google-map-iframe"
      :src="`https://www.google.com/maps/embed/v1/place?key=${apiKey}&q=${encodedAddress}`"
      referrerpolicy="no-referrer-when-downgrade"
      height="300"
      style="border: 0"
      loading="lazy"
    />
  </q-card>
</template>

<style scoped>
.google-map-widget {
  overflow: hidden;
}
.google-map-iframe {
  display: block;
  min-height: 400px;
  height: 100%;
  width: 100%;
}
</style>
