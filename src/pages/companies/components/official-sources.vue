<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Sources from '@/components/commons/sources.vue';

const props = defineProps({
  loading: Boolean,
  companies: Number,
  publications: Number,
  statements: Number
});

const { t, locale } = useI18n();
const formatter = new Intl.NumberFormat(locale);

const stats = computed(() => {
  return [
    {
      icon: 'fa-regular fa-buildings',
      label: t('companiesCount'),
      count: formatter.format(props.companies)
    },
    {
      icon: 'fa-regular fa-file-contract',
      label: t('publicationCount'),
      count: formatter.format(props.publications)
    },
    {
      icon: 'fa-regular fa-chart-line-up-down',
      label: t('accountCount'),
      count: formatter.format(props.statements)
    }
  ];
});
</script>

<template>
  <q-card>
    <q-card-section>
      <q-list>
        <q-item v-for="stat in stats" :key="stat.label">
          <q-item-section avatar>
            <q-icon :name="stat.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label caption>{{ stat.label }}</q-item-label>
            <q-item-label>
              <q-circular-progress
                v-show="loading"
                indeterminate
                color="primary"
                size="18px"
              />
              <span v-show="!loading">{{ stat.count }}</span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
      <q-separator class="q-mb-md" />
      <Sources />
    </q-card-section>
  </q-card>
</template>
