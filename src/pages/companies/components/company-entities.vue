<script setup>
import AbstractCard from '@/pages/companies/components/abstract-card.vue';

defineProps({
  identifier: String,
  legalRepresentativePersons: Number,
  establishmentUnits: Number
});
</script>

<template>
  <div>
    <!-- LINKED ENTITIES -->
    <AbstractCard
      :title="$t('linkedEntities')"
      :value="establishmentUnits || $t('none')"
      icon="fa-regular fa-code-branch"
      icon-color="warning"
      icon-bg-color="bg-amber-1"
    >
      <q-btn
        flat
        round
        size="sm"
        color="primary"
        icon="fa-regular fa-chevron-right"
        :to="`/companies/${identifier}/establishments`"
      />
    </AbstractCard>

    <!-- LEGAL REPRESENTATIVES -->
    <AbstractCard
      :title="$t('legalRepresentatives')"
      :value="legalRepresentativePersons || $t('none')"
      icon="fa-regular fa-user-tie"
    >
      <q-btn
        flat
        round
        size="sm"
        color="primary"
        icon="fa-regular fa-chevron-right"
        :to="`/companies/${identifier}/legal-representatives`"
      />
    </AbstractCard>
  </div>
</template>
