<script setup>
import { watch } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import AbstractCard from '@/pages/companies/components/abstract-card.vue';

const props = defineProps({
  identifier: String,
  currentRatio: Number,
  currentRatioGrowth: Number,
  employees: Number,
  netCash: Number,
  netCashGrowth: Number,
  netCashCurrency: String,
  returnOnEquity: Number,
  returnOnEquityGrowth: Number,
  cashFlowToDebt: Number,
  cashFlowToDebtGrowth: Number,
  cashFlowToDebtCurrency: String,
  grossOperatingMargin: Number,
  grossOperatingMarginGrowth: Number,
  grossOperatingMarginCurrency: String,
  accountingYear: Number,
  isPrint: Boolean
});

const companyStore = CompaniesStore();
const { loading, latestStatement } = storeToRefs(companyStore);
const route = useRoute();

if (!props.isPrint) {
  companyStore.loadLatestStatement(props.identifier);
}

watch(
  () => route.params.id,
  async (newIdentifier) => await companyStore.loadLatestStatement(newIdentifier)
);
</script>

<template>
  <q-banner v-if="!latestStatement && !loading" dense class="bg-amber-1">
    <template v-slot:avatar>
      <q-icon
        name="fa-regular fa-exclamation-triangle"
        color="warning"
        size="xl"
      />
    </template>

<style scoped>
.print .col {
  width: calc(50% - 16px) !important;
}

.print :deep(.source-value) {
  flex-direction: row !important;
  padding-left: 0.5rem;
}
</style>
