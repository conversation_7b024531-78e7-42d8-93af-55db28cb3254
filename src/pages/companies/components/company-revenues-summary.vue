<template>
  <q-banner v-if="!latestStatement && !loading" dense class="bg-amber-1">
    <template v-slot:avatar>
      <q-icon
        name="fa-regular fa-exclamation-triangle"
        color="warning"
        size="xl"
      />
    </template>
    {{ $t('latestStatementFailToLoad') }}.
  </q-banner>

  <q-linear-progress
    v-if="loading"
    indeterminate
    color="primary"
    class="q-my-lg"
  />

  <div v-if="latestStatement" :class="['row q-gutter-md', { print: isPrint }]">
    <div class="col">
      <AbstractCard
        :title="$t('currentRatio')"
        :value="currentRatio"
        :ratio="currentRatioGrowth"
        icon="fa-regular fa-scale-balanced"
      >
        <q-btn
          to="./graphs#currentRatio"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>

      <AbstractCard
        :title="$t('cashFlowInRelation')"
        :value="cashFlowToDebt"
        :ratio="cashFlowToDebtGrowth"
        :currency="cashFlowToDebtCurrency"
        icon="fa-regular fa-money-check-dollar"
      >
        <q-btn
          to="./graphs#cashFlowToDebt"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>

      <AbstractCard
        :title="$t('returnOnEquity')"
        :value="returnOnEquity"
        :ratio="returnOnEquityGrowth"
        icon="fa-regular fa-right-left"
      >
        <q-btn
          to="./graphs#returnOnEquity"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>
    </div>
    <div class="col">
      <AbstractCard
        :title="$t('numberOfEmployees')"
        :value="employees || 'N/A'"
        icon="fa-regular fa-user"
      >
        <q-btn
          to="./graphs#employees"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>

      <AbstractCard
        :title="$t('netCashRatio')"
        :value="netCash"
        :ratio="netCashGrowth"
        :currency="netCashCurrency"
        icon="fa-regular fa-hand-holding-dollar"
      >
        <q-btn
          to="./graphs#netCash"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>

      <AbstractCard
        :title="$t('grossOperation')"
        :value="grossOperatingMargin"
        :ratio="grossOperatingMarginGrowth"
        :currency="grossOperatingMarginCurrency"
        icon="fa-regular fa-chart-line"
      >
        <q-btn
          to="./graphs#grossOperatingMargin"
          round
          flat
          size="sm"
          icon="fa-regular fa-chevron-right"
          color="primary"
        />
      </AbstractCard>
    </div>
  </div>
  <p class="text-right text-caption">
    {{ $t('accountingYear') }}: {{ accountingYear }}
  </p>
</template>

<script setup>
import { watch } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import AbstractCard from '@/pages/companies/components/abstract-card.vue';

const props = defineProps({
  identifier: String,
  currentRatio: Number,
  currentRatioGrowth: Number,
  employees: Number,
  netCash: Number,
  netCashGrowth: Number,
  netCashCurrency: String,
  returnOnEquity: Number,
  returnOnEquityGrowth: Number,
  cashFlowToDebt: Number,
  cashFlowToDebtGrowth: Number,
  cashFlowToDebtCurrency: String,
  grossOperatingMargin: Number,
  grossOperatingMarginGrowth: Number,
  grossOperatingMarginCurrency: String,
  accountingYear: Number,
  isPrint: Boolean
});

const companyStore = CompaniesStore();
const { loading, latestStatement } = storeToRefs(companyStore);
const route = useRoute();

if (!props.isPrint) {
  companyStore.loadLatestStatement(props.identifier);
}

watch(
  () => route.params.id,
  async (newIdentifier) => await companyStore.loadLatestStatement(newIdentifier)
);
</script>

<style scoped>
.print .col {
  width: calc(50% - 16px) !important;
}

.print :deep(.source-value) {
  flex-direction: row !important;
  padding-left: 0.5rem;
}
</style>
