<script setup>
import { useI18n } from 'vue-i18n';
import { format } from 'timeago.js';

defineProps({
  recentlyViewed: {
    type: Array,
    default: () => []
  }
});

const { locale } = useI18n();
</script>

<template>
  <q-card>
    <q-card-section>
      <div v-if="recentlyViewed.length === 0" class="text-center">
        <q-icon
          name="fa-regular fa-building-magnifying-glass"
          size="lg"
          color="grey"
          class="q-ma-md"
        />
        <p>{{ $t('noCompaniesSearched') }}</p>
      </div>

      <q-list separator>
        <q-item
          v-for="viewed in recentlyViewed"
          :key="viewed.id"
          :to="`/companies/${viewed.id}`"
        >
          <q-item-section avatar>
            <q-avatar
              :style="{ backgroundColor: viewed.color || 'var(--primary)' }"
            >
              <q-icon name="fa-regular fa-building" color="white" size="sm" />
            </q-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label>
              {{ viewed.name }}
            </q-item-label>
            <q-item-label caption>
              {{ $t('lastViewed') }}:
              {{ format(viewed.date, locale) }}
            </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon name="fa-regular fa-chevron-right" color="grey" />
          </q-item-section>
        </q-item>
      </q-list>

      <q-btn
        v-show="recentlyViewed.length"
        :label="$t('clearAllRecentlyViewed')"
        icon="fa-regular fa-close"
        class="q-mt-md full-width"
        color="grey"
        size="sm"
        no-caps
        flat
        @click="$emit('clear')"
      />
    </q-card-section>
  </q-card>
</template>
