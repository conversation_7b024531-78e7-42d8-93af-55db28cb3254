<script setup>
import { ref, computed } from 'vue';
import { format } from 'timeago.js';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_PDF_REPORT } from '@/utils/features';
import CompanySidebar from '@/pages/companies/components/company-sidebar.vue';
import WatchlistMenu from '@/pages/companies/components/watchlist-menu.vue';

const props = defineProps({
  loading: Boolean,
  isMobile: Boolean,
  showActions: Boolean,
  watchlistsLoading: Boolean,
  extended: Boolean,
  identifier: String,
  companyName: String,
  companyVat: String,
  companyId: String,
  parentCompany: String,
  parentIdentifier: String,
  juridicalSituation: String,
  updatedAt: String,
  color: String,
  initials: String,
  active: Boolean,
  isEstablishment: Boolean,
  watchlists: {
    type: Array,
    default: () => []
  }
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowForReports = computed(() =>
  features.value.includes(COMPANY_PDF_REPORT)
);

const emit = defineEmits(['remove-from-list', 'add-to-list']);

const router = useRouter();
const { locale } = useI18n();
const mobileMenu = ref(false);

function onMobileMenu() {
  mobileMenu.value = !mobileMenu.value;
}

const updatedDateTimeAgo = computed(() => {
  if (!props.updatedAt || !props.extended) return null;

  return format(props.updatedAt, locale);
});

function isCompanyInList({ watchlistShortId, companyIdentifier }) {
  const watchlist = props.watchlists.find(
    (watchlist) => watchlist.id === watchlistShortId
  );

  if (!watchlist) return false;

  return (
    watchlist.watchedCompanies.findIndex(
      (watchedCompanyId) => watchedCompanyId === companyIdentifier
    ) !== -1
  );
}

function toggleToList({ watchlistShortId, companyIdentifier }) {
  const event = isCompanyInList({ watchlistShortId, companyIdentifier })
    ? 'remove-from-list'
    : 'add-to-list';

  emit(event, { watchlistShortId, companyIdentifier });
}

function openReport() {
  const routeData = router.resolve({
    name: 'company-report',
    params: {
      id: props.companyId
    }
  });
  window.open(routeData.href, '_blank', 'width=300', 'heigth=300');
}
</script>

<template>
  <header :class="['company-header q-mb-md', { extended }]">
    <span class="company-header-bg-color" :style="{ backgroundColor: color }" />
    <div class="header-wrapper">
      <q-avatar
        class="header-icon shadow-3"
        :style="{ backgroundColor: color }"
        rounded
        :size="isMobile ? '50px' : '70px'"
      >
        {{ initials }}
      </q-avatar>

      <div v-if="!loading" class="header-infos">
        <div class="row q-pt-sm">
          <h1 class="company-title text-h5 text-bold q-ma-none ellipsis">
            {{ companyName || '?' }}
          </h1>
          <q-chip
            size="sm"
            color="blue-1"
            :class="[
              'text-primary text-uppercase text-bold q-mt-sm',
              { 'q-ml-sm': !isMobile }
            ]"
          >
            {{ $t(active ? 'active' : 'inactive') }}
          </q-chip>
        </div>

        <q-breadcrumbs
          separator="|"
          separator-color="grey"
          class="company-header-breadcrumb gt-sm"
        >
          <q-breadcrumbs-el
            v-if="isEstablishment && parentIdentifier"
            :label="parentCompany"
            :to="`/companies/${parentIdentifier}`"
          />

          <q-breadcrumbs-el
            v-if="isEstablishment"
            :label="$t('establishments')"
            :to="
              parentIdentifier
                ? `/companies/${parentIdentifier}/establishments`
                : '/companies'
            "
          />
          <q-breadcrumbs-el v-else :label="$t('companies')" to="/companies" />

          <q-breadcrumbs-el
            :label="companyVat"
            :to="`/companies/${
              isEstablishment ? 'establishments/' : ''
            }${identifier}`"
          />
          <q-breadcrumbs-el v-if="juridicalSituation">
            <q-icon
              name="fa-regular fa-wave-pulse"
              size="0.85rem"
              color="primary"
            />
            <span class="q-pl-sm text-primary">
              {{ juridicalSituation }}
            </span>
          </q-breadcrumbs-el>
        </q-breadcrumbs>
      </div>

      <div v-show="showActions" class="header-actions">
        <q-btn
          icon="fa-regular fa-bars"
          class="lt-md q-mr-md"
          rounded
          outline
          color="grey"
          @click="onMobileMenu"
        />

        <q-dialog
          v-model="mobileMenu"
          maximized
          transition-show="slide-right"
          transition-hide="slide-left"
        >
          <q-card>
            <div class="flex justify-end q-pa-sm">
              <q-btn flat rounded size="lg" @click="onMobileMenu">
                <q-icon name="fa-regular fa-xmark" color="grey" size="sm" />
              </q-btn>
            </div>
            <CompanySidebar
              :company-id="identifier"
              :features="features"
              extended
            />
          </q-card>
        </q-dialog>

        <q-btn
          v-if="isAllowForReports"
          :label="isMobile ? '' : $t('report')"
          icon="fa-regular fa-download"
          color="primary"
          rounded
          unelevated
          class="q-mr-sm"
          @click="openReport"
        />

        <!-- WATCHLISTS -->
        <WatchlistMenu
          v-if="!isEstablishment"
          :disable="!companyId"
          :loading="watchlistsLoading"
          :watchlists="watchlists"
          :company-id="companyId"
          @load-watchlists="$emit('load-watchlists')"
          @toggle="toggleToList"
        />

        <div v-if="extended" class="header-update-date text-primary q-pt-sm">
          <q-icon
            name="fa-regular fa-circle-check"
            size="0.85rem"
            class="q-pr-xs"
          />
          {{ $t('lastUpdate') }}: {{ updatedDateTimeAgo }}
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
.company-header.extended {
  position: relative;
  background-color: var(--white);
  border-radius: 0.5rem;
  padding: 2rem 1.5rem 1rem;
  border: 1px solid var(--border);
  overflow: hidden;
}

.company-header.extended .company-header-bg-color {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4.5rem;
  background-color: var(--greyDark);
  background-image: url('/company-bg.jpg');
  background-size: cover;
  background-blend-mode: multiply;
}

.header-wrapper {
  display: grid;
  grid-template-columns: auto minmax(0, 1fr) auto auto;
  grid-template-areas: 'icon infos actions menu';
  grid-column-gap: 0;
  grid-row-gap: 0;
  position: relative;
  z-index: 1;
  align-items: center;
}

.company-header.extended .header-wrapper {
  grid-template-columns: auto minmax(0, 100%) auto;
  grid-template-areas: 'icon infos actions';
  align-items: start;
}

.header-icon {
  grid-area: icon;
  color: var(--white);
  background-color: var(--black);
}

.header-infos {
  grid-area: infos;
  padding-left: 1rem;
}

.company-title {
  white-space: normal;
}

.company-header.extended .header-infos {
  padding-left: 0;
  grid-area: 2 / 1 / 3 / 3;
}

.company-header-breadcrumb {
  font-size: 0.85rem;
}

.header-actions {
  grid-area: actions;
  white-space: nowrap;
}

.company-header.extended .header-actions {
  padding-top: 1rem;
  grid-area: 3 / 1 / 4 / 4;
}

@media (min-width: 600px) {
  .header-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

@media (min-width: 1024px) {
  .header-actions {
    display: block;
    text-align: right;
    justify-self: end;
    grid-area: actions;
    white-space: nowrap;
    padding-top: 0;
  }

  .company-header.extended .header-actions {
    grid-area: 2 / 3 / 3 / 4;
  }
}

.header-update-date {
  font-size: 0.85rem;
  word-break: normal;
}

@media (min-width: 600px) {
  .header-update-date {
    text-align: right;
    flex-grow: 1;
  }
}
</style>
