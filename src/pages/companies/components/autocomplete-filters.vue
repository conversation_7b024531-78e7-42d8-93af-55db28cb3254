<script setup>
import { storeToRefs } from 'pinia';
import { SearchCompaniesStore } from '@/store/search-companies';

const searchCompanyStore = SearchCompaniesStore();
const { loading, autocompleteFilters } = storeToRefs(searchCompanyStore);
</script>

<template>
  <q-card style="min-width: 250px">
    <q-card-section>
      <div class="text-overline text-uppercase">{{ $t('filters') }}:</div>
      <div class="column">
        <q-toggle
          :disable="loading"
          :label="$t('includeInactive')"
          :model-value="autocompleteFilters.includeInactive"
          size="sm"
          @update:model-value="
            (value) =>
              searchCompanyStore.setFilter({
                key: 'includeInactive',
                value
              })
          "
        />
        <q-toggle
          :disable="loading"
          :label="$t('includeEstablishments')"
          :model-value="autocompleteFilters.includeEstablishments"
          size="sm"
          @update:model-value="
            (value) =>
              searchCompanyStore.setFilter({
                key: 'includeEstablishments',
                value
              })
          "
        />
        <q-toggle
          :disable="loading"
          :label="$t('includeNaturalPersons')"
          :model-value="autocompleteFilters.includeNaturalPersons"
          size="sm"
          @update:model-value="
            (value) =>
              searchCompanyStore.setFilter({
                key: 'includeNaturalPersons',
                value
              })
          "
        />
      </div>
    </q-card-section>
    <q-separator />
    <q-card-actions align="right">
      <q-btn
        :loading="loading"
        :label="$t('reset')"
        flat
        @click="searchCompanyStore.resetFilters"
        v-close-popup
      />
      <q-btn
        :loading="loading"
        :label="$t('apply')"
        flat
        color="primary"
        @click="$emit('submit')"
        v-close-popup
      />
    </q-card-actions>
  </q-card>
</template>
