<script setup>
import ValueAndGrowth from '@/pages/companies/components/value-and-growth.vue';

defineProps({
  title: String,
  icon: String,
  iconColor: String,
  iconBgColor: String,
  currency: String,
  value: [Number, String],
  ratio: Number,
  inline: Boolean,
  tooltip: String,
  noValue: Boolean,
  isFlat: Boolean
});
</script>

<template>
  <q-card bordered :flat="isFlat" class="abstract-card q-mb-sm">
    <div class="source-item">
      <q-avatar
        v-if="icon"
        rounded
        :class="iconBgColor || 'bg-blue-1'"
        :size="$q.screen.lt.md ? 'md' : 'xl'"
      >
        <q-icon :name="icon" :color="iconColor || 'primary'" />
      </q-avatar>

      <div
        :class="[
          'source-text text-bold',
          {
            'source-inline': inline
          }
        ]"
      >
        <div class="source-title text-uppercase">
          <q-icon
            v-if="tooltip"
            name="fa-regular fa-circle-question"
            :size="$q.screen.lt.md ? 'xs' : 'sm'"
            class="q-mr-sm"
          />
          <div>{{ title }}</div>
          <q-tooltip v-if="tooltip" left>
            {{ tooltip }}
          </q-tooltip>
        </div>

        <template v-if="!noValue">
          <ValueAndGrowth :value="value" :ratio="ratio" :currency="currency" />
        </template>

<style scoped>
@media print {
  .abstract-card {
    box-shadow: none;
  }
}

.source-item {
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

@media screen and (min-width: 600px) {
  .source-item {
    flex-direction: row;
  }
}

.source-text {
  flex-grow: 1;
  padding-top: 0.5rem;
  font-size: var(--small);
}

@media screen and (min-width: 600px) {
  .source-text {
    padding-top: 0;
    padding-left: 1rem;
  }
}

.source-title {
  color: var(--primaryGrey);
  font-size: var(--xsmall);
}

.source-text.source-inline {
  align-self: center;
  padding-left: 0;
}

.source-inline div:first-child {
  display: flex;
}

.source-action {
  align-self: end;
  display: flex;
  justify-content: center;
}
</style>
