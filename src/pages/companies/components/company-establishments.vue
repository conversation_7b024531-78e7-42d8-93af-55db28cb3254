<script setup>
import { h, computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  loading: Boolean,
  isMobile: Boolean,
  withTitle: <PERSON>olean,
  company: Object,
  columns: Array,
  establishments: Array
});

const navigate = function (event, identifier) {
  event.preventDefault();
  router.push(`/companies/establishments/${identifier}`);
};

const formattedColumns = computed(() => {
  if (!props.isMobile) return props.columns;

  return props.columns.map((column) => {
    if (column.field === 'name') {
      column.format = (_value, row) =>
        h('a', { onClick: (event) => navigate(event, row.identifier) }, [
          row.name || '?'
        ]);
    }

    return column;
  });
});
</script>

<template>
  <q-table
    :loading="loading"
    :columns="formattedColumns"
    :rows="establishments"
    :rows-per-page-options="[0]"
    :title="withTitle ? $t('establishments') : null"
    title-class="text-uppercase"
    :grid="isMobile"
    wrap-cells
    hide-bottom
    bordered
    flat
  >
    <!-- ICON -->
    <template #body-cell-icon="props">
      <q-td :props="props">
        <q-avatar
          size="sm"
          class="text-white"
          :style="{ backgroundColor: props.row.color || 'var(--primary)' }"
        >
          {{ props.row.initials }}
        </q-avatar>
      </q-td>
    </template>
