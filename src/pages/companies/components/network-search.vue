<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { SearchCompaniesStore } from '@/store/search-companies';
import CompanyAutocompleteItem from '@/components/commons/company-autocomplete-item.vue';

const props = defineProps({
  loading: <PERSON><PERSON><PERSON>,
  disabled: Boolean,
  includeInactive: <PERSON>olean
});

const emit = defineEmits(['select', 'clear']);

const searchCompaniesStore = SearchCompaniesStore();
const { loading: searchLoading, autocompleteCompaniesResults } =
  storeToRefs(searchCompaniesStore);

const selectedCompany = ref(null);
const selectCompany = function (company) {
  emit('select', { companyId: company.identifier });
};

const searchInput = ref(null);
const search = async function (input) {
  if (
    typeof searchInput.value === 'string' &&
    input.toLowerCase() === searchInput.value.toLowerCase()
  ) {
    return;
  }

  searchInput.value = input;
  await searchCompaniesStore.autocompleteCompanies(input, {
    includeEstablishments: false,
    includeInactive: props.includeInactive
  });
};

const clear = function () {
  searchInput.value = '';
  selectedCompany.value = null;
  searchCompaniesStore.clearAutocomplete();
  emit('clear');
};
</script>

<template>
  <q-form class="network-form">
    <q-select
      v-model="selectedCompany"
      :label="$t('searchForCompanies')"
      :options="autocompleteCompaniesResults"
      :loading="searchLoading || loading"
      :disable="disabled"
      option-value="identifier"
      option-label="name"
      borderless
      use-input
      hide-selected
      fill-input
      fit
      input-debounce="500"
      hide-dropdown-icon
      @input-value="search"
      @update:model-value="selectCompany"
    >
      <template v-slot:prepend>
        <q-btn
          flat
          round
          icon="fa-regular fa-magnifying-glass"
          size="sm"
          color="primary"
          @click="search"
        />
      </template>

      <template v-slot:append>
        <q-icon
          v-if="searchInput"
          class="cursor-pointer"
          name="fa-regular fa-close"
          @click.stop.prevent="clear"
        />
      </template>

      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            {{ $t('noResultFound') }}
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:option="scope">
        <CompanyAutocompleteItem
          v-bind="scope.itemProps"
          :key="scope.opt.id"
          :id="scope.opt.id"
          :name="scope.opt.name"
          :vat="scope.opt.vatFormatted"
          :is-active="scope.opt.isActive"
          :juridical-form="scope.opt.juridicalForm"
          :main-activity="scope.opt.mainActivity"
          :address="scope.opt.address"
          :entity="scope.opt.entity"
          :type="scope.opt.type"
        />
      </template>
    </q-select>
  </q-form>
</template>

<style scoped>
.network-form {
  width: 100%;
}
</style>
