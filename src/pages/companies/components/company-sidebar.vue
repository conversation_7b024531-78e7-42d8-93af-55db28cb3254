<script setup>
import { computed } from 'vue';
import { companyPageData } from '@/pages/companies/data/company-pages';

const props = defineProps({
  companyId: String,
  extended: Boolean,
  features: Array
});

const companyPages = computed(() => companyPageData(props.features));
</script>

<template>
  <aside :class="['company-nav', { extended }]">
    <q-list dense dense-toggle class="company-nav-list scroll">
      <q-expansion-item
        v-for="group in companyPages"
        :key="group.id"
        default-opened
        hide-expand-icon
      >
        <template #header="props">
          <q-item-section avatar>
            <q-icon :name="group.icon" color="primary" size="xs" />
          </q-item-section>

          <q-item-section class="text-primary">
            {{ group.title }}
          </q-item-section>

          <q-item-section side>
            <q-icon
              :name="`fa-regular fa-${props.expanded ? 'minus' : 'plus'}`"
              color="grey"
              size="xs"
            />
          </q-item-section>
        </template>

        <div class="company-nav-group">
          <q-item
            v-for="child in group.children"
            :key="child.id"
            :to="`/companies/${companyId}/${child.id}`"
            :disable="child.disabled"
            active-class="company-nav-active-link"
            class="company-nav-child"
          >
            {{ child.title }}
          </q-item>
        </div>
      </q-expansion-item>
    </q-list>
  </aside>
</template>

<style scoped>
.company-nav {
  padding-top: 1.75rem;
  background-color: var(--white);
  margin-left: -1.45rem;
  margin-top: -1.45rem;
  margin-bottom: -1.45rem;
  height: calc(100% + 2.65em + 5px);
  min-height: calc(100dvh - 2px);
  border-right: 1px solid var(--border);
}

.company-nav.extended {
  width: 100%;
  margin: 0;
  padding-top: 0.5rem;
  height: 100dvh;
  min-height: 0;
  border: none;
}

.company-nav.extended .company-nav-list {
  overflow-y: auto;
  min-height: 100dvh;
}

.company-nav-group {
  position: relative;
  padding-left: 2.5rem;
  padding-right: 1rem;
}

.company-nav-group::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 0.15rem;
  margin-left: -0.15rem;
  border-radius: 0.15rem;
  background-color: var(--primary);
}

.company-nav-child {
  font-size: var(--small);
  align-items: center;
  border-radius: 0.25rem;
}

.company-nav-active-link {
  font-weight: bold;
  color: var(--primary);
  background-color: var(--primarySmoke);
}

.company-nav :deep(.q-item__section--avatar) {
  min-width: 2rem;
}
</style>
