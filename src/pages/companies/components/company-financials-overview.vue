<script setup>
import FeatureTeasing from '@/components/commons/feature-teasing.vue';
import { COMPANY_KEY_FINANCIALS } from '@/utils/features';

defineProps({
  statements: {
    type: Object,
    default: () => ({
      top: [],
      others: []
    })
  },
  teasing: <PERSON><PERSON><PERSON>
});
</script>

<template>
  <div :class="['company-financials-overview', { 'q-pa-md': teasing }]">
    <FeatureTeasing v-if="teasing" :feature="COMPANY_KEY_FINANCIALS" overlay />
    <!-- TOP -->
    <div :class="['row q-col-gutter-lg q-pb-lg', { isTeasing: teasing }]">
      <div
        class="col-12 col-sm-6"
        v-for="statement in statements.top"
        :key="statement.key"
      >
        <q-card class="overview-card">
          <q-card-section class="text-center q-pa-lg">
            <q-avatar
              :color="statement.color"
              rounded
              class="overview-card-icon"
            >
              <q-icon :name="statement.icon" color="white" />
            </q-avatar>
            <div class="text-h1 overview-card-value">
              <div
                :class="[
                  statement.textClass,
                  `text-${statement.color}`,
                  { 'overview-card-top': statement.isTop }
                ]"
              >
                {{ statement.value }}
              </div>
            </div>
            <div class="text-h6">{{ statement.label }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <!-- OTHERS -->
    <div
      :class="[
        'row q-col-gutter-lg justify-center q-pb-lg',
        { isTeasing: teasing }
      ]"
    >
      <div
        class="col-6 col-sm-3"
        v-for="statement in statements.others"
        :key="statement.key"
      >
        <q-card class="overview-card">
          <q-card-section class="text-center">
            <div class="text-h1 overview-card-value">
              <div :class="statement.textClass">{{ statement.value }}</div>
            </div>
            <div class="text-h6">{{ statement.label }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.company-financials-overview {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

.isTeasing {
  filter: blur(3px);
}

.overview-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.overview-card-icon {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
}

.overview-card-value {
  display: flex;
  flex-grow: 1;
  line-height: 1.3;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.overview-card-value .overview-card-top {
  font-size: 4rem;
}
</style>
