<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { getMonthName, formatDate } from '@/utils/date';

const props = defineProps({
  annualMeetingMonth: Number,
  endFiscalYearDay: Number,
  endFiscalYearMonth: Number,
  excStart: String,
  excEnd: String
});

const { t, locale } = useI18n();

const annualMeeting = computed(() =>
  props.annualMeetingMonth
    ? getMonthName(props.annualMeetingMonth, locale)
    : '–'
);

const endFiscal = computed(() =>
  [props.endFiscalYearDay, getMonthName(props.endFiscalYearMonth, locale)].join(
    ' '
  )
);

const dateOption = {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
};

const none = '–';

const items = computed(() => [
  {
    label: t('annualAssembly'),
    value: annualMeeting
  },
  {
    label: t('endDateFinancialYear'),
    value: endFiscal
  },
  {
    // exceptional_fiscal_year_begin
    label: t('startExc'),
    value: props.excStart
      ? formatDate(props.excStart, locale.value, dateOption)
      : none
    // tooltip: 'text'
  },
  {
    // exceptional_fiscal_year_end
    label: t('endExc'),
    value: props.excEnd
      ? formatDate(props.excEnd, locale.value, dateOption)
      : none
    // tooltip: 'text'
  }
]);
</script>

<template>
  <q-card bordered class="company-key-dates">
    <q-card-section class="key-dates">
      <q-item v-for="item in items" :key="item.label" inline>
        <q-item-section v-if="item.tooltip" avatar class="key-dates-tooltip">
          <q-icon name="fa-regular fa-circle-question" color="grey" size="sm" />
          <q-tooltip>{{ item.tooltip }}</q-tooltip>
        </q-item-section>

        <q-item-section class="text-bold text-body2">
          {{ item.label }}
        </q-item-section>

        <q-item-section class="text-body2">
          {{ item.value }}
        </q-item-section>
      </q-item>
    </q-card-section>
  </q-card>
</template>

<style scoped>
@media print {
  .company-key-dates {
    box-shadow: none;
  }
}

.key-dates {
  line-height: 1.33;
}
.key-dates-tooltip {
  min-width: 0;
  padding-right: 0.5rem;
}
</style>
