<script setup>
import { storeToRefs } from 'pinia';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import CompanyHeader from '@/pages/companies/components/company-header.vue';
import { AppStore } from '@/store';

defineProps({
  company: {
    type: Object,
    default: null
  },
  loading: Boolean,
  isMobile: Boolean,
  noHeader: Boolean,
  fullPage: Boolean
});

// Collapse the main navigation because the company sidebar take a lot of space
const appStore = AppStore();
appStore.setNavCollapse(true);

const monitoringStore = MonitoringStore();
const authStore = AuthStore();

const { loading: watchlistsLoading, watchlists } = storeToRefs(monitoringStore);

function loadWatchlists() {
  monitoringStore.loadWatchlists(authStore.userOrganisation);
}

function addToList({ watchlistShortId, companyIdentifier }) {
  monitoringStore.addCompanyToWatchlist({
    organisationShortId: authStore.userOrganisation,
    watchlistShortId,
    companyIdentifier
  });
}

function removeFromList({ watchlistShortId, companyIdentifier }) {
  monitoringStore.removeCompanyFromWatchlist({
    organisationShortId: authStore.userOrganisation,
    watchlistShortId,
    companyIdentifier
  });
}
</script>

<template>
  <div v-if="!fullPage" class="col q-pl-md-lg">
    <CompanyHeader
      v-if="!noHeader && company !== null"
      :loading="loading"
      :is-mobile="isMobile"
      :watchlists-loading="watchlistsLoading"
      :identifier="company.identifier"
      :company-id="company.id"
      :company-name="company.name"
      :company-vat="company.vatFormatted"
      :active="company.active"
      :updated-at="company.updatedAtOriginal"
      :juridical-situation="company.juridicalSituation"
      :color="company.color"
      :initials="company.initials"
      :extended="$route.meta && $route.meta.extendedHeader"
      :watchlists="watchlists"
      show-actions
      @load-watchlists="loadWatchlists"
      @add-to-list="addToList"
      @remove-from-list="removeFromList"
    />
    <slot />
  </div>

  <div v-else class="col full-page">
    <slot />
  </div>
</template>

<style scoped>
.full-page {
  margin-top: -24px;
  margin-bottom: -24px;
  margin-right: -24px;
  background-color: var(--white);
}
</style>
