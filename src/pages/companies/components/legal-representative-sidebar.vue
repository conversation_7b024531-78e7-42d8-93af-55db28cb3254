<script setup>
import { watch, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { SearchStore } from '@/pages/search/store';
import { PersonStore } from '@/pages/persons/store';
import { AuthStore } from '@/pages/auth/store';
import { PERSON_SEARCH } from '@/utils/features';
import downloadCSV from '@/utils/download-csv';
import { getAvatarColor } from '@/utils/avatar';

const props = defineProps({
  active: Boolean,
  personName: String,
  companyId: String
});

const searchStore = SearchStore();
const { loading, list } = storeToRefs(searchStore);

const personStore = PersonStore();
const { loading: companiesLoading, personCompanies } = storeToRefs(personStore);

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForSearchPersons = computed(() =>
  features.value.includes(PERSON_SEARCH)
);

const person = computed(() => {
  if (list.value.length === 0) return {};

  return list.value[0];
});

const genderIcon = computed(() => {
  if (!person.value.gender) return null;

  return person.value.gender === 'm'
    ? 'fa-regular fa-mars'
    : 'fa-regular fa-venus';
});

const compamiesIdentifiersCount = computed(() => {
  if (!list.value[0]?.companiesIdentifiers) return 0;

  return list.value[0].companiesIdentifiers.length;
});

watch(
  () => props.active,
  async (isActive) => {
    if (isActive) {
      await searchStore.searchPersons({
        q: props.personName,
        max: 1,
        includeInactive: true,
        identifiers: [props.companyId],
        extended: true
      });

      if (isAllowedForSearchPersons.value) {
        personStore.loadPersonCompanies(list.value[0].companiesIdentifiers, {
          includeInactive: true,
          withAvatar: true
        });
      }
    }
  }
);

function download() {
  downloadCSV(person.value);
}
</script>

<template>
  <q-card :loading="loading" flat class="q-pa-md text-body2">
    <div v-if="person" class="flex items-center justify-between">
      <q-avatar
        size="lg"
        class="text-white q-mr-md"
        :style="{ backgroundColor: person.color || 'var(--primary)' }"
      >
        {{ person.initials }}
      </q-avatar>

      <div class="grow-1">
        <h1 class="text-h6 q-ma-none">
          <span v-show="person.title" class="text-capitalize">
            {{ person.title }}.
          </span>
          {{ person.fullName }}

          <q-icon
            v-show="person.gender"
            :name="genderIcon"
            color="grey"
            size="xs"
          />
        </h1>

        <div>{{ $t('profession') }}: {{ person.profession || '-' }}</div>
        <div>{{ $t('birthdate') }}: {{ person.birthDate || '-' }}</div>
        <div>
          {{ $t('lastAppearance') }}: {{ person.lastAppearance || '-' }}
        </div>
      </div>
      <q-btn icon="fa-regular fa-close" round flat @click="$emit('close')" />
    </div>

    <q-separator class="q-my-md" />

    <!-- ADDRESSES -->
    <div v-if="person">
      <h2 class="text-subtitle1 text-bold">
        {{ $t('addresses') }}
        ({{ person?.addresses?.length || 0 }})
      </h2>

      <div v-if="isAllowedForSearchPersons">
        <q-item
          v-for="address in person.addresses"
          :key="address.id"
          dense
          :href="`https://www.google.com/maps/search/?api=1&query=${address.encodedAddress}`"
          target="_blank"
        >
          <q-item-section avatar>
            <q-avatar
              icon="fa-regular fa-map-location-dot"
              color="white"
              text-color="primary"
              size="md"
            />
          </q-item-section>
          <q-item-section>
            <div>
              <span :class="{ 'text-bold': address.primary }">
                {{ address.formatted_address }}
              </span>
              <q-chip v-show="address.primary" color="warning" size="xs">
                {{ $t('mainAddress') }}
              </q-chip>
            </div>
          </q-item-section>
        </q-item>
      </div>

      <div v-else>
        <p>{{ $t('personSearchNotAllowed') }}</p>
      </div>
    </div>

    <!-- COMPANIES -->
    <div v-if="person">
      <h2 class="text-subtitle1 text-bold">
        {{ $t('companies') }} ({{ person?.companiesCount || 0 }})
      </h2>

      <template v-if="companiesLoading">
        <q-item v-for="i in compamiesIdentifiersCount" :key="i" dense>
          <q-item-section avatar>
            <q-skeleton type="QAvatar" size="1.5rem" />
          </q-item-section>
          <q-item-section>
            <q-skeleton type="rect" />
          </q-item-section>
        </q-item>
      </template>
