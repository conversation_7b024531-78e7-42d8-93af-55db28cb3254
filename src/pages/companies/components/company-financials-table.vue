<script setup>
import ValueAndGrowth from '@/pages/companies/components/value-and-growth.vue';

defineProps({
  loading: Boolean,
  isMobile: Boolean,
  columns: Array,
  rows: Array,
  title: String,
  withTitle: <PERSON>olean,
  isSmall: <PERSON>ole<PERSON>
});

const pagination = {
  page: 1,
  rowsPerPage: 0
};
</script>

<template>
  <q-table
    :loading="loading"
    :columns="columns"
    :rows="rows || []"
    :no-data-label="$t('noDataAvailable')"
    :pagination="pagination"
    :title="withTitle ? title : null"
    :dense="isSmall || isMobile"
    title-class="text-uppercase"
    separator="vertical"
    row-key="description"
    wrap-cells
    hide-bottom
    bordered
    flat
  >
    <template #body-cell-year="props">
      <q-td
        :class="{
          'font-size-normal': !isSmall && !isMobile,
          'font-size-xsmall': isSmall || isMobile
        }"
      >
        <ValueAndGrowth
          v-if="props.value?.current"
          :value="props.value.current"
          :ratio="props.value.growth"
          :currency="props.value.currency"
          :dense="isSmall || isMobile"
        />
        <!-- no data for this year -->
        <div v-else>
          {{ $t('n-a') }}
        </div>
      </q-td>
    </template>
