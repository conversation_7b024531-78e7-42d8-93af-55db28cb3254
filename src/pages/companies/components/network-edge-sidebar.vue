<script setup>
defineProps({
  selectedEdge: Object
});
</script>

<template>
  <q-card class="selection-sidebar">
    <q-list>
      <q-item>
        <q-item-section>
          <q-item-label class="text-capitalize">
            {{ selectedEdge.label }}
          </q-item-label>
        </q-item-section>
        <q-item-section avatar>
          <q-btn
            icon="fa-regular fa-close"
            round
            flat
            @click.stop.prevent="$emit('deselect')"
          />
        </q-item-section>
      </q-item>

      <q-item v-show="selectedEdge.startDate">
        <q-item-section>
          <q-item-label caption>
            {{ $t('startDate') }}:
            {{ selectedEdge.startDate }}
          </q-item-label>
        </q-item-section>
      </q-item>

      <q-item v-show="selectedEdge.endDate">
        <q-item-section>
          <q-item-label caption>
            {{ $t('endDate') }}:
            {{ selectedEdge.endDate }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-card>
</template>

<style scoped>
.selection-sidebar {
  position: absolute;
  top: 8rem;
  right: 4rem;
  max-width: 20rem;
  z-index: 1;
  padding: 1rem;
  max-height: calc(100vh - 10rem);
  overflow-y: auto;
}
</style>
