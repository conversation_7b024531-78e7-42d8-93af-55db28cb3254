<script setup>
defineProps({
  loading: <PERSON><PERSON>an,
  isMobile: Boolean,
  columns: Array,
  publications: Array,
  options: Object,
  pagination: Object,
  withTitle: <PERSON>olean,
  disabled: Boolean
});
</script>

<template>
  <q-table
    :loading="loading"
    :dense="isMobile"
    :columns="columns"
    :rows="publications"
    :rows-per-page-options="options"
    :pagination="pagination"
    :title="withTitle ? $t('publications') : null"
    title-class="text-uppercase"
    flat
    bordered
  >
    <template #body-cell-link="props">
      <q-td :props="props">
        <q-btn
          v-if="!(props.row.type === 'be_moniteur' && !props.row.hasURI)"
          :to="{
            name: 'company-publication',
            params: { identifier: props.row.identifier }
          }"
          :label="isMobile ? '' : $t('publication')"
          :disable="disabled"
          icon="fa-regular fa-file-pdf"
          color="primary"
          size="sm"
          outline
        />
      </q-td>
    </template>
