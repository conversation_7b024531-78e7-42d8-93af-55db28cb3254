<template>
  <FeatureTeasing
    v-if="!isAllowedForReports"
    :feature="COMPANY_PDF_REPORT"
    overlay
  />

  <main v-if="company" id="pdf-company-report" class="pages">
    <section class="page-cover">
      <div class="page-cover-header">
        <img src="@/assets/logo.svg" alt="Data.be" class="page-cover-logo" />
        <div class="page-cover-header-title">{{ $t('businessReport') }}</div>
      </div>

      <div class="page-cover-content">
        <div class="page-cover-inside">
          <h1 class="page-cover-title">{{ company.name }}</h1>
          <ul class="company-cover-data">
            <li class="company-cover-data-item">
              <span>{{ $t('vat') }}</span>
              <span>{{ company.vatFormatted }}</span>
            </li>
            <li class="company-cover-data-item">
              <span>{{ $t('date') }}</span>
              <span>{{ today }}</span>
            </li>
            <li class="company-cover-data-item">
              <span>{{ $t('account') }}</span>
              <span>{{ user.email }}</span>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <section class="page">
      <CompanyHeader
        :identifier="company.identifier"
        :company-id="company.id"
        :company-name="company.name"
        :company-vat="company.vatFormatted"
        :active="company.active"
        :updated-at="company.updatedAtOriginal"
        :juridical-situation="company.juridicalSituation"
        :color="company.color"
        :initials="company.initials"
        extended
        class="section"
      />

      <CompanyOverview
        :address="company.mainAddress.formatted_address"
        :start-date="company.orginialStartDate"
        :juridical-situation="company.juridicalSituation"
        :main-activity="company.mainActivity || company.firstActivity"
        :name="company.name"
        :abbreviation="company.abbreviation"
        :vat="company.vatFormatted"
        :juridical-form="company.juridicalForm"
        :legal-person-type="company.legalPersonType"
        class="section q-mb-md"
      />

      <CompanyContact
        :company="company"
        :address="company.mainAddress"
        :addresses="company.otherAddresses"
        :website="company.contacts.website"
        :email="company.contacts.email"
        :mobile="company.contacts.phone"
        :phone="company.contacts.faxNumber"
        class="section q-mb-md"
      />

      <CompanyRevenuesSummary
        v-if="latestStatement?.data"
        :identifier="company.identifier"
        :current-ratio="latestStatement.data.currentRatio"
        :current-ratio-growth="latestStatement.data.currentRatioGrowth"
        :employees="latestStatement.data.employees"
        :net-cash="latestStatement.data.netCash"
        :net-cash-growth="latestStatement.data.netCashGrowth"
        :net-cash-currency="latestStatement.data.netCashCurrency"
        :return-on-equity="latestStatement.data.returnOnEquity"
        :return-on-equity-growth="latestStatement.data.returnOnEquityGrowth"
        :cash-flow-to-debt="latestStatement.data.cashFlowToDebt"
        :cash-flow-to-debt-growth="latestStatement.data.cashFlowToDebtGrowth"
        :cash-flow-to-debt-currency="
          latestStatement.data.cashFlowToDebtCurrency
        "
        :gross-operating-margin="latestStatement.data.grossOperatingMargin"
        :gross-operating-margin-growth="
          latestStatement.data.grossOperatingMarginGrowth
        "
        :gross-operating-margin-currency="
          latestStatement.data.grossOperatingMarginCurrency
        "
        class="section"
      />

      <CompanySectors
        :activities="company.activities"
        :characteristics="company.characteristics"
        :authorizations="company.authorizations"
        class="section q-my-md"
      />

      <CompanyKeyDates
        :annual-meeting-month="company.financialMeta?.annual_meeting_month"
        :end-fiscal-year-day="company.financialMeta?.end_fiscal_year_day"
        :end-fiscal-year-month="company.financialMeta?.end_fiscal_year_month"
        :exc-start="company.financialMeta?.exceptional_fiscal_year_begin"
        :exc-end="company.financialMeta?.exceptional_fiscal_year_end"
        class="section q-mb-md"
      />

      <CompanyAddressHistory
        :addresses="company.addresses"
        class="section q-mb-md"
      />

      <CompanyNameHistory
        :names="company.names"
        class="section q-mb-md"
        with-title
      />

      <CompanyEstablishments
        :loading="loading"
        :columns="establishmentsColumns"
        :establishments="establishments.list"
        with-title
        class="section q-mb-md"
      />

      <CompanyPublications
        :loading="loading"
        :columns="publicationColumns"
        :publications="publications.list"
        :options="pageOptions"
        :pagination="pagination"
        with-title
        class="section q-mb-md"
      />

      <CompanyLegalRepresentatives
        :loading="loading"
        :columns="functionsColumns"
        :list="representatives.list"
        :title="$t('representatives')"
        class="section q-mb-md"
      />

      <CompanyLegalRepresentatives
        :loading="loading"
        :columns="functionsColumns"
        :list="functions.list"
        :title="$t('functions')"
        class="section q-mb-md"
      />

      <div class="section q-mb-md">
        <h2 class="text-h5 q-mb-none">{{ $t('financialsOverview') }}</h2>
        <h3 class="text-h6 q-mt-sm">
          {{ $t('accountDate') }}: {{ latestStatement?.overview?.date }}
        </h3>
        <CompanyFinancialsOverview :statements="statements" />
      </div>
    </section>

    <CompanyReportGraph
      v-for="entry in graphs.selectedSeries"
      :key="entry"
      :chart-id="entry"
      :name="graphOptionNameByKey(entry)"
      :series="graphEntryByKey(entry)"
      :settings="graphs.settings"
      class="section"
    />

    <CompanyFinancialsTable
      :loading="loading"
      :columns="financials.columns"
      :rows="financials.rows"
      :title="$t('financialsOverview')"
      withTitle
      is-small
      class="section q-mb-md"
    />

    <CompanyFinancialsTable
      v-for="group in groups"
      :key="group"
      :loading="loading"
      :columns="ratios.columns"
      :rows="ratios.rows[group]"
      :title="$t(group)"
      withTitle
      is-small
      class="q-mb-md"
    />

    <CompanyFinancialsTable
      :loading="loading"
      :columns="socials.columns"
      :rows="socials.rows"
      :title="$t('socialBalance')"
      withTitle
      is-small
    />

    <footer class="page-footer">
      <img src="@/assets/logo.svg" alt="" class="page-footer-logo q-mr-md" />
      {{ $t('reportFooter', { name: user.email, date: today }) }}
    </footer>
  </main>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_PDF_REPORT } from '@/utils/features';
import { statements } from '@/pages/companies/composables/prepare-statements';
import CompanyHeader from '@/pages/companies/components/company-header.vue';
import CompanyOverview from '@/pages/companies/components/company-overview.vue';
import CompanyContact from '@/pages/companies/components/company-contact.vue';
import CompanyRevenuesSummary from '@/pages/companies/components/company-revenues-summary.vue';
import CompanySectors from '@/pages/companies/components/company-sectors.vue';
import CompanyKeyDates from '@/pages/companies/components/company-key-dates.vue';
import CompanyAddressHistory from '@/pages/companies/components/company-address-history.vue';
import CompanyNameHistory from '@/pages/companies/components/company-name-history.vue';
import CompanyEstablishments from '@/pages/companies/components/company-establishments.vue';
import CompanyPublications from '@/pages/companies/components/company-publications.vue';
import CompanyLegalRepresentatives from '@/pages/companies/components/company-legal-representatives.vue';
import CompanyFinancialsOverview from '@/pages/companies/components/company-financials-overview.vue';
import CompanyFinancialsTable from '@/pages/companies/components/company-financials-table.vue';
import CompanyReportGraph from '@/pages/companies/components/company-report-graph.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';
import establishmentsColumns from '@/pages/companies/data/establishments-columns';
import publicationColumns from '@/pages/companies/data/publications-columns';
import functionsColumns from '@/pages/companies/data/functions-columns';

const { t, locale } = useI18n();
const route = useRoute();

const authStore = AuthStore();
const { user } = storeToRefs(authStore);

const { features } = storeToRefs(authStore);
const isAllowedForReports = computed(() =>
  features.value.includes(COMPANY_PDF_REPORT)
);

const companiesStore = CompaniesStore();
const {
  loading,
  company,
  latestStatement,
  establishments,
  publications,
  functions,
  representatives,
  financials,
  ratios,
  socials,
  graphs
} = storeToRefs(companiesStore);

const emit = defineEmits(['ready']);

onMounted(async () => {
  if (!isAllowedForReports.value) return;

  await companiesStore.loadReport(route.params.id);
  emit('ready');
});

function graphOptionNameByKey(key) {
  const option = graphs.value.options.find((option) => option.id === key);
  return option ? option.label : '';
}

function graphEntryByKey(key) {
  return graphs.value.series.filter((serie) => serie.id === key);
}

useHead({
  title: () => [t('company-title'), company.value?.name].join(' - ')
});

const today = new Date().toLocaleDateString(locale);
const pageOptions = [];
const pagination = {
  rowsPerPage: 0
};
const groups = ['profitability', 'solvency', 'liquidity', 'added_value'];
</script>

<style>
html,
body,
.pages {
  background: white !important;
}

@page {
  size: a4 portait;
  margin: 25mm 15mm 10mm;
}

@media screen {
  .pages {
    padding: 1rem 2rem;
  }
}

.page {
  page-break-after: always;
}

.section {
  break-inside: avoid;
}

@media screen {
  .page-footer {
    display: none;
  }
}

.page-footer {
  position: fixed;
  bottom: 5mm;
  left: 0;
  right: 0;
  border-top: 1px solid var(--primary);
  color: var(--primary);
  background-color: white;
  padding-top: 1rem;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-footer-logo {
  display: inline-block;
  height: 30px;
}

.page-cover {
  display: flex;
  flex-direction: column;
  page-break-after: always;
  height: 100dvh;
  background-color: white;
  position: relative;
  z-index: 2;
}

.page-cover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  flex-grow: 0;
}

.page-cover-logo {
  height: 3rem;
}

.page-cover-header-title {
  font-weight: bold;
  font-size: 1.75rem;
}

.page-cover-content {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-cover-inside {
  padding: 1rem 0;
}

.page-cover-title {
  text-align: center;
}

.company-cover-data {
  display: block;
  margin: 0 auto;
  padding: 0;
}

.company-cover-data-item {
  display: flex;
  align-items: center;
}

.company-cover-data-item > span {
  display: block;
  padding: 0.25rem 0.5rem;
  width: 50%;
}

.company-cover-data-item > span:first-child {
  text-align: right;
  font-weight: bold;
}
</style>
