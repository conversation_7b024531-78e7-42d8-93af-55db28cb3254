<script setup>
defineProps({
  companyName: String,
  companyId: String,
  items: Array
});
</script>

<template>
  <q-breadcrumbs class="q-pb-md" separator-color="grey">
    <q-breadcrumbs-el :label="companyName" :to="`/companies/${companyId}`" />
    <q-breadcrumbs-el
      v-for="item in items"
      :key="item.label"
      :label="item.label"
      :to="item.to"
    />
  </q-breadcrumbs>
</template>
