<script setup>
defineProps({
  loading: <PERSON><PERSON><PERSON>,
  isMobile: <PERSON><PERSON><PERSON>,
  columns: <PERSON>rray,
  list: Array,
  title: String
});
</script>

<template>
  <q-table
    :loading="loading"
    :columns="columns"
    :rows="list"
    :no-data-label="$t('noDataAvailable')"
    :rows-per-page-options="[]"
    :title="title"
    :dense="isMobile"
    title-class="text-uppercase"
    wrap-cells
    hide-bottom
    bordered
    flat
    class="q-mb-md"
  >
    <template #body-cell-company="props">
      <q-td :props="props">
        <router-link
          v-if="props.row.company_name"
          :to="`/companies/${props.row.company_identifier}`"
        >
          {{ props.row.company_name }}
        </router-link>
        <span v-else>–</span>
      </q-td>
    </template>

    <template #body-cell-person_name="props">
      <q-td :props="props">
        <q-btn
          :label="props.row.person_name"
          text-color="primary"
          flat
          @click="$emit('toggle-sidebar', props.row.person_name)"
        />
      </q-td>
    </template>
  </q-table>
</template>
