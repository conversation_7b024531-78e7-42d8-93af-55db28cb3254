<script setup>
import { computed } from 'vue';
import abstractBox from '@/pages/companies/components/abstract-box.vue';

const props = defineProps({
  activities: Array,
  characteristics: Array,
  authorizations: Array
});

const primaryActivities = computed(() => {
  if (!Array.isArray(props.activities) || props.activities.length === 0) {
    return [];
  }

  return props.activities.filter((activity) => activity.primary);
});

const secondaryActivities = computed(() => {
  if (!Array.isArray(props.activities) || props.activities.length === 0) {
    return [];
  }

  return props.activities.filter((activity) => !activity.primary);
});
</script>

<template>
  <q-card flat bordered>
    <q-card-section class="q-px-lg">
      <h2 class="text-h6 text-bold q-ma-none text-uppercase">
        {{ $t('sectors') }}
      </h2>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <div class="row q-col-gutter-lg">
        <div class="col col-12 col-md-6">
          <!-- PRIMARY -->
          <abstractBox
            v-if="primaryActivities.length"
            :title="$t('primaryActivity')"
            icon="fa-regular fa-cog"
            class="q-mt-lg"
          >
            <div class="q-pl-lg">
              <!-- ACTIVITY -->
              <template
                v-for="(primaryActivity, index) in primaryActivities"
                :key="primaryActivity.id"
              >
                <q-item class="q-px-none text-body2">
                  <q-item-section thumbnail top>
                    <q-icon
                      name="fa-regular fa-tag"
                      color="primary"
                      size="xs"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-bold">
                      <span class="text-uppercase">
                        {{ $t('naceCode') }}
                      </span>
                      <q-icon
                        name="fa-regular fa-chevron-right"
                        size="xs"
                        class="q-mx-sm"
                      />
                      {{ primaryActivity.code }}
                    </q-item-label>
                    <q-item-label class="label">
                      {{ primaryActivity.name }}
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <!-- REGISTRATION DATE -->
                <q-item class="q-px-none text-body2">
                  <q-item-section thumbnail top>
                    <q-icon
                      name="fa-regular fa-calendar"
                      color="primary"
                      size="xs"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-bold">
                      {{ $t('registrationDate') }}
                    </q-item-label>
                    <q-item-label>
                      {{ primaryActivity?.startDate }}
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <q-separator
                  v-if="index + 1 < primaryActivities.length"
                  class="q-my-sm item-separator"
                />
              </template>

<style scoped>
.item-boxed {
  font-size: var(--small);
  background-color: var(--primarySmoke);
  border: 1px solid var(--border);
  margin-bottom: 0.25rem;
  padding-left: 1.5rem;
  border-radius: 0.5rem;
  flex-wrap: wrap;
}

.label:first-letter {
  text-transform: uppercase;
}

@media (min-width: 1024px) and (max-width: 1280px) {
  .secondary-activity {
    flex-direction: column;
    align-items: flex-start;
    padding-left: 0;
  }

  .secondary-activity > div {
    margin-left: 0 !important;
    padding-left: 0.5rem;
  }
}

.item-separator {
  margin-left: -1.5rem;
}
</style>
