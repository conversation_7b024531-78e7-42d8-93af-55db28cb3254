<script setup>
import sortBy from 'lodash/sortBy';
import { computed } from 'vue';

const props = defineProps({
  loading: <PERSON><PERSON><PERSON>,
  disable: <PERSON><PERSON><PERSON>,
  watchlists: <PERSON>rray,
  companyId: String
});

const sortedWatchlist = computed(() => {
  if (!Array.isArray(props.watchlists)) return [];

  return sortBy(props.watchlists, 'name');
});

function isInWatchlist(companyId, watchlistId) {
  if (!Array.isArray(props.watchlists)) return false;

  const watchlist = props.watchlists.find(
    (watchlist) => watchlist.id === watchlistId
  );

  return watchlist.watchedCompanies.includes(companyId);
}
</script>

<template>
  <q-btn :disable="disable" color="primary" rounded unelevated>
    <q-icon name="fa-regular fa-circle-plus" size="xs" class="q-pr-sm" />
    <span class="q-pr-sm gt-sm">{{ $t('monitor') }}</span>
    <q-icon name="fa-regular fa-chevron-down" size="xs" />

    <!-- WATCHLISTS -->
    <q-menu
      max-height="200px"
      transition-show="jump-down"
      transition-hide="jump-up"
      @show="$emit('load-watchlists')"
    >
      <q-list style="min-width: 100px">
        <q-banner dense class="bg-blue-1 text-primary">
          <template v-slot:avatar>
            <q-icon name="fa-regular fa-circle-info" size="xs" />
          </template>
