<script setup>
import savedListColumns from '@/pages/companies/data/saved-list-columns';

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  watchlists: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
  <section class="company-saved-list q-pb-md">
    <div class="q-pa-lg">
      <h2 class="text-uppercase text-grey text-subtitle1 text-bold q-ma-none">
        {{ $t('savedInList') }}
      </h2>
    </div>

    <q-separator />

    <q-table
      :loading="loading"
      :columns="savedListColumns"
      :rows="watchlists"
      virtual-scroll
      hide-bottom
      flat
    >
      <!-- Name -->
      <template #body-cell-name="props">
        <q-td :props="props">
          <router-link
            :to="`/monitoring/${props.row.id}/alerts`"
            class="text-bold"
          >
            {{ props.row.name }}
          </router-link>
        </q-td>
      </template>

      <!-- TAGS -->
      <template #body-cell-tags="props">
        <q-td :props="props">
          <q-chip
            v-for="tag in props.row.tags"
            :key="`${props.row.id}-${tag}`"
            color="blue-1"
            text-color="primary"
            square
          >
            {{ tag }}
          </q-chip>
          <div v-if="props.row.tags.length === 0">&mdash;</div>
        </q-td>
      </template>
    </q-table>
  </section>
</template>

<style scoped>
.company-saved-list {
  background-color: var(--white);
  border: 1px solid var(--grey);
  border-radius: 0.5rem;
}
</style>
