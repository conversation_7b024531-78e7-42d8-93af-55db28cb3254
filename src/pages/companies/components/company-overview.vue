<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { format } from 'timeago.js';
import { isValidDate } from '@/utils/date';

const props = defineProps({
  address: String,
  startDate: String,
  juridicalSituation: String,
  mainActivity: String,
  name: String,
  abbreviation: String,
  vat: String,
  juridicalForm: String,
  legalPersonType: String,
  establishmentUnits: Number,
  firstPublicationDate: String,
  employees: Number,
  grossMargin: Number,
  isEstablishment: Boolean
});

const { t, locale } = useI18n();

let startDateObject;

if (isValidDate(new Date(props.startDate))) {
  startDateObject = new Date(props.startDate);
}

const currencyOptions = {
  style: 'currency',
  currency: 'EUR',
  notation: 'compact',
  maximumFractionDigits: 0
};

const grossMarginFormatted = computed(() => {
  return typeof props.grossMargin === 'number'
    ? Intl.NumberFormat(locale.value, currencyOptions).format(props.grossMargin)
    : 'N/A';
});

const overview = computed(() => {
  const left = filterForEstablishment([
    {
      icon: 'fa-regular fa-location-dot',
      value: props.address || '—'
    },
    {
      icon: 'fa-regular fa-seedling',
      value: format(startDateObject, locale.value)
    },
    {
      icon: 'fa-regular fa-wave-pulse',
      value: props.juridicalSituation || '—',
      isOnlyCompany: true
    }
  ]);

  const right = filterForEstablishment([
    {
      icon: 'fa-regular fa-tag',
      value: props.mainActivity || '—'
    },
    {
      icon: 'fa-regular fa-user',
      value: t('employeesPlural', props.employees || 0),
      isOnlyCompany: true
    },
    {
      icon: 'fa-regular fa-chart-area',
      value: grossMarginFormatted.value || '—'
    }
  ]);

  return { left, right };
});

const overviewDetails = computed(() => {
  const left = filterForEstablishment([
    {
      label: t('denomination'),
      value: props.name || '—'
    },
    {
      label: t('abbreviation'),
      value: props.abbreviation || '—'
    },
    {
      label: t('vatNumber'),
      value: props.vat || '—'
    },
    {
      label: t('typeofEnterprise'),
      value: props.legalPersonType || '—',
      isOnlyCompany: true
    }
  ]);
  const right = filterForEstablishment([
    {
      label: t('dateOfCreation'),
      value: props.startDate ? props.startDate : '—'
    },
    {
      label: t('firstPublication'),
      value: props.firstPublicationDate || '—',
      isOnlyCompany: true
    },
    {
      label: t('juridicalForm'),
      value: props.juridicalForm || '—',
      isOnlyCompany: true
    },
    {
      label: t('numberOfEstablishments'),
      value: t('establishmentsPlural', props.establishmentUnits || 0)
    }
  ]);

  return { left, right };
});

function filterForEstablishment(items) {
  if (props.isEstablishment) {
    return items.filter((el) => !el.isOnlyCompany);
  }

  return items;
}
</script>

<template>
  <q-card flat bordered>
    <q-card-section class="q-px-lg">
      <h2 class="text-h6 text-bold q-ma-none text-uppercase">
        {{ $t('companyOverview') }}
        <!-- <q-icon
          id="company-overview-info"
          class="company-overview-info"
          name="fa-regular fa-circle-question"
          size="xs"
        />
        <q-tooltip target="#company-overview-info"> text </q-tooltip> -->
      </h2>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <!-- TOP OVERVIEWS -->
      <div class="row">
        <div class="col-12 col-lg-6">
          <q-item
            v-for="(leftOverview, index) in overview.left"
            :key="`lo-${index}`"
            dense
          >
            <q-item-section avatar>
              <q-icon :name="leftOverview.icon" color="primary" size="xs" />
            </q-item-section>
            <q-item-section>
              <span class="value">{{ leftOverview.value }}</span>
            </q-item-section>
          </q-item>
        </div>
        <div class="col-12 col-lg-6">
          <q-item
            v-for="(rightOverview, index) in overview.right"
            :key="`ro-${index}`"
            dense
          >
            <q-item-section avatar>
              <q-icon :name="rightOverview.icon" color="primary" size="xs" />
            </q-item-section>
            <q-item-section>
              <span class="value">{{ rightOverview.value }}</span>
            </q-item-section>
          </q-item>
        </div>
      </div>

      <q-separator class="q-my-md" />

      <!-- MORE OVERVIEWS -->
      <div class="row">
        <div class="col-12 col-lg-6">
          <q-item
            v-for="(leftOverviewDetail, index) in overviewDetails.left"
            :key="`lod-${index}`"
            dense
          >
            <q-item-section avatar class="text-bold">
              {{ leftOverviewDetail.label }}:
            </q-item-section>
            <q-item-section>
              <span class="value">{{ leftOverviewDetail.value }}</span>
            </q-item-section>
          </q-item>
        </div>
        <div class="col-12 col-lg-6">
          <q-item
            v-for="(rightOverviewDetail, index) in overviewDetails.right"
            :key="`rod-${index}`"
            dense
          >
            <q-item-section avatar class="text-bold">
              {{ rightOverviewDetail.label }}:
            </q-item-section>
            <q-item-section>
              <span class="value">{{ rightOverviewDetail.value }}</span>
            </q-item-section>
          </q-item>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<style>
@media print {
  .company-overview-info {
    display: none;
  }
}
</style>

<style scoped>
.value:first-letter {
  text-transform: uppercase;
}
</style>
