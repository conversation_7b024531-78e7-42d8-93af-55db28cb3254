<template>
  <div class="row">
    <div class="company-sidebar col gt-sm">
      <CompanySidebar :company-id="$route.params.id" :features="features" />
    </div>

    <router-view
      :loading="loading"
      :company="company"
      :is-mobile="isMobile"
      :key="$route.fullPath"
      v-slot="{ Component, route }"
    >
      <Transition name="fade" mode="out-in">
        <component :key="route.path" :is="Component" />
      </Transition>
    </router-view>
  </div>
</template>

<script setup>
import { onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useHeadSafe } from '@vueuse/head';
import { useRoute } from 'vue-router';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import CompanySidebar from '@/pages/companies/components/company-sidebar.vue';

defineProps({
  noHeader: Boolean,
  isMobile: Boolean
});

const { t } = useI18n();
const route = useRoute();
const authStore = AuthStore();
const { user, features } = storeToRefs(authStore);
const companiesStore = CompaniesStore();
const { loading, company } = storeToRefs(companiesStore);

function init() {
  if (!route.params.id) return;

  companiesStore.loadCompany({
    id: route.params.id,
    customerShortId: user.value?.customerShortId
  });
}

onMounted(() => init());

watch(
  () => route.params.id,
  () => init()
);

const nameOrId = company.value?.name || route.params.id;

useHeadSafe({
  title: [t('company-title'), nameOrId].join(' - ')
});
</script>

<style scoped>
.company-sidebar {
  max-width: 250px;
}
</style>
