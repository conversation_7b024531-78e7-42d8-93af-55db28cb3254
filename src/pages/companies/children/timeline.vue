<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <!-- FILTERS -->
    <div class="timeline-fitlers flex items-center justify-end q-pb-md">
      <!-- SEVERITY -->
      <q-select
        v-model="params.severities"
        :options="severities"
        :loading="loading"
        :label="$t('severity')"
        :display-value="
          Array.isArray(params.severities) && params.severities.length
            ? `${$t('selectedSeverities')}: ${params.severities.length}`
            : null
        "
        :disable="!isAllowedForTimeline"
        emit-value
        multiple
        square
        filled
        dense
        :bg-color="$q.dark.isActive ? 'transparent' : 'white'"
        :label-color="$q.dark.isActive ? null : 'blue-grey-10'"
        class="q-mb-sm shadow-2 events-filter-select text-uppercase"
      >
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon
                :name="
                  Array.isArray(params.severities) &&
                  params.severities.includes(scope.opt.value)
                    ? 'fa-regular fa-square-check'
                    : 'fa-regular fa-square'
                "
                size="xs"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-size-small">
                {{ scope.opt.label }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>

      <!-- EVENT TYPES -->
      <q-select
        v-model="params.eventTypes"
        :options="eventOptions"
        :loading="loading"
        :label="$t('event')"
        :display-value="
          Array.isArray(params.eventTypes) && params.eventTypes.length
            ? `${$t('selectedEvents')}: ${params.eventTypes.length}`
            : null
        "
        :disable="!isAllowedForTimeline"
        emit-value
        use-input
        input-debounce="0"
        multiple
        square
        filled
        dense
        :bg-color="$q.dark.isActive ? 'transparent' : 'white'"
        :label-color="$q.dark.isActive ? null : 'blue-grey-10'"
        class="q-mb-sm shadow-2 events-filter-select text-uppercase"
        @filter="filterEvents"
      >
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon
                :name="
                  Array.isArray(params.eventTypes) &&
                  params.eventTypes.includes(scope.opt.value)
                    ? 'fa-regular fa-square-check'
                    : 'fa-regular fa-square'
                "
                size="xs"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-size-small">
                {{ scope.opt.label }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>
      <!-- CLEAR -->
      <q-btn
        :label="isMobile ? '' : $t('clear')"
        :disable="loading || !isAllowedForTimeline"
        :color="$q.dark.isActive ? null : 'white'"
        :text-color="$q.dark.isActive ? null : 'blue-grey-10'"
        class="events-clear"
        icon="fa-regular fa-filter-slash"
        @click="clearFilters"
      />
    </div>

    <div class="events row">
      <FeatureTeasing
        v-if="!isAllowedForTimeline"
        :feature="COMPANY_TIMELINE"
        class="timeline-feature-teasing"
        overlay
      />

      <div :class="['col-10', { isTeasing: !isAllowedForTimeline }]">
        <!-- LIST -->

        <q-timeline layout="loose">
          <div
            v-for="{ year, events } in eventsToDisplay"
            :key="year"
            :data-id="year"
            v-intersection="onIntersection"
          >
            <div
              :id="`year-${year}`"
              class="text-h5 text-center timeline-section-title"
            >
              <span>{{ year }}</span>
            </div>

            <TimelineItem
              v-for="event in events"
              :key="event.identifier"
              :is-mobile="isMobile"
              :severity="event.severity"
              :type="event.type"
              :diff-from="event.from"
              :diff-to="event.to"
              :date="event.date"
            />
          </div>
        </q-timeline>
      </div>

      <!-- SIDEBAR -->
      <div class="col-2">
        <TimelineSidebar
          :is-mobile="isMobile"
          :events="events.list"
          :max="events.max"
          :in-view="inView"
        />
      </div>
    </div>
  </CompanyLayout>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useHead } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { AuthStore } from '@/pages/auth/store';
import { CompaniesStore } from '@/pages/companies/store';
import { COMPANY_TIMELINE } from '@/utils/features';
import eventsTypesSource from '@/pages/monitoring/data/event-types';
import severitiesSource from '@/pages/monitoring/data/severities';
import { timelineData } from '@/pages/companies/data/timeline-data';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import TimelineItem from '@/pages/companies/components/timeline-item.vue';
import TimelineSidebar from '@/pages/companies/components/timeline-sidebar.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

defineProps({
  company: Object,
  isMobile: Boolean
});

const { t } = useI18n();
const route = useRoute();

useHead({
  title: [t('timeline'), t('company-title'), route.params?.id].join(' - ')
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);

const defaultFromDate = '1900-01-01';
const defaultToDate = '2024-01-01';

const companiesStore = CompaniesStore();
const { loading, events } = storeToRefs(companiesStore);

const params = ref({
  from: defaultFromDate,
  to: defaultToDate,
  eventTypes: [],
  severities: []
});

const eventsTypes = eventsTypesSource.map((event) => ({
  label: t(event),
  value: event
}));
const eventOptions = ref(eventsTypes);

const severities = severitiesSource.map((severity) => ({
  label: t(severity),
  value: severity
}));

const isAllowedForTimeline = computed(() =>
  features.value.includes(COMPANY_TIMELINE)
);
const eventsToDisplay = computed(() => {
  if (!isAllowedForTimeline.value) return timelineData;
  return events.value.list;
});

function init() {
  if (!isAllowedForTimeline.value) return;

  if (events.value.id === null || events.value.id !== route.params.id) {
    companiesStore.loadEvents({
      companyId: route.params.id,
      params: params.value
    });
  }
}

onMounted(() => init());
watch(
  () => features.value,
  () => init()
);

function filterEvents(val, update) {
  update(() => {
    const needle = val.toLocaleLowerCase();
    eventOptions.value = eventsTypes.filter(
      (event) => event.label.toLocaleLowerCase().indexOf(needle) > -1
    );
  });
}

function clearFilters() {
  params.value.from = defaultFromDate;
  params.value.to = defaultToDate;
  params.value.eventTypes = [];
}

const inView = ref([]);

function add(i) {
  remove(i);
  inView.value.push(i);
  inView.value.sort(sortAtoi);
}

function remove(i) {
  let index;
  while ((index = inView.value.indexOf(i)) > -1) {
    inView.value.splice(index, 1);
    inView.value.sort(sortAtoi);
  }
}

function sortAtoi(a, b) {
  return Number(a) - Number(b);
}

function onIntersection(entry) {
  if (entry.isIntersecting === true) {
    add(entry.target.dataset.id);
  } else {
    remove(entry.target.dataset.id);
  }
}

const breadcrumbs = [
  {
    label: t('events')
  },
  {
    label: t('timeline')
  }
];
</script>

<style scoped>
.timeline-feature-teasing {
  position: absolute;
  top: 12px;
  left: 0;
  right: 0;
}

.events {
  position: relative;
}

.isTeasing {
  filter: blur(3px);
}

.events-filter-select {
  min-width: 8rem;
  max-width: 15rem;
  margin-bottom: 0;
  border-radius: 0.25rem;
  overflow: hidden;
}

.events-filter-select :deep(.q-field__control) {
  height: 100%;
}

.events-clear {
  height: 40px;
}

.timeline-fitlers {
  gap: 1rem;
  flex-wrap: nowrap;
}

.timeline-section-title {
  position: relative;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.timeline-section-title:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border);
}

.timeline-section-title span {
  position: relative;
  background-color: var(--primarySmoke);
  padding: 0 1rem;
}
</style>
