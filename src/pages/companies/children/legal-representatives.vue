<script setup>
import { ref, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import CompanyLegalRepresentatives from '@/pages/companies/components/company-legal-representatives.vue';
import LegalRepresentativeSidebar from '@/pages/companies/components/legal-representative-sidebar.vue';
import functionsColumns from '@/pages/companies/data/functions-columns';

defineProps({ company: Object, isMobile: Boolean });

const { t } = useI18n();
const route = useRoute();

const companiesStore = CompaniesStore();
const { loading, functions, representatives } = storeToRefs(companiesStore);

const isSidebarToggled = ref(false);
const personToSearch = ref('');

function toggleSidebar(personName = '') {
  personToSearch.value = personName;
  isSidebarToggled.value = !isSidebarToggled.value;
}

useHead({
  title: [t('legalRepresentatives'), t('company-title'), route.params?.id].join(
    ' - '
  )
});

onBeforeMount(() => {
  if (functions.value.id === null || functions.value.id !== route.params.id) {
    companiesStore.loadFunctions(route.params.id);
    companiesStore.loadRepresentatives(route.params.id);
  }
});

const breadcrumbs = [
  {
    label: t('structure')
  },
  {
    label: t('legalRepresentatives')
  }
];
</script>

<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <!-- REPRESENTATIVES -->
    <CompanyLegalRepresentatives
      :loading="loading"
      :is-mobile="isMobile"
      :columns="functionsColumns"
      :list="representatives.list"
      :title="$t('representatives')"
      @toggle-sidebar="toggleSidebar"
    />

    <!-- SIDEBAR -->
    <q-drawer
      v-model="isSidebarToggled"
      :width="420"
      behavior="mobile"
      side="right"
      elevated
      overlay
      @update:model-value="isSidebarToggled = false"
    >
      <q-scroll-area class="fit">
        <LegalRepresentativeSidebar
          :active="isSidebarToggled"
          :person-name="personToSearch"
          :company-id="company?.id"
          @close="toggleSidebar"
        />
      </q-scroll-area>
    </q-drawer>

    <!-- No data -->
    <q-card
      v-if="!loading && representatives.list.length === 0"
      bordered
      square
      flat
      style="margin-top: -2px"
    >
      <q-card-section>{{ $t('noLegalRepresentatives') }}</q-card-section>
    </q-card>

    <!-- FUNCTIONS -->
    <CompanyLegalRepresentatives
      :loading="loading"
      :is-mobile="isMobile"
      :columns="functionsColumns"
      :list="functions.list"
      :title="$t('functions')"
    />
    <!-- No data -->
    <q-card
      v-if="!loading && functions.list.length === 0"
      bordered
      square
      flat
      style="margin-top: -2px"
    >
      <q-card-section>{{ $t('noFuntions') }}</q-card-section>
    </q-card>
  </CompanyLayout>
</template>
