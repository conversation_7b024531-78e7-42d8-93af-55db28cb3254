<template>
  <CompanyLayout :company="company" :loading="loading" :is-Mobile="isMobile">
    <div v-if="company" class="row q-col-gutter-md">
      <div class="col-12 col-md-8">
        <CompanyOverview
          :address="company.mainAddress.formatted_address"
          :start-date="company.startDate"
          :juridical-situation="company.juridicalSituation"
          :main-activity="company.mainActivity || company.firstActivity"
          :name="company.name"
          :abbreviation="company.abbreviation"
          :vat="company.vatFormatted"
          :juridical-form="company.juridicalForm"
          :legal-person-type="company.legalPersonType"
          :establishment-units="company.establishmentUnits"
          :first-publication-date="firstPublicationDate"
          :employees="company.employees"
          :gross-margin="company.grossMargin"
          class="q-mb-md"
        />
        <CompanyContact
          :company="company"
          :address="company.mainAddress"
          :addresses="company.otherAddresses"
          :website="company.contacts.website"
          :email="company.contacts.email"
          :mobile="company.contacts.phone"
          :phone="company.contacts.faxNumber"
          show-actions
          class="q-mb-md"
        />
        <CompanyRevenuesSummary
          v-if="latestStatement?.data"
          :identifier="company.identifier"
          :current-ratio="latestStatement.data.currentRatio"
          :current-ratio-growth="latestStatement.data.currentRatioGrowth"
          :employees="latestStatement.data.employees"
          :net-cash="latestStatement.data.netCash"
          :net-cash-growth="latestStatement.data.netCashGrowth"
          :net-cash-currency="latestStatement.data.netCashCurrency"
          :return-on-equity="latestStatement.data.returnOnEquity"
          :return-on-equity-growth="latestStatement.data.returnOnEquityGrowth"
          :cash-flow-to-debt="latestStatement.data.cashFlowToDebt"
          :cash-flow-to-debt-growth="latestStatement.data.cashFlowToDebtGrowth"
          :cash-flow-to-debt-currency="
            latestStatement.data.cashFlowToDebtCurrency
          "
          :gross-operating-margin="latestStatement.data.grossOperatingMargin"
          :gross-operating-margin-growth="
            latestStatement.data.grossOperatingMarginGrowth
          "
          :gross-operating-margin-currency="
            latestStatement.data.grossOperatingMarginCurrency
          "
          :accounting-year="latestStatement.data.accountingYear"
        />
        <CompanySectors
          :activities="company.activities"
          :characteristics="company.characteristics"
          :authorizations="company.authorizations"
          class="q-mt-sm"
        />
      </div>

      <!-- RIGHT SIDEBAR -->
      <div class="col">
        <CompanyEntities
          :identifier="company.identifier"
          :establishment-units="company.establishmentUnits"
          :legal-representative-persons="company.legalRepresentativePersons"
        />
        <CompanyMap :address="company.mainAddress" class="q-mb-sm" />
        <CompanyKeyDates
          :annual-meeting-month="company.financialMeta?.annual_meeting_month"
          :end-fiscal-year-day="company.financialMeta?.end_fiscal_year_day"
          :end-fiscal-year-month="company.financialMeta?.end_fiscal_year_month"
          :exc-start="company.financialMeta?.exceptional_fiscal_year_begin"
          :exc-end="company.financialMeta?.exceptional_fiscal_year_end"
          class="q-mb-sm"
        />
        <CompanyAddressHistory :addresses="company.addresses" class="q-mb-sm" />
        <CompanyNameHistory :names="company.names" class="q-mb-sm" />
        <CompanySources :vat="company.vat" is-flat />
      </div>
    </div>
  </CompanyLayout>
</template>

<script setup>
import { watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import CompanyOverview from '@/pages/companies/components/company-overview.vue';
import CompanySources from '@/pages/companies/components/company-sources.vue';
import CompanyEntities from '@/pages/companies/components/company-entities.vue';
import CompanyMap from '@/pages/companies/components/company-map.vue';
import CompanyContact from '@/pages/companies/components/company-contact.vue';
import CompanyRevenuesSummary from '@/pages/companies/components/company-revenues-summary.vue';
import CompanyKeyDates from '@/pages/companies/components/company-key-dates.vue';
import CompanySectors from '@/pages/companies/components/company-sectors.vue';
import CompanyAddressHistory from '@/pages/companies/components/company-address-history.vue';
import CompanyNameHistory from '@/pages/companies/components/company-name-history.vue';
import { formatDate } from '@/utils/date';

defineProps({
  isMobile: Boolean
});

const { t, locale } = useI18n();
const route = useRoute();

useHead({
  title: [t('profile'), t('company-title'), route.params.id].join(' - ')
});

const companiesStore = CompaniesStore();
const { loading, company, latestStatement, publications } =
  storeToRefs(companiesStore);

watch(company, (watchedCompany) => {
  if (!watchedCompany) return;

  companiesStore.loadLatestStatement(watchedCompany.identifier);
  companiesStore.loadCompanyPublications(watchedCompany.identifier);
});

const firstPublicationDate = computed(() => {
  if (!publications.value.list.length) return;

  const sortedPublications = [...publications.value.list].sort((a, b) => {
    return new Date(a.originalDate) - new Date(b.originalDate);
  });

  if (!sortedPublications[0]) return null;

  return formatDate(sortedPublications[0].publicationDate, locale.value);
});
</script>
