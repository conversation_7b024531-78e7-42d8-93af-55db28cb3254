<script setup>
import { onBeforeMount } from 'vue';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import CompanyEstablishments from '@/pages/companies/components/company-establishments.vue';
import establishmentsColumns from '@/pages/companies/data/establishments-columns';

defineProps({ company: Object, isMobile: Boolean });

const { t } = useI18n();
const route = useRoute();
const companiesStore = CompaniesStore();
const { loading, establishments } = storeToRefs(companiesStore);

useHead({
  title: [t('establishments'), t('company-title'), route.params?.id].join(' - ')
});

onBeforeMount(() => {
  if (
    establishments.value.id === null ||
    establishments.value.id !== route.params.id
  ) {
    companiesStore.loadEstablishments(route.params.id);
  }
});

const breadcrumbs = [
  {
    label: t('establishments')
  }
];
</script>

<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <CompanyEstablishments
      :loading="loading"
      :is-Mobile="isMobile"
      :columns="establishmentsColumns"
      :establishments="establishments.list"
    />

    <!-- No data -->
    <q-card
      v-if="!loading && establishments.list.length === 0"
      bordered
      square
      flat
      style="margin-top: -2px"
    >
      <q-card-section>{{ $t('noLegalRepresentatives') }}</q-card-section>
    </q-card>
  </CompanyLayout>
</template>
