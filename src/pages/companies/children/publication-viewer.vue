<script setup>
import { onMounted, onUnmounted, ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { CompaniesStore } from '@/pages/companies/store';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';

const props = defineProps({
  noHeader: Boolean,
  fullPage: Boolean,
  company: Object
});

const { t } = useI18n();
const route = useRoute();
const companiesStore = CompaniesStore();
const { publication } = storeToRefs(companiesStore);
const publicationLoaded = ref(false);

onMounted(async () => {
  if (
    publication.value.id === null ||
    publication.value.id !== route.params.identifier
  ) {
    await companiesStore.loadPublicationPDF(route.params.identifier);
    publicationLoaded.value = true;
  }
});

onUnmounted(() => {
  publication.value = false;
});

const breadcrumbs = computed(() => [
  {
    label: t('events')
  },
  {
    label: t('publications'),
    to: `/companies/${props.company?.id}/publications`
  },
  {
    label: t('publication')
  }
]);
</script>

<template>
  <CompanyLayout
    :no-header="noHeader"
    :full-page="fullPage"
    :is-Mobile="isMobile"
  >
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <div class="pdf-viewer shadow-2">
      <template v-if="publicationLoaded">
        <iframe
          title="pdf"
          :src="publication?.file"
          width="100%"
          height="100%"
          frameBorder="0"
        />
      </template>

      <q-linear-progress v-else indeterminate color="primary" />
    </div>
  </CompanyLayout>
</template>

<style scope>
.pdf-viewer {
  width: 100%;
  height: calc(100vh - 100px);
}
</style>
