<script setup>
import { useHead } from '@vueuse/head';
import { useRoute } from 'vue-router';
import { CompaniesStore } from '@/pages/companies/store';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import CompanySavedList from '@/pages/companies/components/saved-list.vue';
import CompanyNotes from '@/pages/companies/components/notes.vue';
import CompanyTimeline from '@/pages/companies/components/timeline.vue';

const route = useRoute();
const { t } = useI18n();

useHead({
  title: [t('overview-title'), t('company-title'), route.params.id].join(' - ')
});

const companiesStore = CompaniesStore();
const { loading, companyWatchlists } = storeToRefs(companiesStore);

companiesStore.loadCompanyWatchlists(route.params.id);
</script>

<template>
  <CompanyLayout :is-Mobile="isMobile">
    <CompanySavedList :loading="loading" :watchlists="companyWatchlists" />

    <div class="row q-py-md">
      <div class="col-12 col-md-6 q-pr-md-md">
        <CompanyNotes />
      </div>

      <div class="col-12 col-md-6 q-pl-md-md">
        <CompanyTimeline />
      </div>
    </div>
  </CompanyLayout>
</template>
