<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <q-linear-progress
      v-if="loading"
      color="primary"
      class="q-mt-sm"
      indeterminate
    />

    <div v-if="!loading && !getStatementsSize">
      {{ $t('noDataAvailable') }}
    </div>

    <div class="text-center">
      <q-btn
        v-show="isMobile"
        :label="$t('sections')"
        icon="fa-regular fa-sidebar-flip"
        flat
        rounded
        size="sm"
        color="blue-grey"
        class="q-mb-md"
        @click="onToggleSidebar"
      />
    </div>

    <div class="company-financials-graph">
      <FeatureTeasing
        v-if="!isAllowedForFullFinancials"
        :feature="COMPANY_FULL_FINANCIALS"
        overlay
      />
      <q-card :class="{ isTeasing: !isAllowedForFullFinancials }">
        <div :class="isMobile ? 'graphs-mobile' : 'row'">
          <div class="col">
            <div :class="['graph-render', { extended: isSidebarOpen }]">
              <VueApexCharts
                id="graphs"
                class="company-graph"
                :options="graphs.settings"
                :series="graphs.series.length > 0 ? graphs.series : []"
                :height="height"
                :key="graphs.series.length"
                type="line"
                @mounted="triggerResize"
              />
            </div>
          </div>
          <!-- SIDEBAR -->
          <div :class="['col graph-sidebar-col', { extended: isSidebarOpen }]">
            <q-btn
              v-show="!isMobile"
              flat
              rounded
              size="sm"
              class="toggle-sidebar"
              @click="onToggleSidebar"
            >
              <q-icon name="fa-regular fa-sidebar-flip" color="blue-grey" />
            </q-btn>
            <div v-show="isSidebarOpen" class="graph-sidebar">
              <div class="flex items-center justify-between">
                <div class="font-size-small text-bold q-pb-md">
                  {{ $t('sections') }}
                </div>
                <q-btn
                  v-show="isMobile"
                  icon="fa-regular fa-close"
                  class="q-mb-md"
                  flat
                  rounded
                  @click="onToggleSidebar"
                />
              </div>
              <q-list separator>
                <q-item
                  v-for="option in graphs.options"
                  :key="option.id"
                  tag="label"
                  v-ripple
                  dense
                >
                  <q-item-section side top>
                    <q-checkbox
                      :model-value="graphs.selectedSeries"
                      :val="option.id"
                      name="graphs"
                      @update:model-value="
                        companiesStore.selectGraphSerie(option.id)
                      "
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>
                      {{ option.label }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn-group
                      v-if="graphs.selectedSeries.includes(option.id)"
                      rounded
                    >
                      <q-btn
                        v-for="optionType in graphs.types"
                        :key="optionType"
                        :color="optionType === option.type ? 'white' : 'grey-2'"
                        size="xs"
                        class="q-px-sm"
                        rounded
                        @click="
                          companiesStore.updateGraphType(option.id, optionType)
                        "
                      >
                        <q-icon
                          :name="`fa-regular fa-${iconByOptionType[optionType]}`"
                          :color="
                            optionType === option.type ? 'primary' : 'grey'
                          "
                          size="xxs"
                        />
                      </q-btn>
                    </q-btn-group>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
      </q-card>
    </div>
  </CompanyLayout>
</template>

<script setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  defineAsyncComponent
} from 'vue';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_FULL_FINANCIALS } from '@/utils/features';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';

const VueApexCharts = defineAsyncComponent(() => import('vue3-apexcharts'));

const props = defineProps({
  isMobile: Boolean,
  company: Object
});

const route = useRoute();
const { t } = useI18n();

useHead({
  title: [t('graphs'), t('company-title'), route.params.id].join(' - ')
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForFullFinancials = computed(() =>
  features.value.includes(COMPANY_FULL_FINANCIALS)
);

const companiesStore = CompaniesStore();
const { loading, graphs, getStatementsSize } = storeToRefs(companiesStore);

const height = computed(() => {
  if (props.isMobile) return '400px';

  return Math.max(window.innerHeight - 190, 350);
});

const iconByOptionType = {
  line: 'chart-line',
  column: 'chart-column',
  area: 'chart-area'
};

function init() {
  if (isAllowedForFullFinancials.value) {
    const selection = route.hash ? route.hash.substring(1) : null;
    companiesStore.loadStatementIdentifiers({
      companyIdentifier: route.params.id,
      type: 'graph',
      selection
    });
  }
}

watch(
  () => isAllowedForFullFinancials.value,
  () => init()
);

onMounted(() => init());
onUnmounted(() => companiesStore.clearSelectionGraphSerie());

const isSidebarOpen = ref(!props.isMobile);

async function onToggleSidebar() {
  isSidebarOpen.value = !isSidebarOpen.value;
  triggerResize();
}

function triggerResize() {
  // trigger resize to start the width watcher
  window.dispatchEvent(new Event('resize'));
}

const breadcrumbs = [
  {
    label: t('financials')
  },
  {
    label: t('graphs')
  }
];
</script>

<style scoped>
.company-financials-graph {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

.isTeasing {
  filter: blur(3px);
}

.graph-render {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem 0.25rem;
  overflow: hidden;
}

@media (min-width: 600px) {
  .graph-render {
    padding: 1rem;
  }
}

.graph-sidebar-col {
  max-width: 0;
  overflow: hidden;
}

.graph-sidebar-col.extended {
  max-width: 350px;
}

.graphs-mobile .graph-sidebar-col.extended {
  max-width: 100%;
}

.graph-render :deep(.apexcharts-toolbar) {
  margin-right: 50px;
  margin-top: 1px;
}

.graph-render.extended :deep(.apexcharts-toolbar) {
  margin-right: 0;
  margin-top: 1px;
}

.company-graph {
  display: inline-flex;
  width: 100%;
}

.graph-sidebar {
  height: 100%;
  padding: 1rem;
  border-left: 1px solid var(--border);
}

.graphs-mobile .graph-sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9000;
  background-color: var(--white);
  overflow-y: auto;
  overflow-x: hidden;
}

.toggle-sidebar {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 1;
}

@media (min-width: 600px) {
  .toggle-sidebar {
    left: auto;
    right: 1rem;
  }
}
</style>
