<script setup>
import { watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_PUBLICATIONS } from '@/utils/features';
import {
  publicationsTypes,
  publicationsFakeData
} from '@/pages/companies/data/publications-data';
import publicationColumns from '@/pages/companies/data/publications-columns';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import CompanyPublications from '@/pages/companies/components/company-publications.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

defineProps({
  isMobile: Boolean,
  company: {
    type: Object,
    default: null
  }
});

const { t } = useI18n();
const route = useRoute();

useHead({
  title: [t('publications'), t('company-title'), route.params.id].join(' - ')
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForPublications = computed(() =>
  features.value.includes(COMPANY_PUBLICATIONS)
);

const companiesStore = CompaniesStore();
const { publications, selectedPublicationType } = storeToRefs(companiesStore);

function init() {
  if (!isAllowedForPublications.value) return;

  if (
    publications.value.id === null ||
    publications.value.id !== route.params.id
  ) {
    companiesStore.loadCompanyPublications(route.params.id);
  }
}

watch(
  () => isAllowedForPublications.value,
  () => init()
);

onMounted(() => init());

const pagination = {
  rowsPerPage: 10,
  page: 1
};

const pageOptions = [10, 20, 50, 100];

const formattedPublicationsTypes = computed(() => {
  return publicationsTypes
    .map((type) => {
      const count = publications.value.list.filter(
        (item) => item.type === type
      ).length;
      return {
        disable: count === 0,
        value: type,
        label: `${t(type)} (${count})`
      };
    })
    .filter((type) => !type.disable);
});

function onSelectPublicationType(type) {
  if (!isAllowedForPublications.value) return;
  companiesStore.loadCompanyPublications(publications.value.id, type || null);
}

const breadcrumbs = [
  {
    label: t('events')
  },
  {
    label: t('publications')
  }
];
</script>

<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <div class="flex justify-end items-center q-pb-md">
      <q-select
        :loading="publications.loading"
        :model-value="selectedPublicationType"
        :label="$t('publicationType')"
        :options="formattedPublicationsTypes"
        :bg-color="$q.dark.isActive ? 'transparent' : 'white'"
        :label-color="$q.dark.isActive ? null : 'blue-grey-10'"
        :disable="!isAllowedForPublications"
        color="primary"
        class="shadow-2 border-rounded text-uppercase"
        clearable
        square
        filled
        dense
        emit-value
        style="min-width: 175px"
        @update:model-value="onSelectPublicationType"
      />
    </div>

    <q-card flat bordered>
      <FeatureTeasing
        v-if="!isAllowedForPublications"
        :feature="COMPANY_PUBLICATIONS"
        overlay
      />
      <CompanyPublications
        :loading="publications.loading"
        :is-mobile="isMobile"
        :columns="publicationColumns"
        :publications="
          isAllowedForPublications ? publications.list : publicationsFakeData
        "
        :options="pageOptions"
        :pagination="pagination"
        :disabled="!isAllowedForPublications"
        :class="{ isTeasing: !isAllowedForPublications }"
      />
    </q-card>
  </CompanyLayout>
</template>

<style scoped>
.isTeasing {
  filter: blur(3px);
}
</style>
