<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <div class="flex justify-end items-center q-pb-md">
      <q-checkbox
        v-model="isHiddingEmptyRows"
        :label="$t('hideEmptyValues')"
        :disable="loading || !isAllowedForFullFinancials"
        name="hideEmptyValues"
        dense
      />
    </div>

    <div class="company-financials-container">
      <FeatureTeasing
        v-if="!isAllowedForFullFinancials"
        :feature="COMPANY_FULL_FINANCIALS"
        overlay
      />

      <template v-if="isAllowedForFullFinancials">
        <q-card v-for="group in groups" :key="group" class="q-mb-lg">
          <CompanyFinancialsTable
            :loading="loading"
            :is-mobile="isMobile"
            :columns="ratios.columns"
            :rows="filteredRows[group]"
            :title="`${$t(group)} (${filteredRows[group]?.length || 0})`"
            withTitle
          />
        </q-card>
      </template>
    </div>
  </CompanyLayout>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_FULL_FINANCIALS } from '@/utils/features';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import CompanyFinancialsTable from '@/pages/companies/components/company-financials-table.vue';

defineProps({
  isMobile: Boolean,
  company: {
    type: Object,
    default: null
  }
});

const route = useRoute();
const { t } = useI18n();
const isHiddingEmptyRows = ref(false);

useHead({
  title: [t('key-ratio-title'), t('company-title'), route.params.id].join(' - ')
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForFullFinancials = computed(() =>
  features.value.includes(COMPANY_FULL_FINANCIALS)
);

const groups = ['profitability', 'solvency', 'liquidity', 'added_value'];
const companiesStore = CompaniesStore();
const { loading, ratios } = storeToRefs(companiesStore);

function init() {
  if (isAllowedForFullFinancials.value) {
    companiesStore.loadStatementIdentifiers({
      companyIdentifier: route.params.id,
      type: 'table',
      context: 'ratios'
    });
  }
}

watch(
  () => isAllowedForFullFinancials.value,
  () => init()
);

onMounted(() => init());

const filteredRows = computed(() => {
  return Object.keys(ratios.value.rows).reduce((all, key) => {
    const values = ratios.value.rows[key];

    if (!isHiddingEmptyRows.value) return { ...all, [key]: values };

    return {
      ...all,
      [key]: values.filter((val) => !val.emptyValues)
    };
  }, {});
});

const breadcrumbs = [
  {
    label: t('financials')
  },
  {
    label: t('keyRatios')
  }
];
</script>

<style scoped>
.company-financials-container {
  position: relative;
  min-height: 500px;
}
</style>
