<script setup>
import { watch, computed } from 'vue';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { statements } from '@/pages/companies/composables/prepare-statements';
import { keyFinancialsTeasing } from '@/pages/companies/data/key-financials-teasing';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import Breadcrumb from '@/pages/companies/components/breadcrumb.vue';
import CompanyFinancialsOverview from '@/pages/companies/components/company-financials-overview.vue';

const props = defineProps({
  isMobile: Boolean,
  company: {
    type: Object,
    default: null
  }
});

const route = useRoute();
const { t } = useI18n();

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForFinancialsOverview = computed(() =>
  features.value.includes('COMPANY_KEY_FINANCIALS')
);

const companiesStore = CompaniesStore();
const { latestStatement } = storeToRefs(companiesStore);

const preparedStatements = computed(() => {
  if (!isAllowedForFinancialsOverview.value) return keyFinancialsTeasing;
  return statements.value;
});

useHead({
  title: [
    t('financialsOverview'),
    t('company-title'),
    props.company?.identifier || route.params?.id
  ].join(' - ')
});

// preloaded company
if (props.company?.identifier && isAllowedForFinancialsOverview.value) {
  companiesStore.loadLatestStatement(props.company.identifier);
}

watch(
  () => props.company,
  (watchedCompany) => {
    if (watchedCompany && isAllowedForFinancialsOverview.value) {
      companiesStore.loadLatestStatement(watchedCompany.identifier);
    }
  }
);

const breadcrumbs = [
  {
    label: t('financials')
  },
  {
    label: t('financialsOverview')
  }
];
</script>

<template>
  <CompanyLayout :company="company" :is-Mobile="isMobile">
    <Breadcrumb
      v-if="company"
      :company-name="company.name"
      :company-id="company.id"
      :items="breadcrumbs"
    />

    <h2 class="text-h6">
      {{ $t('accountDate') }}: {{ latestStatement?.overview?.date }}
    </h2>

    <CompanyFinancialsOverview
      :teasing="!isAllowedForFinancialsOverview"
      :statements="preparedStatements"
    />
  </CompanyLayout>
</template>
