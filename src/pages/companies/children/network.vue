<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useHead } from '@vueuse/head';
import { Network } from 'vis-network';
import { DataSet, DataView } from 'vis-data/peer';
import { BUSINESS_GRAPH } from '@/utils/features';
import { CompaniesStore } from '@/pages/companies/store';
import { AuthStore } from '@/pages/auth/store';
import { featureTeasingGraphData } from '@/pages/companies/data/feature-teasing-graph-data';
import CompanyLayout from '@/pages/companies/components/layout.vue';
import NetworkSearch from '@/pages/companies/components/network-search.vue';
import NetworkEdgeSidebar from '@/pages/companies/components/network-edge-sidebar.vue';
import NetworkNodeSidebar from '@/pages/companies/components/network-node-sidebar.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';
import businessGraphOptions from '@/pages/companies/data/business-graph-options.json';

defineProps({
  company: {
    type: Object,
    default: null
  },
  noHeader: Boolean,
  fullPage: Boolean,
  withSearch: Boolean,
  isMobile: Boolean
});

const { t } = useI18n();
const route = useRoute();
const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const companiesStore = CompaniesStore();
const { loading, businessGraph } = storeToRefs(companiesStore);

useHead({
  title: [t('businessGraph'), t('company-title'), route.params?.id].join(' - ')
});

const isAllowToUseGraph = computed(() =>
  features.value.includes(BUSINESS_GRAPH)
);

const preparedBusinessGraphData = computed(() => {
  if (!isAllowToUseGraph.value) return featureTeasingGraphData;
  return businessGraph.value;
});

async function loadCompanyGraph(companyId) {
  if (companyId) {
    await companiesStore.loadCompanyGraph(companyId);
  }
}

async function loadPersonGraph(personParams) {
  await companiesStore.loadPersonGraph(personParams);
}

const network = ref(null);
const networkNodes = ref(null);
const showInactives = ref(true);
const selectedNode = ref(null);
const selectedEdge = ref(null);
const isCleared = computed(() => {
  if (preparedBusinessGraphData.value.nodes) {
    return preparedBusinessGraphData.value.nodes?.length === 0;
  }

  return true;
});

function clear() {
  if (networkNodes.value === null) return;
  network.value.destroy();
  companiesStore.clearGraph();
}

function deselect() {
  selectedNode.value = null;
  selectedEdge.value = null;
}

const graphLoading = ref(false);

async function renderGraph() {
  const nodes = new DataSet(preparedBusinessGraphData.value.nodes);
  const nodesView = new DataView(nodes, {
    filter: (node) => {
      if (showInactives.value) return true;
      return node.isActive;
    }
  });
  networkNodes.value = nodesView;

  const edges = new DataSet(preparedBusinessGraphData.value.edges);
  const edgesView = new DataView(edges);

  network.value = new Network(
    document.getElementById('container'),
    {
      nodes: nodesView,
      edges: edgesView
    },
    businessGraphOptions
  );

  network.value.on('selectEdge', (event) => {
    deselect();

    const edgeData = preparedBusinessGraphData.value.edges.find(
      (edge) => edge.id === event.edges[0]
    );

    if (!edgeData.dashes) {
      selectedEdge.value = edgeData;
    }
  });

  network.value.on('selectNode', async (event) => {
    deselect();

    const nodeData = preparedBusinessGraphData.value.nodes.find(
      (node) => node.id === event.nodes[0]
    );

    selectedNode.value = nodeData;

    if (nodeData.type === 'person') {
      const infos = nodeData.infos.find((info) => info.label === 'companies');

      if (!infos) return;

      const personCompanyIds = infos.value.map((info) => info.id);

      if (!personCompanyIds.length) return;

      await companiesStore.searchCompanies(personCompanyIds);

      selectedNode.value?.infos.forEach((info) => {
        if (info.label === 'companies') {
          info.value.forEach((infoEntity) => {
            const companyInfo = preparedBusinessGraphData.value.companies.find(
              (company) => company.id === infoEntity.id
            );

            infoEntity.name = companyInfo?.name || infoEntity.id;
          });
        }
      });
    }
  });

  network.value.on('deselectNode', () => {
    selectedNode.value = null;
  });

  network.value.on('doubleClick', async (event) => {
    const nodeData = businessGraph.value.nodes.find(
      (node) => node.id === event.nodes[0]
    );
    selectedNode.value = null;

    if (nodeData?.type === 'person') {
      const personParams = {
        identifier: nodeData.id,
        identifiers: nodeData.personUUIDs,
        companyIdentifiers: nodeData.person_companies
      };

      await loadPersonGraph(personParams);
      renderGraph();
    }

    if (nodeData?.type === 'company' && nodeData.id) {
      await loadCompanyGraph(nodeData.id);
      renderGraph();
    }
  });

  network.value.on('afterDrawing', () => {
    if (graphLoading.value) {
      graphLoading.value = false;
    }
  });
}

async function init() {
  clear();
  const companyId = isAllowToUseGraph.value ? route.params?.id : null;
  const fullName = isAllowToUseGraph.value
    ? route.params?.person || route.query?.person
    : null;
  const identifiers = route.query?.companies
    ? route.query?.companies.split(',')
    : [];
  const identifier = route.query?.id;

  if (fullName || companyId) {
    graphLoading.value = true;
  }

  if (fullName) {
    await loadPersonGraph({ identifier, fullName, identifiers });
    renderGraph();
    return;
  }

  if (companyId) {
    await loadCompanyGraph(companyId);
    renderGraph();
  }
}

async function selectCompany({ companyId }) {
  await loadCompanyGraph(companyId);
  renderGraph();
}

watch(
  () => showInactives.value,
  () => networkNodes.value?.refresh()
);

watch(
  () => isAllowToUseGraph.value,
  () => init()
);

onMounted(() => init());
</script>

<template>
  <CompanyLayout
    :no-header="noHeader"
    :full-page="fullPage"
    :is-Mobile="isMobile"
  >
    <FeatureTeasing
      v-if="!isAllowToUseGraph && !loading"
      :feature="BUSINESS_GRAPH"
      class="feature-teasing"
      overlay
    />

    <q-linear-progress
      v-if="loading"
      color="primary"
      class="api-loader"
      indeterminate
    />

    <div :class="['business-graph', { isTeasing: !isAllowToUseGraph }]">
      <q-inner-loading :showing="loading || graphLoading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>

      <NetworkEdgeSidebar
        v-if="selectedEdge !== null"
        :selected-edge="selectedEdge"
        @deselect="deselect"
      />

      <NetworkNodeSidebar
        v-if="selectedNode !== null"
        :loading="businessGraph.loading"
        :selected-node="selectedNode"
        @deselect="deselect"
      />

      <div
        v-show="isCleared && !loading && isAllowToUseGraph && withSearch"
        class="graph-intro"
      >
        {{ $t('networkGraphIntro') }}
      </div>
      <div v-show="!loading" class="graph-container" id="container" />
      <div class="top-options">
        <q-card class="top-option">
          <q-checkbox
            v-model="showInactives"
            label="Show inatives"
            class="q-pr-sm"
            name="showInactives"
          />
        </q-card>
        <q-card v-show="withSearch" class="top-option top-option-search">
          <NetworkSearch
            :loading="loading"
            :disable="isAllowToUseGraph"
            :include-inactives="showInactives"
            @select="selectCompany"
            @clear="clear"
          />
        </q-card>
      </div>
      <q-card class="graph-legend">
        <div class="legend company">{{ $t('company') }}</div>
        <div class="legend person">{{ $t('person') }}</div>
        <div class="legend address">{{ $t('address') }}</div>
        <div class="legend external">{{ $t('externalCompany') }}</div>
      </q-card>
    </div>
  </CompanyLayout>
</template>

<style scoped>
.graph-container {
  text-align: center;
  width: 100%;
  height: 100dvh;
}

.business-graph {
  position: relative;

  --graph-company: #1f72c1;
  --graph-person: #6ab759;
  --graph-address: #fdcc62;
  --graph-inactive: #283d50;
}

.top-options {
  display: flex;
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 1;
  width: calc(100% - 6rem);
}

.top-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.5rem;
}

.top-option:not(:last-child) {
  margin-right: 1rem;
}

.top-option-search {
  flex-grow: 1;
}

.graph-legend {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  z-index: 1;
  padding: 1rem;
  bottom: 2rem;
  left: 2rem;
  z-index: 1;
}

@media (min-width: 600px) {
  .graph-legend {
    flex-direction: row;
    align-items: center;
  }
}

.api-loader {
  position: absolute;
}

.business-graph .graph-legend {
  bottom: 2rem;
  padding: 1rem 0 1rem 1rem;
}

.business-graph .legend {
  display: block;
  position: relative;
  margin: 0;
  padding: 0 1.5rem;
  font-size: 0.75rem;
}

.business-graph .legend::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  background-color: silver;
  box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.15);
}

.business-graph .legend.company::after {
  background-color: var(--graph-company);
}

.business-graph .legend.person::after {
  background-color: var(--graph-person);
}

.business-graph .legend.address::after {
  background-color: var(--graph-address);
}

.business-graph .legend.external::after {
  color: var(--graph-inactive);
}

.graph-intro {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  color: var(--grey);
}

.isTeasing {
  filter: blur(3px);
}

.feature-teasing {
  position: absolute;
}
</style>
