import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    label: i18n.global.t('listName'),
    field: 'name',
    align: 'left',
    name: 'name'
  },
  {
    label: i18n.global.t('type'),
    field: 'type',
    align: 'center',
    name: 'type'
  },
  {
    label: i18n.global.t('frequency'),
    field: 'frequency',
    align: 'center'
  },
  {
    label: i18n.global.t('companiesInListing'),
    field: 'companies',
    align: 'center',
    format: (count) => i18n.global.t('companiesPlural', { count })
  },
  {
    label: i18n.global.t('subscribers'),
    field: 'subscribers',
    align: 'center',
    format: (count) => i18n.global.t('subscribersPlural', { count })
  },
  {
    label: i18n.global.t('tags'),
    field: 'tags',
    align: 'left',
    name: 'tags'
  },
  {
    label: i18n.global.t('latestAlert'),
    field: 'lastestAlert',
    align: 'right'
  },
  {
    label: i18n.global.t('actions'),
    align: 'right',
    field: true,
    name: 'actions'
  }
];
