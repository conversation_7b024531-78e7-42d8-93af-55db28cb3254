import i18n from '@/plugins/i18n-dynamic';

export default [
  // {
  //   name: 'type',
  //   label: i18n.global.t('notificationType'),
  //   field: 'notificationType',
  //   align: 'left'
  // },
  {
    name: 'contact',
    label: i18n.global.t('notificationContact'),
    field: 'notificationContact',
    align: 'left'
  },
  {
    name: 'frequency',
    label: i18n.global.t('notificationInterval'),
    field: 'notificationInterval',
    align: 'left'
  },
  {
    name: 'actions',
    label: i18n.global.t('actions'),
    field: true,
    align: 'right'
  }
];
