import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    label: 'Initials',
    name: 'avatar',
    field: true
  },
  {
    label: i18n.global.t('companyVat'),
    field: 'vatFormatted',
    align: 'left'
  },
  {
    label: i18n.global.t('companyName'),
    field: 'name',
    align: 'left',
    style: {
      maxWidth: '150px',
      wordBreak: 'normal',
      whiteSpace: 'normal'
    }
  },
  {
    label: i18n.global.t('companyType'),
    field: 'legalPersonType',
    align: 'left'
  },
  {
    label: i18n.global.t('juridicalSituation'),
    field: 'juridicalForm',
    align: 'left',
    style: {
      maxWidth: '150px',
      wordBreak: 'normal',
      whiteSpace: 'normal'
    }
  },
  {
    label: i18n.global.t('updatedStatusAt'),
    field: 'updatedAt',
    align: 'left'
  },
  {
    label: i18n.global.t('actions'),
    field: true,
    align: 'right',
    name: 'action'
  }
];
