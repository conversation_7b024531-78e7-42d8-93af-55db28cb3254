import i18n from '@/plugins/i18n-dynamic';
import { formatDateWithTime } from '@/utils/date';

export default [
  {
    label: i18n.global.t('listName'),
    field: 'name',
    name: 'name',
    align: 'left',
    sortable: true
  },
  {
    label: i18n.global.t('companiesInListing'),
    field: 'watchedCompanies',
    name: 'watchedCompanies',
    align: 'left',
    rawSort: (a, b) => parseInt(a.length, 10) - parseInt(b.length, 10),
    sortable: true
  },
  {
    label: i18n.global.t('subscribers'),
    field: 'subscribersCount',
    name: 'subscribersCount',
    align: 'left',
    rawSort: (a, b) => parseInt(a, 10) - parseInt(b, 10),
    sortable: true
  },
  {
    label: i18n.global.t('alerts'),
    field: 'alertLevels',
    name: 'severity',
    align: 'left',
    format: (value) => value.join(', ')
  },
  {
    label: i18n.global.t('tags'),
    field: 'tags',
    name: 'tags',
    align: 'left',
    sortable: true
  },
  {
    label: i18n.global.t('latestAlert'),
    field: 'lastestAlert',
    name: 'lastestAlert',
    align: 'right',
    sortable: true,
    style: 'white-space: nowrap',
    format: (value) => (value ? formatDateWithTime(value) : '–')
  },
  {
    label: i18n.global.t('actions'),
    field: true,
    name: 'actions',
    align: 'right',
    style: 'white-space: nowrap'
  }
];
