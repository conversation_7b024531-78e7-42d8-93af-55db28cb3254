import i18n from '@/plugins/i18n';
import { formatDateWithTime } from '@/utils/date';

export default [
  {
    label: i18n.global.t('severity'),
    field: 'severity',
    name: 'severity',
    align: 'center',
    style: 'width: 0'
  },
  {
    label: i18n.global.t('companyIdentifier'),
    field: 'companyIdentifier',
    name: 'company',
    align: 'left'
  },
  {
    label: i18n.global.t('eventName'),
    field: 'eventType',
    align: 'left',
    format: (value) => i18n.global.t(value)
  },
  {
    label: i18n.global.t('assignations'),
    field: 'assignations',
    align: 'left',
    format: (value) => i18n.global.t('assignationsCount', value && value.length)
  },
  {
    label: i18n.global.t('status'),
    field: 'alertLastStatus',
    align: 'center',
    format: (value) => i18n.global.t(value)
  },
  {
    label: i18n.global.t('comments'),
    field: 'comments',
    name: 'comments',
    align: 'center',
    style: 'width: 0'
  },
  {
    label: i18n.global.t('createdAt'),
    field: 'createdAt',
    name: 'createdAt',
    align: 'right',
    sortable: true,
    format: (value) => formatDateWithTime(value)
  },
  {
    label: i18n.global.t('updatedAt'),
    field: 'updatedAt',
    name: 'updatedAt',
    align: 'right',
    sortable: true,
    format: (value) => formatDateWithTime(value)
  },
  {
    label: i18n.global.t('actions'),
    field: true,
    name: 'action',
    align: 'right'
  }
];
