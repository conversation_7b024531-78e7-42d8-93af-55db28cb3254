<script setup>
import AlertSidebar from '@/pages/monitoring/components/alert-sidebar.vue';

defineProps({
  active: Boolean,
  loading: Boolean,
  alertId: String,
  isReviewed: Boolean
});
</script>

<template>
  <AlertSidebar :active="active" @close="$emit('close')">
    <h2 class="text-h6 q-pb-md">{{ $t('review') }}</h2>

    <!-- EVENT DIFF -->

    <!-- REVIEW CHECKBOX -->
    <q-card bordered>
      <q-item
        class="bordered"
        clickable
        @click="$emit(isReviewed ? 'unreview' : 'review')"
      >
        <q-item-section side top>
          <q-checkbox :model-value="isReviewed" name="review" />
        </q-item-section>
        <q-item-section>
          <q-item-label>
            {{ isReviewed ? $t('reviewed') : $t('review') }}
          </q-item-label>
          <q-item-label caption>
            {{ isReviewed ? $t('reviewMessage') : $t('unreviewMessage') }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-card>
  </AlertSidebar>
</template>
