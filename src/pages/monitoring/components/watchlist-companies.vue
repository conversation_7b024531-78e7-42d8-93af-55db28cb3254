<script setup>
import { onMounted, ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { SearchCompaniesStore } from '@/store/search-companies';
import { MonitoringStore } from '@/pages/monitoring/store';
import watchlistCompaniesColumns from '@/pages/monitoring/data/watchlist-companies-columns';
import CompaniesSearch from '@/components/commons/companies-search.vue';
import SearchCompaniesTable from '@/components/commons/search-companies-table.vue';
import { requiredField } from '@/utils/policies';

const props = defineProps({
  loading: Boolean,
  watchlist: {
    type: Object,
    default: null
  },
  companies: {
    type: Array,
    default: () => []
  },
  page: Number,
  total: Number,
  rowsPerPage: Number
});

const emit = defineEmits([
  'add-company',
  'remove-company',
  'load-companies',
  'set-pagination'
]);

const searchCompaniesStore = SearchCompaniesStore();
const { loading: searchLoading, autocompleteCompaniesResults } =
  storeToRefs(searchCompaniesStore);

searchCompaniesStore.prepareAutocomplete();

const selection = computed(() => props.watchlist.watchedCompanies);
const count = ref(0);
const displayNotification = ref(false);

const pagination = ref({
  rowsPerPage: 10,
  page: 1,
  rowsNumber: 0
});

function loadCompanies(page, isReady) {
  if (!isReady) return;
  emit('load-companies', page);
}

onMounted(() => loadCompanies(1, props.watchlist.isReady));

watch(
  () => props.watchlist.isReady,
  (isReady) => loadCompanies(1, isReady)
);

watch(
  () => props.companies,
  () => {
    pagination.value = {
      rowsPerPage: props.rowsPerPage,
      page: props.page,
      rowsNumber: props.total
    };
  }
);

function paginate({ pagination }) {
  emit('set-pagination', pagination.rowsPerPage);
  loadCompanies(pagination.page, props.watchlist.isReady);
}

const monitoringStore = MonitoringStore();
const isUploadDialogVisible = ref(false);
const companiesToUpload = ref('');

function toggleUploadDialog() {
  isUploadDialogVisible.value = !isUploadDialogVisible.value;
}

async function uploadCompanies() {
  await monitoringStore.uploadCompanies({
    watchlistId: props.watchlist.id,
    companies: companiesToUpload.value
  });
  toggleUploadDialog();
  companiesToUpload.value = '';
}
</script>

<template>
  <div class="q-pt-md row items-center">
    <CompaniesSearch
      :results="autocompleteCompaniesResults"
      :selection="selection"
      :loading="loading || searchLoading"
      class="q-mr-md"
      @add="
        (companyIdentifier) =>
          $emit('add-company', {
            watchlistShortId: watchlist.id,
            companyIdentifier
          })
      "
      @remove="
        (companyIdentifier) =>
          $emit('remove-company', {
            watchlistShortId: watchlist.id,
            companyIdentifier
          })
      "
      @search="searchCompaniesStore.autocompleteCompanies"
      @clear="searchCompaniesStore.clearSearch"
    />

    <q-btn
      :loading="loading"
      :label="$t('batchUpload')"
      icon="fa-regular fa-file-plus"
      outline
      color="primary"
      @click="toggleUploadDialog"
    />
  </div>

  <!-- NOTIFICATIONS -->
  <q-banner
    v-if="displayNotification"
    inline-actions
    class="q-mt-md bg-blue-1 text-primary static-notification"
  >
    <template #avatar>
      <q-icon name="fa-regular fa-circle-info" size="xs" class="q-my-md" />
    </template>

<style>
.static-notification {
  border: 1px solid var(--primary);
}

.search-result-container {
  position: relative;
}

.search-results {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--white);
  z-index: 1;
  padding: 1rem;
  border-radius: 0.25rem;
  max-height: 400px;
  overflow-y: auto;
}
</style>
