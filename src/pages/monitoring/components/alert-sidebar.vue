<script setup>
defineProps({ active: <PERSON>olean });
defineEmits(['add-comment', 'close', 'uncomment']);
</script>

<template>
  <q-drawer
    :model-value="active"
    side="right"
    ref="alertSidebar"
    :width="420"
    behavior="mobile"
    @update:model-value="$emit('close')"
  >
    <q-scroll-area class="fit">
      <div class="sticky-header">
        <div class="q-py-md q-px-lg">
          <q-btn outline @click="$emit('close')">
            <q-icon name="fa-regular fa-arrow-right-to-line" class="q-mr-md" />
            <span>{{ $t('closeTab') }}</span>
          </q-btn>
        </div>
        <q-separator />
      </div>

      <div class="q-py-md q-px-lg">
        <slot />
      </div>
    </q-scroll-area>
  </q-drawer>
</template>

<style scoped>
.sticky-header {
  position: sticky;
  top: 0;
  background-color: rgba(var(--whiteRGB), 0.5);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.v-theme--dark .sticky-header {
  background-color: rgba(21, 21, 21, 0.5);
}
</style>
