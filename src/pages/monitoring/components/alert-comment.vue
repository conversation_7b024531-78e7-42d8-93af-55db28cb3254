<script setup>
import { onMounted } from 'vue';

const props = defineProps({
  id: String,
  text: String,
  date: String,
  customerId: String,
  customerName: String,
  customerInitials: String,
  customerColor: String,
  allowActions: Boolean
});

const emit = defineEmits(['uncomment', 'load-customer']);

onMounted(() => {
  emit('load-customer', props.customerId);
});
</script>

<template>
  <q-chat-message
    :text="[text]"
    :name="customerName"
    class="comment"
    bg-color="blue-1"
    text-color="black"
    text-html
  >
    <template v-slot:avatar>
      <q-avatar
        class="text-white q-mr-sm"
        :style="{ backgroundColor: customerColor }"
        size="md"
      >
        {{ customerInitials }}
      </q-avatar>
    </template>

<style scoped>
.comment :deep(div) {
  white-space: normal !important;
}
</style>
