<script setup>
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import { MonitoringStore } from '@/pages/monitoring/store';
import { useI18n } from 'vue-i18n';
import eventsTypes from '@/pages/monitoring/data/event-types';

defineProps({ loading: Boolean });

const { t } = useI18n();
const monitoringStore = MonitoringStore();

const { alertsFilter } = storeToRefs(monitoringStore);

const toggleOptions = [
  {
    label: t('lastWeek'),
    value: 'LASTWEEK'
  },
  {
    label: t('yesterday'),
    value: 'YESTERDAY'
  },
  {
    label: t('today'),
    value: 'TODAY'
  }
];

const events = eventsTypes.map((event) => ({
  label: t(event),
  value: event
}));
const eventOptions = ref(events);

const statuses = ref([
  { value: 'CREATED', label: t('CREATED') },
  { value: 'ASSIGNED', label: t('ASSIGNED') },
  { value: 'REVIEWED', label: t('REVIEWED') }
]);

function clearFilters() {
  alertsFilter.value.dates = null;
  alertsFilter.value.period = null;
  alertsFilter.value.event = null;
  alertsFilter.value.status = null;
}

function filterEvents(val, update) {
  update(() => {
    const needle = val.toLocaleLowerCase();
    eventOptions.value = events.filter(
      (event) => event.label.toLocaleLowerCase().indexOf(needle) > -1
    );
  });
}
</script>

<template>
  <div class="row justify-between align-center q-pb-sm q-gutter-sm">
    <!-- PERIOD -->
    <q-btn-toggle
      v-model="alertsFilter.period"
      :options="toggleOptions"
      :disable="loading"
      toggle-color="primary"
      class="q-mb-sm alert-filters-field"
      :color="$q.dark.isActive ? null : 'white'"
      :text-color="$q.dark.isActive ? null : 'blue-grey-10'"
      @update:model-value="alertsFilter.dates = null"
    />

    <!-- DATE -->
    <q-btn-dropdown
      :disable="loading"
      :color="$q.dark.isActive ? null : 'white'"
      :text-color="$q.dark.isActive ? null : 'blue-grey-10'"
      class="q-mb-sm alert-filters-field"
    >
      <template #label>
        <div class="row align-center justify-start">
          <q-icon name="fa-regular fa-calendar" class="self-center" />
          <div class="q-px-sm">{{ $t('date') }}</div>
        </div>

        <q-chip v-if="alertsFilter.dates" outline color="primary" size="sm">
          <span v-if="alertsFilter.dates">
            {{ alertsFilter.dates.from }}
            <q-icon
              name="fa-regular fa-chevron-right"
              class="q-mx-sm q-mb-xs"
            />
            {{ alertsFilter.dates.to }}
          </span>
          <span v-else>{{ alertsFilter.dates }}</span>
        </q-chip>
      </template>

<style scoped>
.alert-filters-field {
  max-height: 50px;
}

.alert-filters-select {
  min-width: 10rem;
  max-width: 15rem;
}
</style>
