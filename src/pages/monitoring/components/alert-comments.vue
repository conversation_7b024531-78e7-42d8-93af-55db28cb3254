<script setup>
import { ref, watch, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import AlertSidebar from '@/pages/monitoring/components/alert-sidebar.vue';
import AlertComment from '@/pages/monitoring/components/alert-comment.vue';

const props = defineProps({
  active: Boolean,
  loading: Boolean,
  alertId: String,
  activeUserId: String,
  comments: Array,
  customers: Array
});

const emit = defineEmits([
  'add-comment',
  'close',
  'uncomment',
  'load-customer'
]);

const { t } = useI18n();
const $q = useQuasar();

const newCommentText = ref('');

const preparedComments = computed(() => {
  return props.comments.map((comment) => {
    const customer = props.customers.find(
      (customer) => customer.customerShortId === comment.customerShortId
    );

    if (!customer) return comment;

    return {
      ...comment,
      customerInitials: customer.initials,
      customerColor: customer.color,
      customerName: customer.name || ''
    };
  });
});

watch(
  () => props.comments,
  () => {
    // Clear current editor content on comments update
    newCommentText.value = '';
  }
);

function uncomment(params) {
  $q.dialog({
    title: t('confirmUncomment'),
    message: t('alertuncommentConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    emit('uncomment', params);
  });
}
</script>

<template>
  <AlertSidebar :active="active" @close="$emit('close')">
    <q-card class="q-mb-lg">
      <q-card-section>
        {{ $t('newComment') }}
      </q-card-section>

      <q-editor
        v-model="newCommentText"
        :toolbar="[
          ['bold', 'italic', 'underline', 'strike'],
          ['undo', 'redo']
        ]"
        :disable="loading"
        min-height="8rem"
      />
      <q-card-section align="right">
        <q-btn
          :label="$t('save')"
          :disable="!newCommentText"
          :loading="loading"
          color="primary"
          flat
          @click="
            $emit('add-comment', {
              actingCustomerShortId: activeUserId,
              alertShortId: alertId,
              comment: newCommentText
            })
          "
        />
        <q-btn :label="$t('cancel')" flat @click="newCommentText = ''" />
      </q-card-section>
    </q-card>

    <h2 class="text-h6">{{ $t('comments') }}</h2>

    <div v-if="!comments">{{ $t('noComments') }}</div>

    <div v-else>
      <AlertComment
        v-for="comment in preparedComments"
        :key="comment.commentShortId"
        :id="comment.commentShortId"
        :text="comment.comment"
        :date="comment.commentDate"
        :customer-id="comment.customerShortId"
        :customer-name="comment.customerName"
        :customer-initials="comment.customerInitials"
        :customer-color="comment.customerColor"
        allow-actions
        @load-customer="(id) => $emit('load-customer', id)"
        @uncomment="
          (commentShortId) =>
            uncomment({
              commentShortId,
              alertShortId: alertId,
              actingCustomerShortId: activeUserId
            })
        "
      />
    </div>
  </AlertSidebar>
</template>

<style scoped>
.sticky-header {
  position: sticky;
  top: 0;
  background-color: rgba(var(--whiteRGB), 0.5);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.v-theme--dark .sticky-header {
  background-color: rgba(21, 21, 21, 0.5);
}
</style>
