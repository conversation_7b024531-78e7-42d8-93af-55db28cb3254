<script setup>
import AlertComment from '@/pages/monitoring/components/alert-comment.vue';
import AlertAssignation from '@/pages/monitoring/components/alert-assignation.vue';

defineProps({
  type: String,
  meta: Object,
  date: String,
  customer: Object
});
</script>

<template>
  <q-card>
    <q-card-section v-if="type === 'CREATED'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-sparkles" size="sm" color="yellow" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">
            {{ $t('CREATED') }}
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-section v-if="type === 'NOTIFIED'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-bell" size="sm" color="primary" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">
            {{ $t('NOTIFIED') }}
          </q-item-label>
          <q-item-label>
            <div class="row items-center">
              <q-icon name="fa-regular fa-clock" color="blue-grey" />
              <span class="q-pl-sm text-blue-grey">
                {{ $t(meta.interval.toUpperCase()) }}
              </span>
            </div>
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-section v-if="type === 'REVIEWED'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-check" size="sm" color="success" />
        </q-item-section>
        <q-item-section>
          <span class="text-bold">{{ $t('reviewedBy') }}:</span>
          <q-item-label>
            {{ customer?.name }}
          </q-item-label>
          <q-item-label caption>
            <span class="text-caption">{{ customer?.email }}</span>
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-section v-if="type === 'UNREVIEWED'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-xmark" size="sm" color="error" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">
            {{ $t('unreviewed') }}
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-section v-if="type === 'ADD_COMMENT'">
      <AlertComment
        :id="meta.commentShortId"
        :text="meta.comment"
        :date="date"
        :customerId="customer?.customerId"
        :customerName="customer?.name"
        :customerInitials="customer?.initials"
        :customerColor="customer?.color"
      />
    </q-card-section>

    <q-card-section v-if="type === 'REMOVE_COMMENT'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-comment-xmark" size="sm" color="error" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">
            {{ $t('uncommented') }}
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-section v-if="type === 'ADD_ASSIGN'">
      <AlertAssignation
        :id="meta.assignationShortId"
        :name="customer?.name"
        :email="customer?.email"
        :date="date"
      />
    </q-card-section>

    <q-card-section v-if="type === 'REMOVE_ASSIGN'">
      <q-item>
        <q-item-section avatar>
          <q-icon name="fa-regular fa-user-xmark" size="sm" color="error" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">
            {{ $t('unassigned') }}
          </q-item-label>
          <q-item-label caption>{{ date }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>
  </q-card>
</template>
