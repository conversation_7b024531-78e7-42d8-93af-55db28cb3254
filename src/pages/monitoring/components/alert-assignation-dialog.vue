<script setup>
import { ref, computed } from 'vue';
import AlertAssignation from '@/pages/monitoring/components/alert-assignation.vue';

const props = defineProps({
  loading: <PERSON><PERSON><PERSON>,
  active: <PERSON>olean,
  assignations: Array,
  customers: Array
});

const emit = defineEmits(['assign', 'unassign']);

const customerToAssign = ref(null);

const filteredCustomers = computed(() => {
  if (!props.customers) return [];

  if (!props.assignations) return props.customers;

  const assignedIds = props.assignations.map(
    (assignation) => assignation.customerShortId
  );

  return props.customers.filter(
    (customer) => !assignedIds.includes(customer.customerShortId)
  );
});

function assign() {
  emit('assign', customerToAssign.value.customerShortId);
  emit('close');
}
</script>

<template>
  <q-dialog :model-value="active" @update:model-value="$emit('close')">
    <q-card style="min-width: 33rem">
      <q-card-section>
        <div class="text-h6">{{ $t('assign') }}</div>
      </q-card-section>

      <q-card-section>
        <q-list bordered separator>
          <AlertAssignation
            v-for="assignation in assignations"
            :key="assignation.assignationShortId"
            :id="assignation.assignationShortId"
            :name="assignation.name"
            :email="assignation.email"
            :date="assignation.date"
            allow-actions
            @unassign="() => $emit('unassign', assignation.customerShortId)"
          />
        </q-list>
      </q-card-section>

      <q-card-section>
        <q-select
          v-model="customerToAssign"
          :options="filteredCustomers"
          :label="$t('selectToAssign')"
          :loading="loading"
          option-value="customerShortId"
          option-label="details"
          class="col-grow q-mr-sm"
          outlined
          flat
        />
      </q-card-section>
      <q-separator />
      <q-card-actions align="right">
        <q-btn flat :label="$t('cancel')" v-close-popup />
        <q-btn
          :label="$t('assign')"
          :loading="loading"
          :disable="!customerToAssign"
          color="primary"
          outline
          flat
          @click="assign"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
