<script setup>
defineProps({
  id: String,
  name: String,
  email: String,
  date: String,
  allowActions: Boolean
});

defineEmits(['unassign']);
</script>

<template>
  <q-item>
    <q-item-section avatar>
      <q-icon name="fa-regular fa-user-check" size="sm" color="success" />
    </q-item-section>
    <q-item-section>
      <span class="text-bold">{{ $t('assignedTo') }}:</span>
      <q-item-label>
        {{ name }}
      </q-item-label>
      <q-item-label caption>
        <span class="text-caption">{{ email }}</span>
      </q-item-label>
      <q-item-label caption>{{ date }}</q-item-label>
    </q-item-section>
    <q-item-section v-if="allowActions" side>
      <q-btn
        icon="fa-regular fa-circle-xmark"
        round
        flat
        @click="$emit('unassign')"
      />
    </q-item-section>
  </q-item>
</template>
