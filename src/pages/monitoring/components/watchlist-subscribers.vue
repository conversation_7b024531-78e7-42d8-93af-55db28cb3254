<script setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import watchlistSubscribersColumns from '@/pages/monitoring/data/watchlist-subscribers-columns';

const props = defineProps({
  loading: Boolean,
  watchlist: {
    type: Object,
    default: null
  },
  companyId: String,
  userEmail: String,
  customers: Array
});

const emit = defineEmits([
  'add-subscriber',
  'update-subscriber',
  'remove-subscriber',
  'load-company-users'
]);

const { t } = useI18n();
const $q = useQuasar();
const filteredUsers = ref([]);
const selectedUser = ref(null);
const pagination = ref({ rowsPerPage: 0 });

function confirmDeletion(id) {
  $q.dialog({
    title: t('confirmDelete'),
    message: t('confirmDeleteMessage'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    emit('remove-subscriber', id);
  });
}

function filterUsers(value, update) {
  // apply disabled status
  const watchlistAddedCustomers = props.watchlist.subscribers.map(
    (subscriber) => subscriber.notificationContact
  );
  const customers = props.customers.map((customer) => {
    return {
      ...customer,
      disable: watchlistAddedCustomers.includes(customer.email)
    };
  });

  // filter
  if (value === '') {
    update(() => {
      filteredUsers.value = customers;
    });
  }

  update(() => {
    const val = value.toLowerCase();
    filteredUsers.value = customers.filter(
      (user) => user.name.toLowerCase().indexOf(val) !== -1
    );
  });
}

onMounted(() => {
  emit('load-company-users', props.companyId);
});
</script>

<template>
  <div class="flex justify-end">
    <q-select
      v-model="selectedUser"
      :label="$t('searchUser')"
      :options="filteredUsers"
      option-label="name"
      option-value="email"
      :bg-color="$q.dark.isActive ? 'transparent' : 'white'"
      :label-color="$q.dark.isActive ? null : 'blue-grey-10'"
      hide-selected
      emit-value
      use-input
      outlined
      dense
      :style="{ minWidth: '220px' }"
      @filter="filterUsers"
      @update:model-value="(email) => $emit('add-subscriber', email)"
    />
  </div>

  <!-- TABLE -->
  <q-card bordered flat class="q-mt-md">
    <q-table
      :loading="loading"
      :columns="watchlistSubscribersColumns"
      :rows="watchlist.subscribers"
      row-key="id"
      flat
      v-model:pagination="pagination"
      :rows-per-page-options="[0]"
      hide-bottom
      virtual-scroll
    >
      <template #body-cell-contact="props">
        <q-td key="notificationContact" :props="props">
          {{ props.row.notificationContact }}
          <q-chip v-if="props.row.notificationContact === userEmail" size="sm">
            {{ $t('you') }}
          </q-chip>
        </q-td>
      </template>
