<script setup>
import { ref, toRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { useHeadSafe } from '@vueuse/head';
import { types } from '@/pages/monitoring/utils/alert-levels';
import { noEmptyRules } from '@/utils/policies';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  watchlist: {
    type: Object,
    default: () => ({
      name: '',
      tags: [],
      alertLevels: []
    })
  }
});

const emit = defineEmits(['changetab', 'create', 'update']);

const { t } = useI18n();

useHeadSafe({
  title: [t('watchlist'), t('monitoring')].join(' - ')
});

const clone = toRef(props, 'watchlist');
const tag = ref('');

function addTag() {
  const cleanTag = String(tag.value).trim();

  if (!cleanTag.length || clone.value.tags.includes(cleanTag)) return;

  clone.value.tags.push(cleanTag);
  tag.value = '';
}

function removeTag(tagEntry) {
  clone.value.tags = clone.value.tags.filter((tag) => tag !== tagEntry);
}

const alertLevels = types.map((alertLevel) => ({
  label: t(alertLevel),
  name: alertLevel,
  value: alertLevel,
  caption: t(alertLevel)
}));

async function save() {
  emit(clone.value.id ? 'update' : 'create', clone.value);
}
</script>

<template>
  <div class="q-py-xl q-px-md">
    <q-form class="list-overview-form" @submit="save">
      <div class="row items-center">
        <!-- LIST NAME -->
        <div class="col-4 q-pb-md">
          <span class="text-weight-medium"> {{ $t('listName') }}* </span>
        </div>
        <div class="col-8 q-pb-md">
          <q-input
            v-model="clone.name"
            :loading="loading"
            :rules="[noEmptyRules]"
            id="name"
            autocomplete="off"
            lazy-rules
            outlined
          />
        </div>

        <!-- TAGS -->
        <div class="col-4 q-pb-md">
          <span class="text-weight-medium">
            {{ $t('tags') }}
          </span>
        </div>
        <div class="col-8 q-pb-md">
          <q-form @submit.prevent.stop="addTag">
            <q-input
              v-model="tag"
              id="tags"
              outlined
              @keydown.enter.prevent="addTag"
            >
              <template v-slot:append>
                <q-btn
                  :label="$t('add')"
                  :disabled="!tag"
                  color="primary"
                  type="submit"
                  size="sm"
                  push
                  @click="addTag"
                />
              </template>

<style>
.list-overview-form {
  max-width: 33rem;
}
</style>
