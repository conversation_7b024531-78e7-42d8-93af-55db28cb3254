<script setup>
defineProps({
  from: String,
  to: String
});
</script>

<template>
  <div class="diffs flex items-center justify-between">
    <q-card class="diff-card">
      <div class="text-caption q-pa-sm">{{ $t('before') }}</div>
      <q-separator />
      <div class="q-pa-md">{{ from || 'N/A' }}</div>
    </q-card>
    <q-icon name="fa-regular fa-chevron-right" class="q-mx-md" />
    <q-card
      :class="[
        'diff-card',
        {
          'text-white': $q.dark.isActive,
          'bg-blue-10': $q.dark.isActive,
          'text-primary': !$q.dark.isActive,
          'bg-blue-1': !$q.dark.isActive
        }
      ]"
    >
      <div class="text-caption q-pa-sm">{{ $t('after') }}</div>
      <q-separator />
      <div class="q-pa-md">
        {{ to || $t('empty') }}
      </div>
    </q-card>
  </div>
</template>

<style scoped>
.diffs {
  width: 100%;
}

.diff-card {
  flex-grow: 1;
}

.event-base {
  display: flex;
}

.event-base-icon {
  padding-right: 0.5rem;
}
</style>
