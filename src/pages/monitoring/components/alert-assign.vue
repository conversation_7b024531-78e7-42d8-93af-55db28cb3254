<script setup>
import { ref, watch, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import AlertSidebar from '@/pages/monitoring/components/alert-sidebar.vue';
import AlertAssignation from '@/pages/monitoring/components/alert-assignation.vue';
import { formatDateWithTime } from '@/utils/date';

const props = defineProps({
  active: Boolean,
  loading: Boolean,
  activeUserId: String,
  alertId: String,
  assignations: Array,
  customers: Array
});

const emit = defineEmits(['load-customers', 'unassign']);

const { t } = useI18n();
const $q = useQuasar();

const customerToAssign = ref(null);

watch(
  () => props.active,
  (isActive) => {
    if (!isActive) return;

    const ids = props?.assignations.map(
      (assignation) => assignation.customerShortId
    );

    if (ids.length === 0) return;

    emit('load-customers', ids);
  }
);

function getCustomer(customerShortId) {
  const customer = props?.customers.find(
    (customer) => customer.customerShortId === customerShortId
  );

  if (customer) return customer;

  return { name: '', email: '' };
}

const preparedAssignations = computed(() => {
  if (!Array.isArray(props.assignations) || props.assignations.length < 1) {
    return [];
  }

  return props.assignations.map((assignation) => {
    const customer = getCustomer(assignation.customerShortId);
    return {
      ...assignation,
      name: customer.name,
      email: customer.email,
      date: formatDateWithTime(assignation.assignationDate)
    };
  });
});

const filteredCustomers = computed(() => {
  if (!Array.isArray(props.assignations) || props.assignations.length < 1) {
    return props.customers;
  }

  const assignationsCustomers = props.assignations.map(
    (assignation) => assignation.customerShortId
  );

  return props.customers.filter(
    (customer) => !assignationsCustomers.includes(customer.customerShortId)
  );
});

function onUnassign(params) {
  $q.dialog({
    title: t('confirmUnassign'),
    message: t('alertUnassignConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    emit('unassign', params);
  });
}
</script>

<template>
  <AlertSidebar :active="active" @close="$emit('close')">
    <h2 class="text-h5 q-pb-md">{{ $t('assign') }}</h2>

    <!-- ADD ASSIGNATION -->
    <div class="flex justify-between">
      <q-select
        v-model="customerToAssign"
        :options="filteredCustomers"
        :label="$t('selectToAssign')"
        :loading="loading"
        option-value="customerShortId"
        option-label="name"
        class="q-mr-md col-grow"
        outlined
        dense
      />

      <q-btn
        color="primary"
        :loading="loading"
        :disabled="!customerToAssign"
        outline
        @click="
          $emit('assign', {
            alertShortId: alertId,
            actingCustomerShortId: activeUserId,
            customerShortId: customerToAssign.customerShortId
          })
        "
      >
        {{ $t('assign') }}
      </q-btn>
    </div>

    <!-- ASSIGNATIONS -->
    <h3 class="text-h6">
      {{ $t('assignationsCount', assignations?.length) }}
    </h3>

    <q-list bordered v-if="preparedAssignations.length" separator>
      <AlertAssignation
        v-for="assignation in preparedAssignations"
        :key="assignation.assignationShortId"
        :id="assignation.assignationShortId"
        :name="assignation.name"
        :email="assignation.email"
        :date="assignation.date"
        allow-actions
        @unassign="
          onUnassign({
            alertShortId: alertId,
            actingCustomerShortId: activeUserId,
            customerShortId: assignation.customerShortId
          })
        "
      />
    </q-list>
  </AlertSidebar>
</template>
