<template>
  <Breadcrumb :links="breadcrumbLinks" />

  <p class="text-subtitle2 q-pt-md">{{ $t('watchlistLead') }}</p>

  <q-tabs v-model="activeTab" align="left" active-color="primary">
    <q-route-tab name="overview" :label="$t('overview')" :to="{ query: {} }">
      <span class="q-py-sm" />
    </q-route-tab>

    <q-route-tab
      name="companies"
      :label="$t('companies')"
      :disable="!watchlist.id"
      :style="{ opacity: !watchlist.id ? 0.5 : 1 }"
      :to="{ query: { tab: 'companies' } }"
    >
      <q-badge rounded>
        {{ watchlist?.watchedCompanies.length || 0 }}
      </q-badge>
    </q-route-tab>

    <q-route-tab
      name="subscribers"
      :disable="!watchlist.id"
      :style="{ opacity: !watchlist.id ? 0.5 : 1 }"
      :label="$t('subscribers')"
      :to="{ query: { tab: 'subscribers' } }"
    >
      <q-badge rounded>
        {{ watchlist?.subscribers.length || 0 }}
      </q-badge>
    </q-route-tab>
  </q-tabs>
  <q-separator />

  <q-tab-panels v-model="activeTab" animated class="transparent">
    <q-tab-panel name="overview" class="q-px-none">
      <WatchlistOverview
        :loading="loading"
        :watchlist="watchlist"
        @create="createWatchlist"
        @update="monitoringStore.updateWatchlist"
      />
    </q-tab-panel>

    <q-tab-panel name="companies" class="q-px-none watchlist-tab">
      <WatchlistCompanies
        :loading="loading"
        :watchlist="watchlist"
        :companies="companies"
        :rows-per-page="companiesPerPage"
        :page="companiesCurrentPage"
        :total="companiesTotal"
        @load-companies="monitoringStore.loadWatchlistCompanies"
        @add-company="addCompany"
        @remove-company="removeCompany"
        @set-pagination="monitoringStore.setCompaniesPerPage"
      />
    </q-tab-panel>

    <q-tab-panel name="subscribers" class="q-px-none">
      <WatchlistSubscribers
        :loading="loading"
        :watchlist="watchlist"
        :customers="customers"
        :user-email="getUserEmail"
        :company-id="getUserOrganisation"
        @add-subscriber="monitoringStore.addSubscriber"
        @update-subscriber="monitoringStore.updateSubscriber"
        @remove-subscriber="monitoringStore.removeSubscriber"
        @load-company-users="monitoringStore.loadOrganisationUsers"
      />
    </q-tab-panel>
  </q-tab-panels>
</template>

<script setup>
import { ref, onUnmounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import Breadcrumb from '@/components/commons/breadcrumb.vue';
import WatchlistOverview from '@/pages/monitoring/components/watchlist-overview.vue';
import WatchlistCompanies from '@/pages/monitoring/components/watchlist-companies.vue';
import WatchlistSubscribers from '@/pages/monitoring/components/watchlist-subscribers.vue';

const { t } = useI18n();

const activeTab = ref('overview');
const breadcrumbLinks = computed(() => [
  { label: t('monitoring'), path: '/monitoring' },
  {
    label: watchlist.value.name || t('newWatchlist'),
    path: watchlist.value.name ? `/monitoring/${watchlist.value.id}` : null
  },
  {
    label: t('edit')
  }
]);

const route = useRoute();
const authStore = AuthStore();
const monitoringStore = MonitoringStore();
const {
  loading,
  watchlist,
  companies,
  customers,
  companiesCurrentPage,
  companiesTotal,
  companiesPerPage
} = storeToRefs(monitoringStore);
const { getUserOrganisation, getUserEmail } = storeToRefs(authStore);

const savedRowPerPage = localStorage.getItem('preference-pagination');

if (savedRowPerPage !== null) {
  monitoringStore.setCompaniesPerPage(parseInt(savedRowPerPage, 10));
}

monitoringStore.loadWatchlist({ watchlistId: route.params.id });

async function createWatchlist(watchlist) {
  const { success } = await monitoringStore.createWatchlist({
    watchlist,
    organisationShortId: getUserOrganisation.value
  });

  if (!success) return;

  activeTab.value = 'companies';
}

onUnmounted(() => {
  // return to the first tab when leaving
  activeTab.value = 'overview';
});

async function addCompany({ watchlistShortId, companyIdentifier }) {
  await monitoringStore.addCompanyToWatchlist({
    organisationShortId: getUserOrganisation.value,
    watchlistShortId,
    companyIdentifier
  });
  // reload the list after update
  monitoringStore.loadWatchlistCompanies(companiesCurrentPage.value);
}

async function removeCompany({ watchlistShortId, companyIdentifier }) {
  await monitoringStore.removeCompanyFromWatchlist({
    organisationShortId: getUserOrganisation.value,
    watchlistShortId,
    companyIdentifier
  });
  // reload the list after update
  monitoringStore.loadWatchlistCompanies(companiesCurrentPage.value);
}
</script>

<style>
.watchlist-tab {
  min-height: 600px;
}
</style>
