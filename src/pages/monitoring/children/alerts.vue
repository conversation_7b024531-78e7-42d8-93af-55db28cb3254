<script setup>
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useHeadSafe } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';
import Breadcrumb from '@/components/commons/breadcrumb.vue';
import AlertsFilters from '@/pages/monitoring/components/alerts-filters.vue';
import AlertAssign from '@/pages/monitoring/components/alert-assign.vue';
import AlertComments from '@/pages/monitoring/components/alert-comments.vue';

const { t } = useI18n();
const route = useRoute();
const { id } = route.params;

useHeadSafe({
  title: [t('alerts'), t('watchlist'), t('monitoring')].join(' - ')
});

const breadcrumbLinks = computed(() => [
  { label: t('monitoring'), path: '/monitoring' },
  { label: watchlist.value.isReady ? watchlist.value.name : t('alerts') }
]);

// Both sidebar
const sidebarAlertId = ref(null);
const sidebarToDisplay = ref('');

function toggleSidebar(context = null, alertId = null) {
  sidebarAlertId.value = alertId;
  sidebarToDisplay.value = context;
}

const sidebarValues = computed(() => {
  const defaultObject = {
    assignations: [],
    comments: []
  };

  if (!sidebarAlertId.value) return defaultObject;

  const alert = alerts.value.find(
    (alert) => alert.alertShortId === sidebarAlertId.value
  );

  if (!alert) return defaultObject;

  return {
    assignations: alert.assignations,
    comments: alert.comments.slice().reverse()
  };
});

// AUTH
const authStore = AuthStore();
const { getUserOrganisation, getUserShortId } = storeToRefs(authStore);

const monitoringStore = MonitoringStore();
const {
  watchlist,
  loading,
  alerts,
  alertsColumns,
  alertsParams,
  alertsFilter,
  customers
} = storeToRefs(monitoringStore);

if (!watchlist?.isReady) {
  monitoringStore.loadWatchlist({ watchlistId: id, withAlerts: false });
}

// SEARCH
function searchAlerts() {
  monitoringStore.searchAlerts({ watchlistShortId: id });
}

function paginateAlerts({ pagination }) {
  monitoringStore.setAlertsParams(pagination);
  searchAlerts();
}

// filters have changed
watch(alertsFilter.value, () => searchAlerts());

// USERS
function loadCustomers() {
  monitoringStore.loadOrganisationUsers(getUserOrganisation.value);
}

loadCustomers();
searchAlerts();
</script>

<template>
  <div class="flex justify-between items-center">
    <Breadcrumb :links="breadcrumbLinks" />
    <q-space></q-space>
    <q-btn
      :label="$t('edit')"
      :to="`/monitoring/${watchlist.id}`"
      icon="fa-regular fa-pen"
    />
  </div>

  <!-- FILTERS -->
  <AlertsFilters class="q-pt-md" :loading="loading" />

  <!-- NO ALERTS -->
  <q-banner
    v-if="!alerts || alerts?.length === 0"
    :class="$q.dark.isActive ? 'bg-blue-10' : 'bg-blue-1'"
    rounded
  >
    <template #avatar>
      <q-icon
        :color="$q.dark.isActive ? 'white' : 'primary'"
        name="fa-regular fa-circle-info"
        size="sm"
        class="q-my-sm"
      />
    </template>

<style scoped>
.alerts-notification {
  background-color: var(--primaryLight);
}

.alerts-table :deep(thead) {
  position: sticky;
  top: 0;
  z-index: 1;
  backdrop-filter: blur(10px);
  background-color: var(--primarySmokeTransparent);
}

.company-name {
  display: block;
  white-space: normal;
  max-width: 10rem;
}
</style>
