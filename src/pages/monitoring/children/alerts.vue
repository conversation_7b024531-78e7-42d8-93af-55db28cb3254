<template>
  <div class="flex justify-between items-center">
    <Breadcrumb :links="breadcrumbLinks" />
    <q-space></q-space>
    <q-btn
      :label="$t('edit')"
      :to="`/monitoring/${watchlist.id}`"
      icon="fa-regular fa-pen"
    />
  </div>

  <!-- FILTERS -->
  <AlertsFilters class="q-pt-md" :loading="loading" />

  <!-- NO ALERTS -->
  <q-banner
    v-if="!alerts || alerts?.length === 0"
    :class="$q.dark.isActive ? 'bg-blue-10' : 'bg-blue-1'"
    rounded
  >
    <template #avatar>
      <q-icon
        :color="$q.dark.isActive ? 'white' : 'primary'"
        name="fa-regular fa-circle-info"
        size="sm"
        class="q-my-sm"
      />
    </template>
    {{ $t('noAlerts') }}
  </q-banner>

  <!-- DATA TABLE -->
  <q-table
    v-if="alerts?.length"
    class="alerts-table"
    :columns="alertsColumns"
    :rows="alerts"
    :loading="loading"
    row-key="field"
    :rows-per-page-options="[5, 10, 50]"
    binary-state-sort
    bordered
    flat
    v-model:pagination="alertsParams"
    @request="paginateAlerts"
  >
    <template #body-cell-severity="props">
      <q-td :props="props">
        <q-badge rounded :color="severityColors(props.row.severity)">
          <q-tooltip anchor="top middle" :offset="[0, 30]">
            {{ $t(props.row.severity) }}
          </q-tooltip>
        </q-badge>
      </q-td>
    </template>

    <template #body-cell-company="props">
      <q-td :props="props">
        <router-link
          :to="`/companies/${props.row.companyIdentifier}`"
          class="company-name"
        >
          {{ props.row.companyName }}
        </router-link>
      </q-td>
    </template>

    <template #body-cell-comments="props">
      <q-td :props="props">
        <q-btn
          flat
          round
          icon="fa-regular fa-comment"
          color="grey"
          @click="toggleSidebar('comments', props.row.alertShortId)"
        >
          <q-badge v-if="props.row.comments.length" rounded floating>
            {{ props.row.comments.length }}
          </q-badge>
        </q-btn>
      </q-td>
    </template>

    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn
          :label="$t('assign')"
          :color="props.row.alertLastStatus === 'CREATED' ? 'primary' : 'green'"
          :icon="`fa-regular fa-${
            props.row.alertLastStatus === 'CREATED' ? 'user' : 'check'
          }`"
          size="sm"
          class="q-mr-sm"
          @click="toggleSidebar('assign', props.row.alertShortId)"
        />
        <q-btn
          :label="$t('history')"
          size="sm"
          color="primary"
          icon="fa-regular fa-list-timeline"
          :to="`/monitoring/${watchlist.id}/alerts/${props.row.alertShortId}`"
        />
      </q-td>
    </template>
  </q-table>

  <AlertAssign
    :active="sidebarToDisplay === 'assign'"
    :loading="loading"
    :active-user-id="getUserShortId"
    :alert-id="sidebarAlertId"
    :assignations="sidebarValues.assignations"
    :customers="customers"
    @load-customers="loadCustomers"
    @assign="monitoringStore.assignUser"
    @unassign="monitoringStore.unassignUser"
    @close="toggleSidebar()"
  />

  <AlertComments
    :active="sidebarToDisplay === 'comments'"
    :loading="loading"
    :active-user-id="getUserShortId"
    :alert-id="sidebarAlertId"
    :comments="sidebarValues.comments"
    :customers="customers"
    @load-customer="monitoringStore.loadCustomer"
    @add-comment="monitoringStore.addComment"
    @uncomment="monitoringStore.uncomment"
    @close="toggleSidebar()"
  />
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useHeadSafe } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';
import Breadcrumb from '@/components/commons/breadcrumb.vue';
import AlertsFilters from '@/pages/monitoring/components/alerts-filters.vue';
import AlertAssign from '@/pages/monitoring/components/alert-assign.vue';
import AlertComments from '@/pages/monitoring/components/alert-comments.vue';

const { t } = useI18n();
const route = useRoute();
const { id } = route.params;

useHeadSafe({
  title: [t('alerts'), t('watchlist'), t('monitoring')].join(' - ')
});

const breadcrumbLinks = computed(() => [
  { label: t('monitoring'), path: '/monitoring' },
  { label: watchlist.value.isReady ? watchlist.value.name : t('alerts') }
]);

// Both sidebar
const sidebarAlertId = ref(null);
const sidebarToDisplay = ref('');

function toggleSidebar(context = null, alertId = null) {
  sidebarAlertId.value = alertId;
  sidebarToDisplay.value = context;
}

const sidebarValues = computed(() => {
  const defaultObject = {
    assignations: [],
    comments: []
  };

  if (!sidebarAlertId.value) return defaultObject;

  const alert = alerts.value.find(
    (alert) => alert.alertShortId === sidebarAlertId.value
  );

  if (!alert) return defaultObject;

  return {
    assignations: alert.assignations,
    comments: alert.comments.slice().reverse()
  };
});

// AUTH
const authStore = AuthStore();
const { getUserOrganisation, getUserShortId } = storeToRefs(authStore);

const monitoringStore = MonitoringStore();
const {
  watchlist,
  loading,
  alerts,
  alertsColumns,
  alertsParams,
  alertsFilter,
  customers
} = storeToRefs(monitoringStore);

if (!watchlist?.isReady) {
  monitoringStore.loadWatchlist({ watchlistId: id, withAlerts: false });
}

// SEARCH
function searchAlerts() {
  monitoringStore.searchAlerts({ watchlistShortId: id });
}

function paginateAlerts({ pagination }) {
  monitoringStore.setAlertsParams(pagination);
  searchAlerts();
}

// filters have changed
watch(alertsFilter.value, () => searchAlerts());

// USERS
function loadCustomers() {
  monitoringStore.loadOrganisationUsers(getUserOrganisation.value);
}

loadCustomers();
searchAlerts();
</script>

<style scoped>
.alerts-notification {
  background-color: var(--primaryLight);
}

.alerts-table :deep(thead) {
  position: sticky;
  top: 0;
  z-index: 1;
  backdrop-filter: blur(10px);
  background-color: var(--primarySmokeTransparent);
}

.company-name {
  display: block;
  white-space: normal;
  max-width: 10rem;
}
</style>
