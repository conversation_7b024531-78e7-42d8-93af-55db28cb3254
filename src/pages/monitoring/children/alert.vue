<script setup>
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { useHeadSafe } from '@vueuse/head';
import { formatDateWithTime } from '@/utils/date';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';
import Breadcrumb from '@/components/commons/breadcrumb.vue';
import AuditTrail from '@/pages/monitoring/components/audit-trail.vue';
import DiffCard from '@/pages/monitoring/components/diff-card.vue';
import AlertAssignationDialog from '@/pages/monitoring/components/alert-assignation-dialog.vue';

const $q = useQuasar();
const { t } = useI18n();
const route = useRoute();
const { id, alertId } = route.params;

const displayAssignDialog = ref(false);

// Comment
const newCommentText = ref('');

useHeadSafe({
  title: [t('event'), t('alerts'), t('watchlist'), t('monitoring')].join(' - ')
});

const breadcrumbLinks = computed(() => {
  const list = watchlist.value.isReady ? watchlist.value : {};
  const { isReady, name, id } = list;

  return [
    { label: t('monitoring'), path: '/monitoring' },
    {
      label: name || t('alerts'),
      path: isReady ? `/monitoring/${id}/alerts` : null
    },
    { label: t('event') }
  ];
});

// Auth Store
const authStore = AuthStore();
const { getUserOrganisation, getUserShortId } = storeToRefs(authStore);

// Monitoring Store
const monitoringStore = MonitoringStore();
const { loading, watchlist, alert, event, customers } =
  storeToRefs(monitoringStore);

const assignations = computed(() => {
  if (!alert?.value?.assignations) return [];

  return alert.value.assignations.map((assignation) => {
    const customer = customers.value.find(
      (customer) => customer.customerShortId === assignation.customerShortId
    );

    return {
      ...assignation,
      date: formatDateWithTime(assignation.assignationDate),
      name: customer?.name || '',
      email: customer?.email || ''
    };
  });
});

const auditTrails = computed(() => {
  if (!alert?.value?.auditTrails) return [];

  return alert.value.auditTrails
    .map((auditTrail) => {
      const customer = customers.value.find(
        (customer) => customer.customerShortId === auditTrail.customerShortId
      );

      return {
        ...auditTrail,
        customer,
        date: formatDateWithTime(auditTrail.createdAt)
      };
    })
    .reverse();
});

// Initial loads
if (!watchlist?.isReady) {
  monitoringStore.loadWatchlist({ watchlistId: id, withAlerts: false });
}

monitoringStore.loadAlert({ alertId, withAuditTrails: true });

if (getUserOrganisation.value) {
  monitoringStore.loadOrganisationUsers(getUserOrganisation.value);
}

watch(
  () => getUserOrganisation.value,
  (organisationShortId) => {
    monitoringStore.loadOrganisationUsers(organisationShortId);
  }
);

// Load events after the alert is loaded
watch(
  () => alert.value,
  async (watchedAlert) => {
    if (watchedAlert.eventShortId) {
      await monitoringStore.loadEvent(watchedAlert.eventShortId);
    }
  }
);

// Unasign confirmation modal
function unassignAlert(customerShortId) {
  $q.dialog({
    title: t('confirmUnassign'),
    message: t('alertUnassignConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    monitoringStore.unassignUser({
      alertShortId: alert.value.alertShortId,
      customerShortId,
      actingCustomerShortId: getUserShortId.value
    });
  });
}

function assignAlert(customerShortId) {
  monitoringStore.assignUser({
    alertShortId: alert.value.alertShortId,
    customerShortId,
    actingCustomerShortId: getUserShortId.value
  });
}

function unreviewAlert() {
  $q.dialog({
    title: t('confirmUnreview'),
    message: t('alertUnreviewConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    monitoringStore.unreview({
      alertShortId: alert.value.alertShortId,
      actingCustomerShortId: getUserShortId.value
    });
  });
}

function reviewAlert() {
  monitoringStore.review({
    alertShortId: alert.value.alertShortId,
    actingCustomerShortId: getUserShortId.value
  });
}

function resetComment() {
  newCommentText.value = '';
}

async function saveComment() {
  await monitoringStore.addComment({
    alertShortId: alert.value.alertShortId,
    actingCustomerShortId: getUserShortId.value,
    comment: newCommentText.value
  });
  newCommentText.value = '';
}
</script>

<template>
  <Breadcrumb :links="breadcrumbLinks" />

  <div v-if="event" class="event-base q-pr-lg q-py-lg">
    <div class="event-base-icon">
      <q-avatar :color="severityColors(event.severity)">
        <q-icon name="fa-regular fa-bell" color="white" />
      </q-avatar>
    </div>

    <div class="event-base-details">
      <q-chip dark :color="severityColors(event.severity)" size="lg">
        {{ $t(event.eventType) }}
      </q-chip>
    </div>
  </div>

  <div class="row">
    <!-- SIDEBAR -->
    <div v-if="alert" class="col-md-4 col-lg-3 font-size-small">
      <q-card class="q-mb-md">
        <q-card-section>
          <div class="text-subtitle1">{{ $t('alertInfos') }}</div>
          <q-separator class="q-my-sm" />

          <div class="row items-center q-pb-sm">
            <div class="col col-auto text-bold">{{ $t('severity') }}</div>
            <div class="col text-right">
              <q-chip
                v-if="event"
                :color="severityColors(event.severity)"
                :text-color="
                  severityColors(event.severity) !== 'white' ? 'white' : 'black'
                "
              >
                {{ $t(event.severity) }}
              </q-chip>
            </div>
          </div>

          <div class="row items-center q-pb-md">
            <div class="col col-auto text-bold">{{ $t('assignee') }}</div>
            <div class="col text-right q-pl-sm">
              <div v-if="assignations.length > 0" class="row no-wrap">
                <div class="q-pr-sm">
                  {{
                    assignations
                      .map((assignation) => assignation.name)
                      .join(', ')
                  }}
                </div>
                <q-btn
                  flat
                  size="xs"
                  @click="displayAssignDialog = !displayAssignDialog"
                >
                  <q-icon name="fa-regular fa-edit" />
                </q-btn>
              </div>

              <q-btn
                v-if="assignations.length === 0"
                flat
                no-caps
                color="primary"
                @click="displayAssignDialog = !displayAssignDialog"
              >
                {{ $t('notAssignee') }}
              </q-btn>

              <AlertAssignationDialog
                :active="displayAssignDialog"
                :assignations="assignations"
                :customers="customers"
                @close="displayAssignDialog = false"
                @assign="assignAlert"
                @unassign="unassignAlert"
              />
            </div>
          </div>

          <div class="row items-center q-pb-lg">
            <div class="col col-auto text-bold">{{ $t('alertDate') }}</div>
            <div class="col text-right">
              <span v-if="event">{{ event.date }}</span>
            </div>
          </div>

          <div class="row items-center q-pb-sm">
            <div class="col col-auto text-bold">{{ $t('watchlist') }}</div>
            <div class="col text-right">
              <span v-if="watchlist">{{ watchlist.name }}</span>
            </div>
          </div>
        </q-card-section>

        <q-separator />

        <!-- REVIEW -->
        <q-card-section class="text-right">
          <q-btn
            v-if="alert.alertLastStatus === 'REVIEWED'"
            :loading="loading"
            :label="$t('unreview')"
            color="red"
            outline
            @click="unreviewAlert"
          />

          <q-btn
            v-else
            :disable="assignations.length === 0"
            :loading="loading"
            :label="$t('review')"
            color="primary"
            outline
            @click="reviewAlert"
          />
        </q-card-section>
      </q-card>
    </div>

    <!-- MAIN -->
    <div class="col col-lg-9 q-px-xl" v-if="event">
      <div class="q-pb-lg">
        <DiffCard :from="event.diff.from" :to="event.diff.to" />
      </div>

      <!-- COMMENTS -->
      <div class="q-pb-md">
        <q-card>
          <h6 class="text-subtitle2 q-pa-sm q-ma-none">
            {{ $t('addComment') }}
          </h6>
          <q-separator class="q-mb-sm" />
          <q-editor
            v-model="newCommentText"
            :toolbar="[
              ['bold', 'italic', 'underline', 'strike'],
              ['undo', 'redo']
            ]"
            :placeholder="$t('commentPlaceholder')"
            flat
            toolbar-toggle-color="blue-5"
            toolbar-text-color="primary"
            :disable="loading"
            min-height="2rem"
          />
          <template v-if="newCommentText">
            <q-separator />
            <div class="row justify-end items-center">
              <q-btn
                :loading="loading"
                :label="$t('cancel')"
                flat
                @click="resetComment"
              />
              <q-btn
                :loading="loading"
                :label="$t('save')"
                flat
                color="primary"
                @click="saveComment"
              />
            </div>
          </template>
        </q-card>
      </div>

      <q-timeline>
        <q-timeline-entry
          v-for="auditTrail in auditTrails"
          :key="auditTrail.alertAuditTrailShortId"
          :subtitle="auditTrail.date"
        >
          <AuditTrail
            :type="auditTrail.auditTrailType"
            :meta="auditTrail.meta"
            :date="auditTrail.date"
            :customer="auditTrail.customer"
          />
        </q-timeline-entry>
      </q-timeline>
    </div>
  </div>
</template>

<style scoped>
.event-base {
  display: flex;
}

.event-base-icon {
  padding-right: 0.5rem;
}

.event-base-details {
  width: 100%;
  padding-right: 1.5rem;
}

.event-comment {
  padding-right: 3rem;
  padding-left: 3.5rem;
}
</style>
