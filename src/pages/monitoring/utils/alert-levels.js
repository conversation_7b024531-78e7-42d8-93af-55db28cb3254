export const CRITICAL = 'CRITICAL';
export const HIGH = 'HIGH';
export const MEDIUM = 'MEDIUM';
export const LOW = 'LOW';

export const types = [CRITICAL, HIGH, MEDIUM, LOW];

const typeToLevelMap = {
  COMPANY_NAME_SOCIAL: HIGH,
  COMPANY_NAME_COMMERCIAL: HIGH,
  COMPANY_NAME_ABBREVIATED: HIGH,
  COMPANY_NAME_ESTABLISHMENT: HIGH,
  COMPANY_LEGALREP_PERSON: HIGH,
  COMPANY_LEGALREP_COMPANY: HIGH,
  COMPANY_STAKEHOLDERS: HIGH,
  COMPANY_MANAGEMENT: HIGH,
  CO<PERSON><PERSON>Y_EXTERNAL_ROLES: LOW,
  COMPANY_RELATED_COMPANIES: HIGH,
  COMPANY_STATUS: HIGH,
  COMPANY_LEGAL_SITUATION: HIGH,
  COMPANY_INSOLVENCY: CRITICAL,
  COMP<PERSON>Y_BANKRUPTCY: CRITICAL,
  COMPANY_DELAYS: HIGH,
  COMPANY_LEGAL_CASE: HIGH,
  CO<PERSON><PERSON>Y_LEGAL_FORM: HIGH,
  COMPANY_NACE_CODES: MEDIUM,
  COMPANY_EMPLOYER_STATUS: MEDIUM,
  COMPANY_EMPLOYER_SIZE: MEDIUM,
  COMPANY_PRIMARY_ADDRESS: HIGH,
  COMPANY_OTHER_ADDRESS: LOW,
  COMPANY_CONTACTS: LOW,
  COMPANY_FINANCIALS: MEDIUM,
  COMPANY_SCORE: HIGH,
  COMPANY_RELATED_MENTION: MEDIUM,
  COMPANY_ESTABLISHMENTS: LOW,
  ESTABLISHMENT_PRIMARY_NAME: MEDIUM,
  ESTABLISHMENT_OTHER_NAME: LOW,
  PERSON_MANDATE: HIGH,
  PERSON_INDEPENDENT: HIGH,
  PERSON_CONSERVATORSHIP: CRITICAL,
  PERSON_DEATH: CRITICAL,
  PERSON_COMPANY_TROUBLE: HIGH,
  PERSON_RELATED_MENTION: MEDIUM,
  PERSON_ADDRESS: MEDIUM,
  PERSON_CONTACT: MEDIUM,
  LOCATION_CHANGE: LOW,
  LOCATION_ZONE: LOW,
  SEARCH_MENTION: MEDIUM
};

export function typeToLevel(type) {
  return typeToLevelMap?.[type] ?? null;
}

export function typesToLevels(types = []) {
  return [...new Set(types.map(typeToLevel))];
}

export function levelToTypes(level) {
  return Object.keys(typeToLevelMap).filter(
    (type) => typeToLevelMap[type] === level
  );
}

export function levelsToTypes(levels = []) {
  return [...new Set(levels.map(levelToTypes).flat())];
}
