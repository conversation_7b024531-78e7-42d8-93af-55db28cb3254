<template>
  <h1 class="page-title text-h6 text-bold q-ma-none">
    {{ $t('monitoring') }}
  </h1>

  <section>
    <div class="flex items-center justify-between no-wrap q-pt-md q-pb-sm">
      <p class="text-subtitle2 q-ma-none q-pr-lg">
        {{ $t('monitoringIntro') }}
      </p>

      <q-btn
        :label="$t('newWatchlist')"
        :disable="!isAllowedForAdvancedWatchlist"
        to="/monitoring/new"
        color="primary"
        icon="fa-regular fa-add"
        style="flex-shrink: 0"
      />
    </div>

    <FeatureTeasing
      v-if="!isAllowedForAdvancedWatchlist"
      :feature="COMPANY_ALERTS_ADVANCED"
      overlay
    />

    <!-- WATCHLISTS TABLE -->
    <q-table
      v-model:pagination="pagination"
      :loading="loading"
      :columns="columns"
      :rows="preparedWatchlists"
      :grid="isMobile"
      :class="['q-mt-md', { teasing: !isAllowedForAdvancedWatchlist }]"
      row-key="name"
      virtual-scroll
      wrap-cells
      bordered
      flat
    >
      <!-- Name -->
      <template #body-cell-name="props">
        <q-td :props="props">
          <router-link
            :to="`/monitoring/${props.row.id}/alerts`"
            class="text-subtitle2 text-bold"
          >
            {{ props.row.name }}
          </router-link>
        </q-td>
      </template>
      <!-- COMPANIES COUNT -->
      <template #body-cell-watchedCompanies="props">
        <q-td :props="props" class="white-space-nowrap">
          <span class="q-pr-sm">
            {{
              $t('companiesPlural', {
                count: props.row.watchedCompanies.length
              })
            }}
          </span>
          <router-link :to="`/monitoring/${props.row.id}?tab=companies`">
            <q-icon name="fa-solid fa-pen" color="grey-5" />
          </router-link>
        </q-td>
      </template>
      <!-- SUBSCRIBERS COUNT -->
      <template #body-cell-subscribersCount="props">
        <q-td :props="props" class="white-space-nowrap">
          <span class="q-pr-sm">
            {{ $t('subscribersPlural', { count: props.row.subscribersCount }) }}
          </span>
          <router-link :to="`/monitoring/${props.row.id}?tab=subscribers`">
            <q-icon name="fa-solid fa-pen" color="grey-5" />
          </router-link>
        </q-td>
      </template>
      <!-- SEVERITY -->
      <template #body-cell-severity="props">
        <q-td :props="props">
          <q-chip
            v-for="severity in props.row.alertLevels"
            :key="`${props.row.id}-${severity}`"
            :color="severityColors(severity)"
            text-color="primary"
            outline
            class="q-mx-xs text-white"
          >
            {{ $t(severity) }}
          </q-chip>
          <div v-if="props.row.alertLevels.length === 0">&mdash;</div>
        </q-td>
      </template>
      <!-- TAGS -->
      <template #body-cell-tags="props">
        <q-td :props="props">
          <q-chip
            v-for="tag in props.row.tags"
            :key="`${props.row.id}-${tag}`"
            :color="$q.dark.isActive ? 'primary' : 'blue-1'"
            :text-color="$q.dark.isActive ? 'black' : 'primary'"
            class="q-mx-xs"
            square
          >
            {{ tag }}
          </q-chip>
          <div v-if="props.row.tags.length === 0">&mdash;</div>
        </q-td>
      </template>
      <!-- ACTIONS -->
      <template #body-cell-actions="props">
        <q-td :props="props">
          <q-btn icon="fa-regular fa-ellipsis" color="grey" size="sm" flat>
            <q-menu>
              <q-list class="action-list">
                <q-item :to="`/monitoring/${props.row.id}`">
                  <q-item-section>
                    <q-item-label>{{ $t('edit') }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item
                  clickable
                  v-close-popup
                  @click="deleteWatchlist(props.row.id)"
                >
                  <q-item-section>
                    <q-item-label>{{ $t('remove') }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>
        </q-td>
      </template>
    </q-table>
  </section>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useHeadSafe } from '@vueuse/head';
import { COMPANY_ALERTS_ADVANCED } from '@/utils/features';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import { teasingMonitoringData } from '@/pages/monitoring/data/teasing-monitoring';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';
import formatRowMobile from '@/pages/monitoring//utils/format-row-mobile';
import watchlistsColumns from '@/pages/monitoring/data/watchlists-columns';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

const props = defineProps({ isMobile: Boolean });
const $q = useQuasar();
const { t } = useI18n();
const router = useRouter();

useHeadSafe({ title: t('monitoring') });

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForAdvancedWatchlist = computed(() =>
  features.value.includes(COMPANY_ALERTS_ADVANCED)
);

const monitoringStore = MonitoringStore();
const { loading, watchlists } = storeToRefs(monitoringStore);
const preparedWatchlists = computed(() => {
  if (!isAllowedForAdvancedWatchlist.value) {
    return teasingMonitoringData;
  }

  return watchlists.value;
});
const pagination = ref({
  rowsPerPage: 0,
  page: 1,
  sortBy: 'name'
});

function deleteWatchlist(id) {
  $q.dialog({
    title: 'Confirm delete',
    message: t('listDeletionConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    monitoringStore.removeWatchlist(id);
  });
}

function init(organisationShortId) {
  if (isAllowedForAdvancedWatchlist.value) {
    monitoringStore.loadWatchlists(organisationShortId);
  }
}

onMounted(() => {
  if (!authStore.getUserOrganisation) {
    // wait for user to be loaded
    return watch(
      () => authStore.getUserOrganisation,
      (organisationShortId) => {
        init(organisationShortId);
      }
    );
  }

  init(authStore.getUserOrganisation);
});

const columns = computed(() => {
  if (!props.isMobile) return watchlistsColumns;

  return formatRowMobile(watchlistsColumns, deleteWatchlist, t, router.push);
});
</script>

<style scoped>
.page-title {
  padding-top: 0.75rem;
}

.action-list {
  font-size: var(--xsmall);
}

.teasing {
  filter: blur(3px);
}
</style>
