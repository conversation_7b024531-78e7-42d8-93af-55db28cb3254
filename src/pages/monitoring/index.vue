<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useHeadSafe } from '@vueuse/head';
import { COMPANY_ALERTS_ADVANCED } from '@/utils/features';
import { MonitoringStore } from '@/pages/monitoring/store';
import { AuthStore } from '@/pages/auth/store';
import { teasingMonitoringData } from '@/pages/monitoring/data/teasing-monitoring';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';
import formatRowMobile from '@/pages/monitoring//utils/format-row-mobile';
import watchlistsColumns from '@/pages/monitoring/data/watchlists-columns';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

const props = defineProps({ isMobile: Boolean });
const $q = useQuasar();
const { t } = useI18n();
const router = useRouter();

useHeadSafe({ title: t('monitoring') });

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForAdvancedWatchlist = computed(() =>
  features.value.includes(COMPANY_ALERTS_ADVANCED)
);

const monitoringStore = MonitoringStore();
const { loading, watchlists } = storeToRefs(monitoringStore);
const preparedWatchlists = computed(() => {
  if (!isAllowedForAdvancedWatchlist.value) {
    return teasingMonitoringData;
  }

  return watchlists.value;
});
const pagination = ref({
  rowsPerPage: 0,
  page: 1,
  sortBy: 'name'
});

function deleteWatchlist(id) {
  $q.dialog({
    title: 'Confirm delete',
    message: t('listDeletionConfirmation'),
    cancel: true,
    persistent: true
  }).onOk(() => {
    monitoringStore.removeWatchlist(id);
  });
}

function init(organisationShortId) {
  if (isAllowedForAdvancedWatchlist.value) {
    monitoringStore.loadWatchlists(organisationShortId);
  }
}

onMounted(() => {
  if (!authStore.getUserOrganisation) {
    // wait for user to be loaded
    return watch(
      () => authStore.getUserOrganisation,
      (organisationShortId) => {
        init(organisationShortId);
      }
    );
  }

  init(authStore.getUserOrganisation);
});

const columns = computed(() => {
  if (!props.isMobile) return watchlistsColumns;

  return formatRowMobile(watchlistsColumns, deleteWatchlist, t, router.push);
});
</script>

<template>
  <h1 class="page-title text-h6 text-bold q-ma-none">
    {{ $t('monitoring') }}
  </h1>

  <section>
    <div class="flex items-center justify-between no-wrap q-pt-md q-pb-sm">
      <p class="text-subtitle2 q-ma-none q-pr-lg">
        {{ $t('monitoringIntro') }}
      </p>

      <q-btn
        :label="$t('newWatchlist')"
        :disable="!isAllowedForAdvancedWatchlist"
        to="/monitoring/new"
        color="primary"
        icon="fa-regular fa-add"
        style="flex-shrink: 0"
      />
    </div>

    <FeatureTeasing
      v-if="!isAllowedForAdvancedWatchlist"
      :feature="COMPANY_ALERTS_ADVANCED"
      overlay
    />

    <!-- WATCHLISTS TABLE -->
    <q-table
      v-model:pagination="pagination"
      :loading="loading"
      :columns="columns"
      :rows="preparedWatchlists"
      :grid="isMobile"
      :class="['q-mt-md', { teasing: !isAllowedForAdvancedWatchlist }]"
      row-key="name"
      virtual-scroll
      wrap-cells
      bordered
      flat
    >
      <!-- Name -->
      <template #body-cell-name="props">
        <q-td :props="props">
          <router-link
            :to="`/monitoring/${props.row.id}/alerts`"
            class="text-subtitle2 text-bold"
          >
            {{ props.row.name }}
          </router-link>
        </q-td>
      </template>

<style scoped>
.page-title {
  padding-top: 0.75rem;
}

.action-list {
  font-size: var(--xsmall);
}

.teasing {
  filter: blur(3px);
}
</style>
