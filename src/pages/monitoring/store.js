import { defineStore } from 'pinia';
import Api from '@/api';
import { notify, notifyError } from '@/utils/notifications';
import alertsColumns from '@/pages/monitoring/data/alerts-columns';
import listsColumns from '@/pages/monitoring/data/lists-columns';
import { levelsToTypes } from '@/pages/monitoring/utils/alert-levels';
import i18n from '@/plugins/i18n';

function getRangeFromPeriod(period) {
  const today = new Date().toISOString().slice(0, 10);

  if (period === 'TODAY') {
    return { gte: today, lte: today };
  }

  if (period === 'YESTERDAY') {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return {
      gte: yesterday.toISOString().slice(0, 10),
      lte: today
    };
  }

  // default to last week
  const lastWeek = new Date();
  lastWeek.setDate(lastWeek.getDate() - 7);
  return {
    gte: lastWeek.toISOString().slice(0, 10),
    lte: today
  };
}

function getNewWatchlist() {
  return {
    id: '',
    name: '',
    type: '',
    frequency: '',
    alertLevels: [],
    lastestAlert: null,
    watchedCompanies: [],
    subscribers: [],
    latestAlert: null,
    tags: [],
    visibility: '',
    isReady: false,
    watchlistType: 'ADVANCED',
    watchlistContent: 'COMPANY'
  };
}

const state = () => ({
  loading: false,
  tableStyle: 'list',
  watchlists: [],
  event: null,
  alert: {},
  alertsParams: {
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
    sortBy: 'updatedAt',
    descending: true
  },
  alertsFilter: {
    dates: null,
    period: 'LASTWEEK',
    event: null,
    status: null
  },
  alerts: [],
  companies: [],
  companiesCurrentPage: 1,
  companiesPerPage: 10,
  companiesTotal: 0,
  watchlist: getNewWatchlist(),
  subscribers: [],
  customers: [],
  alertsColumns,
  listsColumns
});

const getters = {
  count: (state) => state.alerts?.length || 0,
  inspectAlert: (state) => (id) => state.alerts.find((alert) => alert.id === id)
};

const actions = {
  async loadWatchlists(organisationShortId) {
    this.loading = true;
    const data = await Api.watchlists.byOrganisation(organisationShortId);

    if (data.isError) {
      notifyError(null, i18n.global.t('watchlistLoadError'));
    } else {
      this.watchlists = data;
    }

    this.loading = false;
  },

  async loadWatchlist({ watchlistId, withAlerts }) {
    this.loading = true;

    if (watchlistId === 'new') {
      this.watchlist = getNewWatchlist();
      this.loading = false;
      return;
    }

    const data = await Api.watchlists.byId({
      watchlistId,
      withAlerts
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorLoadWatchlist'));
    } else {
      const { alerts, ...watchlist } = data;
      this.watchlist = watchlist;

      if (Array.isArray(alerts) && alerts.length) {
        this.alerts = alerts;
      }
    }

    this.watchlist.isReady = true;
    this.loading = false;
  },

  async loadWatchlistCompany(identifier) {
    this.loading = true;

    if (
      this.companies.findIndex((company) => company.id === identifier) !== -1
    ) {
      this.loading = false;
      return;
    }

    const data = await Api.companies.byId(identifier);

    if (data.isError) {
      notifyError(null, i18n.global.t('errorLoadCompany'));
    } else {
      this.companies = [...this.companies, data];
    }

    this.loading = false;
  },

  setCompaniesPerPage(rowsPerPage) {
    this.companiesPerPage = rowsPerPage;
    localStorage.setItem('preference-pagination', rowsPerPage);
  },

  setAlertsParams(pagination) {
    this.alertsParams = pagination;
  },

  async searchAlerts({ watchlistShortId }) {
    this.loading = true;
    const params = {
      filters: { watchlistShortId },
      from: (this.alertsParams.page - 1) * this.alertsParams.rowsPerPage,
      size: this.alertsParams.rowsPerPage
    };

    if (this.alertsParams.sortBy) {
      params.sort = [
        {
          [this.alertsParams.sortBy]: this.alertsParams.descending
            ? 'DESC'
            : 'ASC'
        }
      ];
    }

    // Filters: status
    if (
      Array.isArray(this.alertsFilter.status) &&
      this.alertsFilter.status.length
    ) {
      params.filters.alertLastStatus = this.alertsFilter.status;
    }

    // Filter: date
    if (this.alertsFilter.dates !== null) {
      // Date is string if same day selection
      const gte =
        typeof this.alertsFilter.dates === 'string'
          ? this.alertsFilter.dates
          : this.alertsFilter.dates.from;
      const lte =
        typeof this.alertsFilter.dates === 'string'
          ? this.alertsFilter.dates
          : this.alertsFilter.dates.to;

      params.filters.createdAt = {
        gte,
        lte
      };
    }

    // Filter: event
    if (
      this.alertsFilter.event !== null &&
      Array.isArray(this.alertsFilter.event) &&
      this.alertsFilter.event.length > 0
    ) {
      params.filters.indexedMeta = {};
      params.filters.indexedMeta.eventType = this.alertsFilter.event;
    }

    // Filter: pediod
    if (this.alertsFilter.period !== null) {
      params.filters.createdAt = getRangeFromPeriod(this.alertsFilter.period);
    }

    const data = await Api.alerts.query(params);
    const identifiers = data.alerts.map((alert) => alert.companyIdentifier);
    const companies = await Api.companies.searchByIdentifers([
      ...new Set(identifiers)
    ]);

    data.alerts.forEach((alert) => {
      const company = companies.find(
        (company) => company.id === alert.companyIdentifier
      );

      if (company) {
        alert.companyName = company.name;
      }
    });

    if (data.isError) {
      notifyError(i18n.global.t('alertsLoadError'));
    } else {
      const identifiers = data.alerts.map((alert) => alert.companyIdentifier);
      const companies = await Api.companies.searchByIdentifers([
        ...new Set(identifiers)
      ]);

      data.alerts.forEach((alert) => {
        const company = companies.find(
          (company) => company.id === alert.companyIdentifier
        );

        if (company) {
          alert.companyName = company.name;
        }
      });
      this.alerts = data.alerts;
      this.alertsParams.rowsNumber = data.count;
    }

    this.loading = false;
  },

  async loadWatchlistCompanies(page = 1) {
    if (this.watchlist.watchedCompanies.length === 0) {
      this.companies = [];
      this.companiesTotal = 0;
      this.companiesCurrentPage = 1;
      return;
    }

    this.loading = true;

    const data = await Api.companies.search(
      {
        identifiers: this.watchlist.watchedCompanies,
        max: this.companiesPerPage,
        page
      },
      true
    );

    if (data.isError) {
      notifyError(null, i18n.global.t('genericError'));
    } else {
      this.companies = data.companies;
      this.companiesTotal = data.total;
      this.companiesCurrentPage = page;
    }

    this.loading = false;
  },

  async addCompanyToWatchlist({
    watchlistShortId,
    companyIdentifier,
    organisationShortId
  }) {
    this.loading = true;
    const data = await Api.watchlists.addToWatchlist({
      organisationShortId,
      watchlistShortId,
      companyIdentifier
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorAddToWatchlist'));
    } else {
      this.watchlist = data;
    }

    this.loading = false;
  },

  async removeCompanyFromWatchlist({ watchlistShortId, companyIdentifier }) {
    this.loading = true;
    const data = await Api.watchlists.removeFromWatchlist({
      watchlistShortId,
      companyIdentifier
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorRemoveFromWatchlist'));
    } else {
      this.watchlist = data;
    }

    this.loading = false;
  },

  async createWatchlist({ watchlist, organisationShortId }) {
    this.loading = true;
    watchlist.eventTypes = levelsToTypes(watchlist.alertLevels);
    delete watchlist.id;

    const data = await Api.watchlists.create({
      ...watchlist,
      organisationShortId
    });

    if (data.isError) {
      notifyError(data);
      return { success: false };
    } else {
      this.watchlist = data;
      notify({ message: i18n.global.t('watchlistCreated'), type: 'positive' });
    }

    this.loading = false;
    return { success: !data.isError };
  },

  async updateWatchlist(watchlist) {
    this.loading = true;
    watchlist.eventTypes = levelsToTypes(watchlist.alertLevels);
    const defaultWatchlist = getNewWatchlist();
    const data = await Api.watchlists.update({
      ...defaultWatchlist,
      ...watchlist
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorUpdateWatchlist'));
    } else {
      this.watchlist = data;
      notify({ message: i18n.global.t('watchlistSaved'), type: 'positive' });
    }

    this.loading = false;
  },

  async removeWatchlist(watchlistId) {
    this.loading = true;
    const data = await Api.watchlists.remove(watchlistId);

    if (data.isError) {
      notifyError(null, i18n.global.t('watchlistCannotBeDeleted'));
    }

    if (data.success) {
      this.watchlists = this.watchlists.filter(
        (watchlist) => watchlist.id !== watchlistId
      );
      notify({
        message: i18n.global.t('watchlitDeletedWithSuccess'),
        type: 'positive'
      });
    }

    this.loading = false;
  },

  async addSubscriber(email) {
    this.loading = true;
    const data = await Api.watchlists.addSubscriber({
      watchlistId: this.watchlist.id,
      email,
      type: 'EMAIL',
      frequency: 'DAILY'
    });

    if (data.isError) {
      notifyError(data);
    } else {
      this.watchlist = data;
      notify({
        message: i18n.global.t('subscribersAddedSuccess'),
        type: 'positive'
      });
    }

    this.loading = false;
  },

  async updateSubscriber({ id, frequency }) {
    this.loading = true;

    this.watchlist.subscribers.forEach((subscriber) => {
      if (subscriber.notificationShortId === id) {
        subscriber.notificationInterval = frequency;
      }
    });

    const defaultWatchlist = getNewWatchlist();
    this.watchlist = {
      ...defaultWatchlist,
      ...this.watchlist
    };
    const data = await Api.watchlists.update(this.watchlist);

    if (data.isError) {
      notifyError(data);
    } else {
      this.watchlist = data;
      notify({
        message: i18n.global.t('subscribersUpdatedSuccess'),
        type: 'positive'
      });
    }

    this.loading = false;
  },

  async removeSubscriber(subscriberId) {
    this.loading = true;

    const data = await Api.watchlists.removeSubscriber({
      watchlistId: this.watchlist.id,
      notificationShortId: subscriberId
    });

    if (data.isError) {
      notifyError(data);
    } else {
      this.watchlist = data;
      notify({
        message: i18n.global.t('subscribersRemoveSuccess'),
        type: 'positive'
      });
    }

    this.loading = false;
  },

  async loadOrganisationUsers(companyShortId) {
    if (typeof companyShortId !== 'string') return;

    this.loading = true;

    const data = await Api.customers.byOrganisation(companyShortId);

    if (data.isError) {
      notifyError(null, i18n.global.t('genericError'));
    } else {
      this.customers = data;
    }

    this.loading = false;
  },

  async loadCustomer(customerShortId) {
    this.loading = true;

    if (
      this.customers.findIndex(
        (customer) => customer.customerShortId === customerShortId
      ) !== -1
    ) {
      this.loading = false;
      return;
    }

    const data = await Api.customers.byId(customerShortId);

    if (data.isError) {
      notifyError(null, i18n.global.t('genericError'));
    } else {
      this.customers.push(data);
    }

    this.loading = false;
  },

  async loadAlert({ alertId, withAuditTrails }) {
    this.loading = true;
    const data = await Api.alerts.byId({ alertId, withAuditTrails });

    if (data.isError) {
      notifyError(data);
    } else {
      this.alert = data;
    }

    this.loading = false;
  },

  async loadEvent(eventId) {
    this.loading = true;
    const data = await Api.events.byId(eventId);

    if (data.isError) {
      notifyError(data);
    } else {
      this.event = data;
    }

    this.loading = false;
  },

  async assignUser({ alertShortId, customerShortId, actingCustomerShortId }) {
    this.loading = true;

    const data = await Api.alerts.assign({
      alertShortId,
      customerShortId,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.updatedAt = data.updatedAt;
          alert.alertLastStatus = data.alertLastStatus;
          alert.assignations = data.assignations;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertAssignSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async unassignUser({ alertShortId, customerShortId, actingCustomerShortId }) {
    this.loading = true;

    const data = await Api.alerts.unassign({
      alertShortId,
      customerShortId,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.updatedAt = data.updatedAt;
          alert.alertLastStatus = data.alertLastStatus;
          alert.assignations = data.assignations;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertUnassignSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async addComment({ alertShortId, actingCustomerShortId, comment }) {
    this.loading = true;

    const data = await Api.alerts.comment({
      alertShortId,
      comment,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts.length) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.comments = data.comments;
          alert.updatedAt = data.updatedAt;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertCommentSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async uncomment({ commentShortId, alertShortId, actingCustomerShortId }) {
    this.loading = true;

    const data = await Api.alerts.uncomment({
      commentShortId,
      alertShortId,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.comments = data.comments;
          alert.updatedAt = data.updatedAt;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertUncommentSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async review({ alertShortId, actingCustomerShortId }) {
    this.loading = true;

    const data = await Api.alerts.review({
      alertShortId,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.alertLastStatus = data.alertLastStatus;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertReviewSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async unreview({ alertShortId, actingCustomerShortId }) {
    this.loading = true;

    const data = await Api.alerts.unreview({
      alertShortId,
      actingCustomerShortId
    });

    if (!data.isError && this.alerts) {
      this.alerts.forEach((alert) => {
        if (alert.alertShortId === data.alertShortId) {
          alert.alertLastStatus = data.alertLastStatus;
        }
      });
    }

    // reload audit trails
    if (!data.isError) {
      await this.loadAlert({ alertId: alertShortId, withAuditTrails: true });
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertUnreviewSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });

    this.loading = false;
  },

  async uploadCompanies({ watchlistId = '', companies = '' }) {
    const preparedCompanies = companies
      .split(/[\n,]/)
      .map((company) =>
        // cleanup (keep only numbers)
        company.trim().replace(/\D/g, '')
      )
      .map((company) => `BE_${company}`);

    const data = await Api.watchlists.uploadCompanies({
      watchlistId,
      companies: preparedCompanies
    });

    if (!data.isError) {
      this.watchlist = data;
      await this.loadWatchlistCompanies();
    }

    notify({
      message: data.isError
        ? data.error.message
        : i18n.global.t('alertUploadSuccess'),
      type: data.isError ? 'negative' : 'positive'
    });
  }
};

export const MonitoringStore = defineStore('monitoring', {
  state,
  getters,
  actions
});
