import { defineStore } from 'pinia';
import Api from '@/api';
import { notifyError } from '@/utils/notifications';
import { featuresByLevel } from '@/utils/features';
import i18n from '@/plugins/i18n-dynamic';
import { setLocale as setDynamicLocale } from '@/plugins/i18n-dynamic';
import { clearCookie } from '@/utils/cookies';

async function setLocale(locale = 'en') {
  try {
    await setDynamicLocale(locale);
  } catch (error) {
    console.error('Failed to set locale:', error);
    // Fallback to setting locale directly if dynamic loading fails
    i18n.global.locale.value = 'en';
  }
}

const state = () => ({
  loading: false,
  user: null,
  features: []
});

const getters = {
  getUserOrganisation: (state) => {
    if (!state.user) return null;

    return state.user.organisationShortId;
  },

  getUserEmail: (state) => {
    if (!state.user) return null;
    return state.user.email;
  },

  getUserShortId: (state) => {
    if (!state.user) return null;
    return state.user.customerShortId;
  }
};

const actions = {
  getToken() {
    return Api.httpClient.getToken();
  },

  async login({ email, password }) {
    this.loading = true;

    const { user, token, error } = await Api.auth.login({ email, password });

    if (error) {
      console.error(error.message);
      Api.httpClient.clearToken();
      notifyError(null, i18n.global.t('loginError'));
      this.loading = false;
      throw error;
    }

    this.user = user;
    setLocale(user.language);

    Api.httpClient.setToken(token);

    this.features = featuresByLevel(user.level);
    this.loading = false;

    return true;
  },

  async logout() {
    await Api.auth.logout();
    Api.httpClient.clearToken();
  },

  async loggedUser() {
    this.user = await Api.auth.loggedUser();

    if (!this.user) {
      clearCookie('token');
      Api.httpClient.clearToken();
      window.location.href = '/login';
    }

    setLocale(this.user.language);
    this.features = featuresByLevel(this.user.level);
    return this.user;
  }
};

export const AuthStore = defineStore('auth', {
  state,
  getters,
  actions
});
