<template>
  <unAuthContainer>
    <q-form
      v-model="valid"
      class="login-form shadow-24 q-pa-xl rounded-borders"
      @submit.prevent="submit"
    >
      <img src="@/assets/logo.svg" alt="" class="logo q-mx-auto q-mb-lg" />

      <q-input
        v-model="email"
        :rules="[emailRule, requiredField]"
        :readonly="loading"
        :label="$t('email')"
        type="email"
        name="email"
        autocomplete="email"
        outlined
      />

      <q-input
        v-model="password"
        :readonly="loading"
        :rules="[passwordRule, requiredField]"
        :label="$t('password')"
        :type="isPasswordVisible ? 'text' : 'password'"
        name="password"
        autocomplete="password"
        outlined
      >
        <template v-slot:append>
          <q-icon
            :name="
              isPasswordVisible
                ? 'fa-regular fa-eye'
                : 'fa-regular fa-eye-slash'
            "
            size="xs"
            @click="isPasswordVisible = !isPasswordVisible"
          />
        </template>
      </q-input>

      <q-btn
        :loading="loading"
        type="submit"
        color="primary"
        class="full-width q-mt-md"
        unelevated
        no-caps
        size="lg"
      >
        {{ $t('signIn') }}
      </q-btn>

      <p class="q-pt-md q-mb-none text-center">
        <a :href="`${accountURL}/register`" class="text-caption">
          {{ $t('createAccount') }}
        </a>
      </p>
      <p class="q-mb-none text-center">
        <a :href="`${accountURL}/reset-password`" class="text-caption">
          {{ $t('forgotPassword') }}
        </a>
      </p>
    </q-form>
  </unAuthContainer>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useHeadSafe } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { useRouter, useRoute } from 'vue-router';
import { AuthStore } from '@/pages/auth/store';
import { checkEmail, checkPassword, requiredField } from '@/utils/policies';
import { getAccountUrl } from '@/utils/account-url';
import unAuthContainer from '@/components/layouts/unauthenticated.vue';

useHeadSafe({ title: 'Login' });
const { t } = useI18n();

const router = useRouter();
const route = useRoute();
const authStore = AuthStore();

const valid = ref(false);
const email = ref('');
const password = ref('');
const isPasswordVisible = ref(false);

const emailRule = (val) => {
  return checkEmail(val) || t('emailPolicy');
};

const passwordRule = (val) => {
  return checkPassword(val) || t('passwordPolicy');
};

const submit = async () => {
  const success = await authStore.login({
    email: email.value,
    password: password.value
  });

  let redirect = { name: 'dashboard' };

  if (route.redirectedFrom?.fullPath) {
    redirect = { path: route.redirectedFrom.fullPath };
  }

  if (success) {
    router.push(redirect);
  }
};

const { loading } = storeToRefs(authStore);

const accountURL = getAccountUrl(window.location.hostname);
</script>

<style scoped>
.login-form {
  background: var(--white);
  width: calc(100vw - 2rem);
  max-width: 30rem;
  margin: 0 auto;
}

.logo {
  max-width: 200px;
}
</style>
