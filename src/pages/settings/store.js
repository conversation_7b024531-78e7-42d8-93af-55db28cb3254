import { defineStore } from 'pinia';
import Api from '@/api';
import { notify, notifyError } from '@/utils/notifications';
import i18n from '@/plugins/i18n';

const state = () => ({
  loading: false,
  organisation: null
});

const actions = {
  async updateUser(params) {
    this.loading = true;

    const data = await Api.auth.update(params);

    if (data.isError) {
      notifyError(data);
    } else {
      notify({
        message: i18n.global.t('userUpdatedSuccess'),
        type: 'positive'
      });
    }

    this.loading = false;
  },

  async loadOrganisation(organisationShortId) {
    this.loading = true;

    const data = await Api.customers.getOrganisation(organisationShortId);

    if (data.isError) {
      notifyError(data);
    } else {
      this.organisation = data;
    }

    this.loading = false;
  }
};

export const SettingsStore = defineStore('settings', {
  state,
  actions
});
