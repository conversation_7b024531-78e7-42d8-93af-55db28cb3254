<template>
  <div>
    <h1 class="page-title text-h6 text-bold q-ma-none q-pb-md">
      {{ $t('settings') }}
    </h1>

    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div v-if="user" class="row q-col-gutter-md">
          <div class="col col-md-4">
            <q-avatar
              size="5rem"
              :style="{ color: 'var(--white)', background: user.color }"
              class="q-mb-md"
            >
              {{ user.initials }}
            </q-avatar>
            <p class="text-caption">
              {{ $t('name') }}: {{ user.name }}<br />
              {{ $t('lastUpdate') }}: {{ user.latestUpdate }}<br />
              {{ $t('subscription') }}:
              {{ user.level }}
            </p>
            <p v-show="organisation" class="text-caption">
              {{ $t('organisation') }}: {{ organisation?.name }}<br />
              {{ $t('lastUpdate') }}: {{ organisation?.latestUpdate }}
            </p>
          </div>

          <div class="col col-md-4">
            <q-input
              v-model="user.firstName"
              outlined
              :label="$t('firstName')"
              :loading="loading"
              name="firstName"
              class="q-my-md"
            />
            <q-input
              v-model="user.lastName"
              outlined
              :label="$t('lastName')"
              :loading="loading"
              name="lastName"
              class="q-my-md"
            />
          </div>
          <div class="col col-md-4">
            <q-input
              v-model="user.email"
              outlined
              :label="$t('email')"
              :loading="loading"
              name="email"
              autocomplete="email"
              class="q-my-md"
            />
            <q-select
              v-model="userLanguage"
              :options="localesOptions"
              :label="$t('selectLanguage')"
              :display-value="$t(userLanguage)"
              :loading="loading"
              class="q-my-md"
              emit-value
              outlined
            />
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          :loading="loading"
          :label="$t('updateUserInfos')"
          flat
          color="primary"
          class="flex-grow-0"
          @click="updateUser"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useHeadSafe } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { SettingsStore } from '@/pages/settings/store';
import { AuthStore } from '@/pages/auth/store';

useHeadSafe({ title: 'Settings' });

const { t, locale, availableLocales } = useI18n();
const settingsStore = SettingsStore();
const authStore = AuthStore();
const { user } = storeToRefs(authStore);
const { loading, organisation } = storeToRefs(settingsStore);
const userLanguage = ref(user.value?.language);

const localesOptions = computed(() =>
  availableLocales.map((availableLocale) => ({
    label: t(availableLocale),
    value: availableLocale
  }))
);

onMounted(() => init());

watch(
  () => user.value,
  () => init()
);

function init() {
  userLanguage.value = user.value?.language;

  if (!availableLocales.includes(userLanguage.value)) {
    userLanguage.value = 'en';
  }

  if (user.value && user.value.organisationShortId && !organisation.value) {
    settingsStore.loadOrganisation(user.value.organisationShortId);
  }
}

function updateUser() {
  settingsStore.updateUser({
    id: user.value.customerShortId,
    firstName: user.value.firstName,
    lastName: user.value.lastName,
    email: user.value.email,
    language: userLanguage.value
  });
  // update app locale
  locale.value = userLanguage.value;
}
</script>

<style scoped>
.page-title {
  padding-top: 0.75rem;
}
</style>
