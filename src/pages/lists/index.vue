<script setup>
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ListsStore } from '@/pages/lists/store';
import { AuthStore } from '@/pages/auth/store';
import listsColumns from '@/pages/lists/data/lists-columns';
import { severityColors } from '@/pages/monitoring/utils/severity-colors';

defineProps({ isMobile: Boolean });

const authStore = AuthStore();
const organisation = ref(authStore.getUserOrganisation);
const listsStore = ListsStore();
const { loading, lists } = storeToRefs(listsStore);

watch(
  () => authStore.getUserOrganisation,
  (newOrga) => {
    organisation.value = newOrga;
    listsStore.loadLists(newOrga);
  }
);

if (organisation.value) {
  listsStore.loadLists(organisation.value);
}

const selectedLists = ref([]);

async function removeSelectedLists() {
  await listsStore.removeSelectedLists(
    selectedLists.value.map((list) => list.id)
  );
  selectedLists.value = [];
  listsStore.loadLists(organisation.value);
}
</script>

<template>
  <h1 class="page-title text-h6 text-bold q-ma-none">
    {{ $t('lists') }}
  </h1>

  <section>
    <div class="row items-center justify-between q-gutter-md q-py-md">
      <div class="text-subtitle2">{{ $t('listIntro') }}</div>

      <div>
        <q-btn
          :loading="loading"
          :label="`${$t('remove')} (${selectedLists.length})`"
          :disable="!selectedLists.length"
          color="red"
          icon="fa-regular fa-close"
          class="q-mr-sm"
          outline
          @click="removeSelectedLists"
        />
        <q-btn
          :label="$t('newList')"
          :loading="loading"
          to="/lists/new"
          color="primary"
          icon="fa-regular fa-add"
        />
      </div>
    </div>

    <q-table
      v-model:selected="selectedLists"
      :loading="loading"
      :rows="lists"
      :columns="listsColumns"
      :grid="isMobile"
      row-key="id"
      class="q-mt-md"
      selection="multiple"
      color="primary"
      virtual-scroll
      hide-bottom
      wrap-cells
      bordered
      flat
    >
      <template #body-cell-name="props">
        <q-td :props="props">
          <router-link
            :to="`/lists/${props.row.watchlistShortId}`"
            class="text-subtitle2 text-bold"
          >
            {{ props.row.name }}
          </router-link>
        </q-td>
      </template>

<style scoped>
.page-title {
  padding-top: 0.75rem;
}
</style>
