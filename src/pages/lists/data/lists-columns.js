import i18n from '@/plugins/i18n-dynamic';
import { format } from 'timeago.js';

export default [
  {
    label: i18n.global.t('name'),
    field: 'name',
    name: 'name',
    align: 'left'
  },
  {
    label: i18n.global.t('type'),
    field: 'alertLevels',
    align: 'center',
    name: 'severity'
  },
  {
    label: i18n.global.t('companies'),
    field: 'companiesCount',
    name: 'companiesCount',
    align: 'center',
    format: (count) => i18n.global.t('companiesPlural', count)
  },
  {
    label: i18n.global.t('lastUpdate'),
    field: 'updatedAt',
    name: 'updatedAt',
    align: 'left',
    format: (date) => format(date)
  },
  {
    label: i18n.global.t('tags'),
    field: 'tags',
    name: 'tags',
    align: 'left',
    format: (tags) => tags.join(', ')
  },
  {
    name: 'icon',
    align: 'right'
  }
];
