import { defineStore } from 'pinia';
import Api from '@/api';
import { notifyError, notify } from '@/utils/notifications';
import i18n from '@/plugins/i18n-dynamic';

const newList = () => ({
  name: '',
  tags: [],
  organisationShortId: null,
  watchedCompanies: [],
  watchlistType: 'BASIC',
  eventTypes: [],
  watchlistContent: 'COMPANY'
});

const state = () => ({
  loading: false,
  lists: [],
  list: newList(),
  companies: [],
  companiesTotal: 0,
  companiesCurrentPage: 1,
  companiesPerPage: 10
});

const actions = {
  async loadLists(organisationShortId) {
    const data = await Api.lists.byOrganisation(organisationShortId);
    this.lists = data;
  },

  async loadList(listShortId) {
    const data = await Api.lists.byId(listShortId);
    this.list = data;
  },

  clearList() {
    this.list = newList();
    this.companies = [];
    this.companiesTotal = 0;
    this.companiesCurrentPage = 1;
  },

  async createList(list) {
    this.loading = true;
    const data = await Api.lists.create(list);

    if (data.isError) {
      notifyError(null, i18n.global.t('listCreateError'));
    } else {
      this.list = data;
      this.companies = [];
      this.companiesTotal = 0;
      this.companiesCurrentPage = 1;
    }

    this.loading = false;
  },

  async loadCompanies({ page, rowsPerPage }) {
    const watchedCompanies = this.list.watchedCompanies.filter(Boolean);

    if (watchedCompanies.length === 0) {
      this.companies = [];
      this.companiesTotal = 0;
      this.companiesCurrentPage = 1;
      return;
    }

    this.loading = true;

    const max = rowsPerPage || this.companiesPerPage;
    const data = await Api.companies.search(
      {
        identifiers: watchedCompanies,
        max,
        page
      },
      true
    );

    if (data.isError) {
      notifyError(null, i18n.global.t('genericError'));
    } else {
      this.companies = data.companies;
      this.companiesTotal = data.total;
      this.companiesCurrentPage = page;
      this.companiesPerPage = max;
    }

    this.loading = false;
  },

  async addCompany({ organisationShortId, companyIdentifier }) {
    this.loading = true;
    const data = await Api.watchlists.addToWatchlist({
      watchlistShortId: this.list.id,
      organisationShortId,
      companyIdentifier
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorAddToList'));
    } else {
      this.list = data;
    }

    this.loading = false;
  },

  async removeCompany({ companyIdentifier, organisationShortId }) {
    this.loading = true;
    const data = await Api.watchlists.removeFromWatchlist({
      watchlistShortId: this.list.id,
      organisationShortId,
      companyIdentifier
    });

    if (data.isError) {
      notifyError(null, i18n.global.t('errorremoveFromList'));
    } else {
      this.list = data;
    }

    this.loading = false;
  },

  async updateList(list) {
    this.loading = true;
    const data = await Api.lists.update(list);

    if (data.isError) {
      notifyError(null, i18n.global.t('errorUpdateList'));
    } else {
      this.list = data;
      notify({ message: i18n.global.t('listSavedSuccess'), type: 'positive' });
    }

    this.loading = false;
  },

  async removeSelectedLists(listShortIds) {
    this.loading = true;
    for (const listShortId of listShortIds) {
      const data = await Api.lists.remove(listShortId);

      if (data.isError) {
        notifyError(null, i18n.global.t('errorRemoveList'));
      } else {
        notify({
          message: i18n.global.t('listRemovedSuccess'),
          type: 'positive'
        });
      }
    }
    this.loading = false;
  }
};

export const ListsStore = defineStore('lists', {
  state,
  actions
});
