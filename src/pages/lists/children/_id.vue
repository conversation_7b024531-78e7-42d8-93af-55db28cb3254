<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { AuthStore } from '@/pages/auth/store';
import { ListsStore } from '@/pages/lists/store';
import {
  types,
  levelsToTypes,
  typesToLevels
} from '@/pages/monitoring/utils/alert-levels';
import { SearchCompaniesStore } from '@/store/search-companies';
import Breadcrumb from '@/components/commons/breadcrumb.vue';
import CompaniesSearch from '@/components/commons/companies-search.vue';
import SearchCompaniesTable from '@/components/commons/search-companies-table.vue';
import companiesColumns from '@/pages/lists/data/companies-columns';
import { noEmptyRules } from '@/utils/policies';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const authStore = AuthStore();

const listStore = ListsStore();
const { loading, list, companies } = storeToRefs(listStore);

const searchCompaniesStore = SearchCompaniesStore();
const { loading: searchLoading, autocompleteCompaniesResults } =
  storeToRefs(searchCompaniesStore);

const organisation = ref(authStore.getUserOrganisation);

const breadcrumbLinks = computed(() => [
  { label: t('lists'), path: '/lists' },
  { label: list.value.name || t('newList') }
]);
const isNew = ref(route.params?.id === 'new');

const alertLevels = types.map((alertLevel) => ({
  label: t(alertLevel),
  value: alertLevel,
  caption: t(alertLevel)
}));

watch(
  () => authStore.getUserOrganisation,
  (newOrga) => (organisation.value = newOrga)
);

function onAlertLevelsChange(alertLevels) {
  list.value.alertLevels = alertLevels;
  list.value.eventTypes = levelsToTypes(alertLevels);
}

const listAlertLevels = computed(() => {
  if (!list.value) return [];

  return typesToLevels(list.value.eventTypes);
});

onMounted(async () => {
  if (isNew.value) {
    listStore.clearList();
    searchCompaniesStore.clearSearch();
    return;
  }

  await listStore.loadList(route.params?.id);
  listStore.loadCompanies({ page: 1 });
});

function onSearch(input) {
  searchCompaniesStore.autocompleteCompanies(input, {
    includeInactive: false,
    includeEstablishments: false,
    includeNaturalPersons: false
  });
}

const selection = computed(() => list.watchedCompanies);

const listForm = ref(null);
const tag = ref('');

function onAddTag() {
  const cleanTag = String(tag.value).trim();

  if (!cleanTag.length || list.value.tags.includes(cleanTag)) return;

  list.value.tags.push(cleanTag);
  tag.value = '';
}

function onRemoveTag(tagEntry) {
  list.value.tags = list.value.tags.filter((tag) => tag !== tagEntry);
}

async function onAddCompany(companyIdentifier) {
  await listStore.addCompany({
    organisationShortId: organisation.value,
    companyIdentifier
  });
  listStore.loadCompanies({ page: 1 });
}

async function onRemoveCompany(companyIdentifier) {
  await listStore.removeCompany({
    organisationShortId: organisation.value,
    companyIdentifier
  });
  listStore.loadCompanies({ page: 1 });
}

function save() {
  listForm.value.validate().then(async (success) => {
    if (!success) return;

    const method = isNew.value ? listStore.createList : listStore.updateList;

    await method({
      ...list.value,
      organisationShortId: organisation.value
    });

    if (isNew.value) router.push('/lists/');
  });
}
</script>

<template>
  <Breadcrumb :links="breadcrumbLinks" class="list-breadcrumb" />

  <div class="full-width flex items-center justify-between q-py-md">
    <div class="text-subtitle2">{{ $t('listChildIntro') }}</div>
    <q-btn
      :loading="loading"
      :label="$t('save')"
      color="primary"
      @click="save"
    />
  </div>

  <div class="row q-col-gutter-lg">
    <div class="col-12 col-md-4">
      <q-card flat bordered>
        <!-- LIST NAME -->
        <q-card-section>
          <p class="text-subtitle2">
            {{ $t('listNameHelp') }}
          </p>
          <q-form ref="listForm" @submit="save">
            <q-input
              v-model="list.name"
              :loading="loading"
              :label="`${$t('listName')}*`"
              :rules="[noEmptyRules]"
              lazy-rules
              outlined
            />
          </q-form>
        </q-card-section>

        <!-- TAGS -->
        <q-card-section>
          <q-form @submit.prevent.stop="onAddTag">
            <p class="text-subtitle2">
              {{ $t('listTagsHelp') }}
            </p>
            <q-input
              v-model="tag"
              :label="$t('tags')"
              outlined
              @keydown.enter.prevent="onAddTag"
            >
              <template v-slot:append>
                <q-btn
                  :label="$t('add')"
                  :disabled="!tag"
                  color="primary"
                  type="submit"
                  size="sm"
                  push
                  @click="onAddTag"
                />
              </template>

<style scoped>
.list-breadcrumb {
  flex-shrink: 0;
  flex-grow: 1;
}
</style>
