<script setup>
import authContainer from '@/components/layouts/unauthenticated.vue';
import { useHeadSafe } from '@vueuse/head';

useHeadSafe({ title: 'Page not found' });
</script>

<template>
  <authContainer>
    <div class="page-not-found">
      <h1>404 Page Not Found</h1>
      <p>This page could not be found.</p>
    </div>
  </authContainer>
</template>

<style scoped>
.page-not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--black);
  gap: 1rem;
}
</style>
