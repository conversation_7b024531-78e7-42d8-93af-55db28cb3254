<script setup>
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useHeadSafe } from '@vueuse/head';
import { PERSON_SEARCH } from '@/utils/features';
import { AuthStore } from '@/pages/auth/store';
import { SearchStore } from '@/pages/search/store';
import { PersonStore } from '@/pages/persons/store';
import SearchPersons from '@/pages/search/children/search-persons.vue';
import PreviouslySeenPerson from '@/pages/persons/components/previously-seen-person.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

const { t } = useI18n();

useHeadSafe({ title: t('person') });

const authStore = AuthStore();
const { features, user } = storeToRefs(authStore);
const isAllowedForSearchPersons = computed(() =>
  features.value.includes(PERSON_SEARCH)
);

const personStore = PersonStore();
const { previouslySeenPersons } = storeToRefs(personStore);

const searchStore = SearchStore();
const { loading, max, list } = storeToRefs(searchStore);

const searchInput = ref('');

function search() {
  searchStore.prepareSearchPerson();
  searchStore.searchPersons({
    q: searchInput.value,
    page: 1,
    max: max.value
  });
}

watch(
  () => user.value?.customerShortId,
  (customerShortId) =>
    customerShortId && personStore.loadpreviouslySeenPersons(customerShortId)
);
</script>

<template>
  <div
    :class="['persons-header', { hasResults: searchInput && list.length > 0 }]"
  >
    <div class="persons-search shadow-10">
      <q-input
        v-model="searchInput"
        :loading="loading"
        :label="$t('searchForPersons')"
        :disable="!isAllowedForSearchPersons"
        :debounce="300"
        borderless
        clearable
        class="persons-search-field"
        @update:model-value="search"
        @clear="searchStore.clearSearchPersons"
      >
        <template v-slot:before>
          <q-icon
            color="primary"
            name="fa-regular fa-users"
            size="xs"
            class="q-mr-sm"
          />
        </template>
        <template v-slot:after>
          <q-btn
            :disable="!isAllowedForSearchPersons || loading"
            round
            flat
            size="md"
            color="primary"
            @click="search"
          >
            <q-icon color="primary" name="fa-regular fa-search" size="xs" />
          </q-btn>
        </template>
      </q-input>
    </div>
  </div>

  <div
    class="result-container teasing-container"
    v-show="!isAllowedForSearchPersons"
  >
    <FeatureTeasing :feature="PERSON_SEARCH" />
  </div>

  <div
    v-show="searchInput && isAllowedForSearchPersons"
    class="result-container"
  >
    <SearchPersons :search-input="searchInput" />
  </div>

  <div
    v-show="!searchInput && isAllowedForSearchPersons"
    class="previous-seen-persons"
  >
    <PreviouslySeenPerson
      v-show="previouslySeenPersons.length"
      :persons="previouslySeenPersons"
      class="previous-seen-persons-list"
      @clear="personStore.clearpreviouslySeenPersons(user.customerShortId)"
    />
  </div>
</template>

<style scoped>
.persons-header {
  position: relative;
  background: url(/company-bg.jpg),
    radial-gradient(circle at 25%, var(--primary), var(--primaryDark));
  background-size: cover;
  background-blend-mode: multiply;
  height: 20rem;
  margin-top: -24px;
  transition: height 300ms ease-in-out;
}

.persons-header.hasResults {
  height: 5rem;
}

.persons-search {
  position: absolute;
  left: 1rem;
  right: 1rem;
  bottom: 0;
  transform: translateY(50%);
  background: var(--white);
  border: 1px solid var(--border);
  padding: 1rem;
  z-index: 1;
  border-radius: 0.5rem;
}

@media (min-width: 1440px) {
  .persons-search {
    left: 3rem;
    right: 3rem;
    padding: 1rem 3rem;
  }
}

.persons-search-field {
  display: flex;
  border: 3px solid var(--primary);
  border-radius: 0.75rem;
  padding: 0.25rem 1rem;
}

.result-container {
  padding: 5rem 3rem 1rem;
}

.previous-seen-persons {
  padding: 7rem 1rem 1rem;
  display: flex;
  justify-content: center;
}

@media (min-width: 1440px) {
  .previous-seen-persons {
    padding: 7rem 3rem 1rem;
  }
}

.previous-seen-persons-list {
  max-width: 33%;
}

.teasing-container {
  position: relative;
  min-height: 400px;
}
</style>
