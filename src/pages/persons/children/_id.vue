<script setup>
import { computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useHeadSafe } from '@vueuse/head';
import { storeToRefs } from 'pinia';
import { SearchStore } from '@/pages/search/store';
import { PersonStore } from '@/pages/persons/store';
import { AuthStore } from '@/pages/auth/store';
import downloadCSV from '@/utils/download-csv';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const authStore = AuthStore();
const { user } = storeToRefs(authStore);

const searchStore = SearchStore();
const { loading, list } = storeToRefs(searchStore);

const personStore = PersonStore();
const { loading: companiesLoading, personCompanies } = storeToRefs(personStore);
const person = computed(() => list.value[0]);

useHeadSafe({ title: () => [person.value?.fullName, t('person')].join(' - ') });

const companiesCount = computed(
  () => route.query?.companies?.split(',').length || 0
);

async function init() {
  if (!route.params?.id) return;

  const companies = route.query?.companies?.split(',') || [];

  await searchStore.searchPersons({
    q: route.params.id,
    max: 1,
    includeInactive: true,
    identifiers: companies,
    extended: true
  });

  personStore.loadPersonCompanies(companies, {
    includeInactive: true,
    withAvatar: true
  });
}

onMounted(() => init());

watch(
  () => route.params.id,
  () => init()
);

watch(
  () => [user.value?.customerShortId, person.value],
  ([customerShortId, watchPerson]) => {
    if (!customerShortId || !watchPerson) return;

    const companies = route.query?.companies?.split(',') || [];

    personStore.addPreviouslySeenPerson({
      person: {
        id: watchPerson?.id,
        fullName: watchPerson?.fullName,
        title: watchPerson?.title,
        color: watchPerson?.color,
        initials: watchPerson?.initials,
        companiesIdentifiers: companies
      },
      customerShortId
    });
  }
);

const contacts = computed(() => [
  {
    id: 'website',
    label: t('website'),
    icon: 'fa-regular fa-laptop',
    value: person.value?.website || '—',
    formatted: person.value?.website
      ? person.value?.website.replace(/(^\w+:|^)\/\//, '')
      : null
  },
  {
    id: 'email',
    label: t('email'),
    icon: 'fa-regular fa-envelope',
    value: person.value?.email || '—'
  },
  {
    id: 'mobile',
    label: t('mobile'),
    icon: 'fa-regular fa-mobile',
    value: person.value?.mobile || '—'
  },
  {
    id: 'phone',
    label: t('fax'),
    icon: 'fa-regular fa-fax',
    value: person.value?.phone || '—'
  }
]);

const genderIcon = computed(() => {
  if (!person.value?.gender) return null;

  return person.value.gender === 'm'
    ? 'fa-regular fa-mars'
    : 'fa-regular fa-venus';
});

function download() {
  downloadCSV(person.value);
}

function goBack() {
  router.go(-1);
}
</script>

<template>
  <q-linear-progress
    v-if="loading"
    indeterminate
    color="warning"
    class="q-mt-sm"
  />

  <div v-if="!loading && !person" class="text-center q-py-xl">
    <p class="text-h6">
      <q-icon name="fa-regular fa-robot" color="primary" class="q-mr-md" />
      {{ $t('personNotFound') }}
    </p>
    <div class="text-center">
      <q-btn
        unelevated
        color="primary"
        icon="fa-regular fa-arrow-left"
        :label="$t('back')"
        @click="goBack"
      />
    </div>
  </div>

  <section v-if="person">
    <q-card :loading="loading" bordered flat class="q-mb-md">
      <div class="flex items-center justify-between q-pa-md">
        <q-avatar
          size="lg"
          class="text-white q-mr-md"
          :style="{ backgroundColor: person.color || 'var(--primary)' }"
        >
          {{ person.initials }}
        </q-avatar>

        <div>
          <h1 class="text-h6 q-ma-none">
            <span v-show="person.title" class="text-capitalize">
              {{ person.title }}.
            </span>
            {{ person.fullName }}

            <q-icon
              v-show="person.gender"
              :name="genderIcon"
              color="grey"
              size="xs"
            />
          </h1>

          <div>{{ $t('profession') }}: {{ person.profession || '-' }}</div>
          <div>{{ $t('birthdate') }}: {{ person.birthDate || '-' }}</div>
          <div>
            {{ $t('firstAppearance') }}: {{ person.firstAppearance || '-' }}
          </div>
          <div>
            {{ $t('lastAppearance') }}: {{ person.lastAppearance || '-' }}
          </div>
        </div>

        <q-space class="col-grow" />

        <q-btn
          unelevated
          color="primary"
          icon="fa-regular fa-file-csv"
          :label="$t('exportToCSV')"
          @click="download"
        />
      </div>
    </q-card>

    <div class="row q-col-gutter-md justify-center q-pb-lg">
      <div class="col-6">
        <q-card bordered flat>
          <h2 class="text-subtitle1 text-bold q-px-md q-mb-none">
            {{ $t('contact') }}
          </h2>
          <q-card-section>
            <q-item
              v-for="contact in contacts"
              :key="contact.icon"
              class="contact-item"
            >
              <q-item-section avatar class="contact-icon">
                <q-icon :name="contact.icon" color="primary" size="xs" />
              </q-item-section>
              <q-item-section class="contact-info">
                <q-item-label class="text-bold">
                  {{ contact.label }}
                </q-item-label>

                <q-item-label
                  v-if="contact.id === 'website'"
                  class="contact-value"
                >
                  <a
                    v-if="contact.formatted"
                    :href="contact.value"
                    target="_blank"
                  >
                    {{ contact.formatted }}
                  </a>
                  <span v-else>{{ contact.value }}</span>
                </q-item-label>

                <q-item-label v-else class="contact-value">
                  {{ contact.value }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-6">
        <!-- ADDRESSES -->
        <q-card bordered flat class="q-mb-md">
          <h2 class="text-subtitle1 text-bold q-px-md q-mb-none">
            {{ $t('addresses') }}
            ({{ person.addresses?.length }})
          </h2>
          <q-card-section>
            <q-item
              v-for="address in person?.addresses"
              :key="address.id"
              :href="`https://www.google.com/maps/search/?api=1&query=${address.encodedAddress}`"
              target="_blank"
            >
              <q-item-section avatar>
                <q-icon
                  name="fa-regular fa-map-location-dot"
                  color="primary"
                  size="sm"
                />
              </q-item-section>
              <q-item-section>
                <div>
                  <span :class="{ 'text-bold': address.primary }">
                    {{ address.formatted_address }}
                  </span>
                  <q-chip v-show="address.primary" color="warning" size="xs">
                    {{ $t('mainAddress') }}
                  </q-chip>
                </div>
              </q-item-section>
            </q-item>
          </q-card-section>
        </q-card>

        <!-- COMPANIES -->
        <q-card bordered flat>
          <h2 class="text-subtitle1 text-bold q-px-md q-mb-none">
            {{ $t('companies') }} ({{ personCompanies.length }})
          </h2>
          <q-card-section>
            <template v-if="companiesLoading">
              <q-item v-for="i in companiesCount" :key="i" dense>
                <q-item-section avatar>
                  <q-skeleton type="QAvatar" size="2rem" />
                </q-item-section>
                <q-item-section>
                  <q-skeleton type="rect" />
                </q-item-section>
              </q-item>
            </template>

<style scoped>
.contact-icon {
  min-width: 0;
}

.contact-item {
  width: 50%;
}

.contact-info {
  font-size: var(--small);
}

.contact-value {
  word-break: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
