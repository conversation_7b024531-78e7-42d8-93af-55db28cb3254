import { defineStore } from 'pinia';
import Api from '@/api';
import { notifyError } from '@/utils/notifications';
import { uniqBy } from 'lodash';

const state = () => ({
  loading: false,
  person: null,
  personCompanies: [],
  previouslySeenPersons: []
});

const actions = {
  loadpreviouslySeenPersons(customerShortId) {
    if (!customerShortId) return;

    const viewed = localStorage.getItem('previously-seen-persons') || '[]';
    const parsed = JSON.parse(viewed);

    this.previouslySeenPersons = parsed.filter(
      (person) => person.customerShortId === customerShortId
    );
  },

  addPreviouslySeenPerson({ person, customerShortId }) {
    if (!customerShortId) return;

    this.loadpreviouslySeenPersons(customerShortId);

    const index = this.previouslySeenPersons.findIndex(
      (previousPerson) => previousPerson.id === person.id
    );

    if (index !== -1) {
      return;
    }

    this.previouslySeenPersons = uniqBy(
      [{ ...person, customerShortId }, ...this.previouslySeenPersons],
      'id'
    ).slice(0, 10);

    localStorage.setItem(
      'previously-seen-persons',
      JSON.stringify(this.previouslySeenPersons)
    );
  },

  clearpreviouslySeenPersons(customerShortId) {
    this.loadpreviouslySeenPersons(customerShortId);
    this.previouslySeenPersons = this.previouslySeenPersons.filter(
      (person) => person.customerShortId !== customerShortId
    );
    localStorage.setItem(
      'previously-seen-persons',
      JSON.stringify(this.previouslySeenPersons)
    );
  },

  async loadPersonCompanies(ids = [], options) {
    if (!Array.isArray(ids) || !ids.length) {
      this.personCompanies = [];
      return;
    }

    this.loading = true;

    const companies = await Api.companies.searchByIdentifers(ids, options);

    if (!companies || companies.isError) {
      notifyError(companies);
      this.personCompanies = [];
    } else {
      this.personCompanies = companies;
    }

    this.loading = false;
  }
};

export const PersonStore = defineStore('person', {
  state,
  actions
});
