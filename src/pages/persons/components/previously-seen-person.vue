<template>
  <div>
    <div class="text-h6 q-pb-sm">
      {{ $t('previouslySeenPersons') }}
      ({{ persons.length }})
    </div>
    <q-card>
      <q-list separator>
        <q-item
          v-for="person in persons"
          :key="person.id"
          :to="`/persons/${person.fullName}?companies=${person.companiesIdentifiers}`"
        >
          <q-item-section avatar>
            <q-avatar
              size="32px"
              :style="{ color: 'var(--white)', background: person.color }"
            >
              {{ person.initials }}
            </q-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label>
              <span v-show="person.title" class="text-uppercase">
                {{ person.title }}.
              </span>
              {{ person.fullName }}
            </q-item-label>
          </q-item-section>
          <q-item-section avatar>
            <q-icon name="fa-regular fa-chevron-right" size="xs" color="grey" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>

    <q-btn
      v-show="persons.length"
      :label="$t('clearAllpreviouslySeenPersons')"
      icon="fa-regular fa-close"
      class="q-mt-md full-width"
      color="grey"
      size="sm"
      no-caps
      flat
      @click="$emit('clear')"
    />
  </div>
</template>

<script setup>
defineProps({
  persons: Array
});
</script>
