<template>
  <q-table
    v-model:pagination="pagination"
    :loading="loading"
    :rows="rows"
    :columns="searchColumns"
    :rows-per-page-options="[10, 50]"
    :no-data-label="$t('searchPersonsNoData')"
    row-key="id"
    wrap-cells
    @request="paginate"
  >
    <template #body-cell-icon="props">
      <q-td :props="props">
        <q-avatar
          size="sm"
          class="text-white"
          :style="{ backgroundColor: props.row.color || 'var(--primary)' }"
        >
          {{ props.row.initials }}
        </q-avatar>
      </q-td>
    </template>
    <template #body-cell-name="props">
      <q-td :props="props">
        <router-link
          :to="`/persons/${props.row.fullName}?companies=${props.row.companiesIdentifiers}`"
        >
          {{ props.row.fullName }}
          <span v-if="props.row.title" class="text-uppercase">
            ({{ props.row.title }}.)
          </span>
        </router-link>
      </q-td>
    </template>
    <template #body-cell-birthDate="props">
      <q-td :props="props">
        <div v-if="props.row.birthDate || props.row.age">
          {{ $t('agePlural', props.row.age) }}
          ({{ props.row.birthDate }})
        </div>
        <div v-else>-</div>
      </q-td>
    </template>
    <template #body-cell-companiesCount="props">
      <q-td :props="props">
        {{ $t('companiesPlural', props.row.companiesCount) }}
      </q-td>
    </template>
    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn
          :to="`/persons/${props.row.fullName}?companies=${props.row.companiesIdentifiers}`"
          icon="fa-regular fa-chevron-right"
          round
          flat
          color="primary"
        />
      </q-td>
    </template>
  </q-table>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import searchColumns from '@/pages/search/data/persons-columns';

const props = defineProps({
  loading: Boolean,
  rows: Array,
  total: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['init', 'paginate']);

const pagination = ref({
  sortBy: 'name',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: props.total
});

onMounted(() => emit('init'));

watch(
  () => props.total,
  (newTotal) => {
    pagination.value.rowsNumber = newTotal;
  }
);

function paginate(params) {
  pagination.value.page = params.pagination.page;
  pagination.value.rowsPerPage = params.pagination.rowsPerPage;
  pagination.value.rowsNumber = props.total;
  emit('paginate', params.pagination);
}
</script>
