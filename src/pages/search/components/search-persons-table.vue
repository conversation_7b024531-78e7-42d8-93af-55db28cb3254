<script setup>
import { ref, onMounted, watch } from 'vue';
import searchColumns from '@/pages/search/data/persons-columns';

const props = defineProps({
  loading: Boolean,
  rows: Array,
  total: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['init', 'paginate']);

const pagination = ref({
  sortBy: 'name',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: props.total
});

onMounted(() => emit('init'));

watch(
  () => props.total,
  (newTotal) => {
    pagination.value.rowsNumber = newTotal;
  }
);

function paginate(params) {
  pagination.value.page = params.pagination.page;
  pagination.value.rowsPerPage = params.pagination.rowsPerPage;
  pagination.value.rowsNumber = props.total;
  emit('paginate', params.pagination);
}
</script>

<template>
  <q-table
    v-model:pagination="pagination"
    :loading="loading"
    :rows="rows"
    :columns="searchColumns"
    :rows-per-page-options="[10, 50]"
    :no-data-label="$t('searchPersonsNoData')"
    row-key="id"
    wrap-cells
    @request="paginate"
  >
    <template #body-cell-icon="props">
      <q-td :props="props">
        <q-avatar
          size="sm"
          class="text-white"
          :style="{ backgroundColor: props.row.color || 'var(--primary)' }"
        >
          {{ props.row.initials }}
        </q-avatar>
      </q-td>
    </template>
