<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { COMPANY_SEARCH_ADVANCED_FILTERS } from '@/utils/features';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

const props = defineProps({
  total: Number,
  aggregations: {
    type: Object,
    default: () => ({ startDates: [] })
  },
  teasing: Boolean
});

const emit = defineEmits(['apply-options', 'apply-filters']);

const { t } = useI18n();

const selectedStartDates = ref([]);
const selectedLegalPersonTypes = ref([]);
const selectedPostalCodes = ref([]);
const selectedJuridicalForms = ref([]);

const selectedOptions = ref({
  has_phone: false,
  has_email: false,
  has_website: false
});
const contentOptions = [
  {
    label: t('hasPhone'),
    key: 'has_phone'
  },
  {
    label: t('hasEmail'),
    key: 'has_email'
  },
  {
    label: t('hasWebsite'),
    key: 'has_website'
  }
];

function applyRangeFilters(values) {
  if (props.teasing) return;
  emit('apply-filters', { age_range: values });
}

function applyTermsFilters(context, value) {
  if (props.teasing) return;
  emit('apply-filters', { [context]: value });
}

function updateOptions() {
  if (props.teasing) return;
  emit('apply-options', selectedOptions.value);
}

function onResetFilters() {
  if (props.teasing) return;

  selectedOptions.value.has_phone = false;
  selectedOptions.value.has_email = false;
  selectedOptions.value.has_website = false;
  selectedStartDates.value = [];
  selectedLegalPersonTypes.value = [];
  selectedPostalCodes.value = [];
  selectedJuridicalForms.value = [];

  emit('apply-options', selectedOptions.value);
  emit('apply-filters', {});
}
</script>

<template>
  <q-card>
    <q-card-section>
      <div class="flex item-center justify-between">
        <div class="text-h6 text-capitalize">
          {{ $t('filters') }}
        </div>

        <q-btn
          :label="$t('resetFilters')"
          flat
          size="sm"
          color="primary"
          @click="onResetFilters"
        />
      </div>
    </q-card-section>

    <q-separator />

    <div class="search-filters-container">
      <FeatureTeasing
        v-if="teasing"
        :feature="COMPANY_SEARCH_ADVANCED_FILTERS"
        condenced
        overlay
      />

      <div :class="{ isTeasing: teasing }">
        <!-- OPTIONS -->
        <q-card-section>
          <div
            v-for="option in contentOptions"
            :key="option.key"
            class="q-py-xs"
          >
            <q-checkbox
              v-model="selectedOptions[option.key]"
              :label="option.label"
              class="text-caption"
              name="options"
              dense
              @update:model-value="updateOptions"
            />
          </div>
        </q-card-section>
        <!-- START DATE -->
        <q-card-section>
          <div class="text-subtitle2 q-pb-sm">{{ $t('startDateInYears') }}</div>
          <q-option-group
            v-model="selectedStartDates"
            :options="aggregations.startDates"
            type="checkbox"
            name="startDates"
            dense
            @update:model-value="applyRangeFilters"
          >
            <template #label="option">
              <q-item dense>
                <q-item-section class="text-caption">
                  {{ option.label }}
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="sm" color="grey-4" text-color="black">
                    {{ option.count }}
                  </q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-option-group>
        </q-card-section>
        <!-- LEGAL PERSON TYPE -->
        <q-card-section>
          <div class="text-subtitle2 q-pb-sm">{{ $t('entityType') }}</div>
          <q-option-group
            v-model="selectedLegalPersonTypes"
            :options="aggregations.legalPersonTypes"
            type="checkbox"
            name="legalPersonTypes"
            dense
            @update:model-value="
              (values) => applyTermsFilters('legal_person_type', values)
            "
          >
            <template #label="option">
              <q-item dense>
                <q-item-section class="text-caption">
                  {{ option.label }}
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="sm" color="grey-4" text-color="black">
                    {{ option.count }}
                  </q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-option-group>
        </q-card-section>
        <!-- POSTAL CODES -->
        <q-card-section>
          <div class="text-subtitle2 q-pb-sm">{{ $t('postalCode') }}</div>
          <q-select
            v-model="selectedPostalCodes"
            :options="aggregations.postalCodes"
            option-value="value"
            option-label="label"
            emit-value
            multiple
            outlined
            dense
            clearable
            @update:model-value="
              (values) => applyTermsFilters('postal_code', values)
            "
          >
            <template
              v-slot:option="{ itemProps, opt, selected, toggleOption }"
            >
              <q-item v-bind="itemProps" dense>
                <q-item-section avatar>
                  <q-checkbox
                    :model-value="selected"
                    :name="opt.icon"
                    @click="toggleOption(opt)"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label caption>{{ opt.label }}</q-item-label>
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="xs" color="grey-4" text-color="black">
                    {{ opt.count }}
                  </q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-card-section>
        <!-- JURIDICAL FORMS -->
        <q-card-section>
          <div class="text-subtitle2 q-pb-sm">{{ $t('juridicalForm') }}</div>
          <q-select
            v-model="selectedJuridicalForms"
            :options="aggregations.juridicalForms"
            option-value="value"
            option-label="label"
            emit-value
            multiple
            outlined
            dense
            clearable
            @update:model-value="
              (values) => applyTermsFilters('juridical_form', values)
            "
          >
            <template v-slot:selected-item="scope">
              {{ aggregations.juridicalForms[scope.index].label }}
            </template>
            <template
              v-slot:option="{ itemProps, opt, selected, toggleOption }"
            >
              <q-item v-bind="itemProps" dense>
                <q-item-section avatar>
                  <q-checkbox
                    :model-value="selected"
                    :name="opt.icon"
                    @click="toggleOption(opt)"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label caption>{{ opt.label || '-' }}</q-item-label>
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="xs" color="grey-4" text-color="black">
                    {{ opt.count }}
                  </q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-card-section>
      </div>
    </div>
  </q-card>
</template>

<style scoped>
.search-filters-container {
  position: relative;
}

.isTeasing {
  filter: blur(3px);
}
</style>
