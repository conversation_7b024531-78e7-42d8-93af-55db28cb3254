<script setup>
import { ref, onMounted, watch } from 'vue';
import searchColumns from '@/pages/search/data/companies-columns';

const props = defineProps({
  loading: Boolean,
  rows: Array,
  total: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['paginate']);

const pagination = ref({
  sortBy: 'company_name',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: props.total
});

onMounted(() => emit('init'));

watch(
  () => props.total,
  (newTotal) => {
    pagination.value.rowsNumber = newTotal;
  }
);

function paginate(params) {
  pagination.value.page = params.pagination.page;
  pagination.value.rowsPerPage = params.pagination.rowsPerPage;
  pagination.value.rowsNumber = props.total;
  pagination.value.sortBy = params.pagination.sortBy;
  pagination.value.descending = params.pagination.descending;

  emit('paginate', params.pagination);
}
</script>

<template>
  <q-table
    v-model:pagination="pagination"
    :loading="loading"
    :rows="rows"
    :columns="searchColumns"
    :rows-per-page-options="[10, 50]"
    row-key="id"
    wrap-cells
    @request="paginate"
  >
    <template #body-cell-icon="props">
      <q-td :props="props">
        <q-avatar
          size="sm"
          class="text-white"
          :style="{ backgroundColor: props.row.color || 'var(--primary)' }"
        >
          {{ props.row.initials }}
        </q-avatar>
      </q-td>
    </template>
    <template #body-cell-company_name="props">
      <q-td :props="props">
        <router-link :to="`/companies/${props.row.id}`">
          {{ props.row.name }}
        </router-link>
        <q-chip size="sm">
          {{ props.row.isActive ? $t('active') : $t('inactive') }}
        </q-chip>
      </q-td>
    </template>
  </q-table>
</template>
