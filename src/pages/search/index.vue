<script setup>
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';
import { SearchCompaniesStore } from '@/store/search-companies';
import { SearchStore } from '@/pages/search/store';
import { AuthStore } from '@/pages/auth/store';
import { PublicationsStore } from '@/pages/publications/store';
import { PERSON_SEARCH, FULL_TEXT_SEARCH } from '@/utils/features';

const route = useRoute();
const router = useRouter();
const searchCompaniesStore = SearchCompaniesStore();
const searchStore = SearchStore();
const publicationsStore = PublicationsStore();
const authStore = AuthStore();

const { loading: companiesLoading, max: companiesMax } =
  storeToRefs(searchCompaniesStore);
const { loading: personsLoading, max: personsMax } = storeToRefs(searchStore);
const { features } = storeToRefs(authStore);

const isAllowToUsePersons = computed(() =>
  features.value.includes(PERSON_SEARCH)
);
const isAllowToUsePublications = computed(() =>
  features.value.includes(FULL_TEXT_SEARCH)
);
const loading = computed(() => companiesLoading.value || personsLoading.value);

const searchInput = ref('');
const activeTab = ref('companies');

searchInput.value = route.query.q;

async function search() {
  if (activeTab.value === 'companies') {
    await searchCompaniesStore.searchCompanies({
      q: searchInput.value,
      page: 1,
      max: companiesMax.value,
      complete: true,
      withAggs: true
    });
  }

  if (activeTab.value === 'persons') {
    searchStore.prepareSearchPerson();
    await searchStore.searchPersons({
      q: searchInput.value,
      page: 1,
      max: personsMax.value
    });
  }

  if (activeTab.value === 'publications') {
    await publicationsStore.search({ q: searchInput.value });
    await publicationsStore.aggregations({ q: searchInput.value });
  }

  router.replace({ query: { q: searchInput.value } });
}

function clear() {
  searchInput.value = '';
}

function onChangeTab(newTab) {
  if (!newTab) return;
  activeTab.value = newTab;
  search();
}
</script>

<template>
  <section>
    <q-card class="q-mb-md">
      <q-card-section class="row items-center justify-between">
        <h1 class="text-h5 q-ma-none">{{ $t('search') }}</h1>
        <q-input
          v-model="searchInput"
          :loading="loading"
          :disable="loading"
          :placeholder="$t('typeToSearchCompanies')"
          class="col-grow q-mx-lg"
          outlined
          rounded
          dense
          clearable
          @keyup.enter="search"
          @clear="clear"
        >
          <template #prepend>
            <q-icon name="fa-regular fa-search" size="xs" class="q-pr-xs" />
          </template>

<style>
.search-tabs-bar {
  background-color: var(--primarySmoke);
}
</style>
