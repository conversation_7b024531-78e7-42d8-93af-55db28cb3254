<template>
  <div class="flex items-center justify-between q-pb-md">
    <div class="text-h6">
      {{ $t('resultsFor', { total, input: searchInput }) }}
    </div>

    <q-space />

    <!-- ACTIVE/INACTIVE -->
    <q-checkbox
      v-model="includeInactive"
      :label="$t('hideInactives')"
      class="q-mr-md"
      name="hideInactives"
      dense
      @update:model-value="onToggleInactives"
    />
  </div>

  <SearchPersonsTable
    :loading="loading"
    :rows="list"
    :total="total"
    :page="currentPage"
    @init="onInitialSearch"
    @paginate="onPaginate"
  />
</template>

<script setup>
import { ref } from 'vue';
import { SearchStore } from '@/pages/search/store';
import { storeToRefs } from 'pinia';
import SearchPersonsTable from '@/pages/search/components/search-persons-table.vue';

const props = defineProps({
  searchInput: String
});

const searchStore = SearchStore();
const { loading, total, list } = storeToRefs(searchStore);
const max = ref(10);
const currentPage = ref(1);
const includeInactive = ref(false);

function search(params) {
  searchStore.searchPersons({
    q: props.searchInput,
    max: max.value,
    params
  });
}

function onToggleInactives() {
  searchStore.setFilter({
    key: 'include_inactive',
    value: includeInactive.value
  });

  search({ page: 1 });
}

function onPaginate(pagination) {
  max.value = pagination.rowsPerPage;
  currentPage.value = pagination.page;
  search({ page: pagination.page });
}

function onInitialSearch() {
  search({ page: 1 });
}
</script>
