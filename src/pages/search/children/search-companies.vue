<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { SearchCompaniesStore } from '@/store/search-companies';
import { AuthStore } from '@/pages/auth/store';
import { COMPANY_SEARCH_ADVANCED_FILTERS } from '@/utils/features';
import SearchCompaniesTable from '@/pages/search/components/search-companies-table.vue';
import SearchCompaniesFilters from '@/pages/search/components/search-companies-filters.vue';

const props = defineProps({
  searchInput: String,
  teasing: Boolean
});

const { t } = useI18n();

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowToUseFilters = computed(() =>
  features.value.includes(COMPANY_SEARCH_ADVANCED_FILTERS)
);

const selectedSort = ref(null);
const sorts = [
  {
    label: t('startDateAsc'),
    value: 'start_date:asc'
  },
  {
    label: t('startDateDesc'),
    value: 'start_date:desc'
  },
  {
    label: t('companyNameAsc'),
    value: 'company_name:asc'
  },
  {
    label: t('companyNameDesc'),
    value: 'company_name:desc'
  }
];

const max = ref(10);
const currentPage = ref(1);
const isHideInavtives = ref(false);
const searchCompaniesStore = SearchCompaniesStore();
const {
  loading,
  searchCompaniesResults,
  searchCompaniesTotal,
  searchCompaniesAggregations
} = storeToRefs(searchCompaniesStore);

async function search({ page, sort, filters, options }) {
  const params = {
    q: props.searchInput,
    page: page || currentPage.value,
    max: max.value,
    complete: true,
    withAggs: true
  };

  if (sort) {
    params.sort = sort;
  }

  if (filters) {
    params.filters = filters;
  }

  if (options) {
    params.options = options;
  }

  await searchCompaniesStore.searchCompanies(params);
}

function onToggleInactives() {
  searchCompaniesStore.setFilter({
    key: 'includeInactive',
    value: isHideInavtives.value
  });

  search({ page: 1 });
}

function onInitialSearch() {
  if (!props.searchInput) return;

  searchCompaniesStore.searchCompanies({
    q: props.searchInput,
    page: 1,
    max: max.value,
    complete: true,
    withAggs: true
  });
}

function onPaginate(pagination) {
  max.value = pagination.rowsPerPage;
  currentPage.value = pagination.page;

  const params = { page: pagination.page };

  if (pagination.sortBy) {
    const direction = pagination.descending ? 'desc' : 'asc';
    selectedSort.value = `${pagination.sortBy}:${direction}`;

    params.sort = [
      {
        [pagination.sortBy]: direction
      }
    ];
  } else {
    selectedSort.value = null;
  }

  search(params);
}

function onSort(sort) {
  const [key, value] = sort.split(':');

  search({
    page: currentPage.value,
    sort: [
      {
        [key]: value
      }
    ]
  });
}

function onApplyFilters(filters) {
  search({ filters });
}

function onApplyOptions(options) {
  search({ options });
}
</script>

<template>
  <div class="flex items-center justify-between q-pb-md">
    <div class="text-h6">
      {{
        $t('resultsFor', { total: searchCompaniesTotal, input: searchInput })
      }}
    </div>

    <q-space />

    <!-- ACTIVE/INACTIVE -->
    <q-checkbox
      v-model="isHideInavtives"
      :label="$t('hideInactives')"
      class="q-mr-md"
      name="hideInactives"
      dense
      @update:model-value="onToggleInactives"
    />

    <!-- SORTING -->
    <q-select
      v-model="selectedSort"
      :label="$t('sortBy')"
      :options="sorts"
      class="col-grow publication-sorting"
      outlined
      dense
      emit-value
      map-options
      style="max-width: 12rem"
      @update:model-value="onSort"
    />
  </div>

  <div class="row q-col-gutter-lg">
    <div class="col-12 col-md-3">
      <SearchCompaniesFilters
        :loading="loading"
        :aggregations="searchCompaniesAggregations"
        :teasing="!isAllowToUseFilters"
        @apply-options="onApplyOptions"
        @apply-filters="onApplyFilters"
      />
    </div>

    <div class="col">
      <SearchCompaniesTable
        :loading="loading"
        :rows="searchCompaniesResults"
        :total="searchCompaniesTotal"
        :page="currentPage"
        @init="onInitialSearch"
        @paginate="onPaginate"
      />
    </div>
  </div>
</template>
