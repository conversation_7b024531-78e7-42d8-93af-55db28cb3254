<template>
  <div class="flex items-center justify-between q-pb-md">
    <div class="text-h6">
      {{ $t('resultsFor', { total, input: searchInput }) }}
    </div>
    <q-space />
    <PublicationsSort @sort="updateSort" />
  </div>

  <PublicationsTable />
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { PublicationsStore } from '@/pages/publications/store';
import PublicationsTable from '@/pages/publications/components/publications-table.vue';
import PublicationsSort from '@/pages/publications/components/publications-sort.vue';

defineProps({
  searchInput: String
});

const publicationsStore = PublicationsStore();
const { total } = storeToRefs(publicationsStore);

function updateSort(value) {
  publicationsStore.updateSort(value);
}
</script>
