import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    align: 'center',
    name: 'icon'
  },
  {
    label: i18n.global.t('name'),
    name: 'name',
    field: 'full_name',
    align: 'left'
  },
  {
    label: i18n.global.t('age'),
    name: 'birthDate',
    field: 'birthDate',
    align: 'left'
  },
  {
    label: i18n.global.t('companies'),
    name: 'companiesCount',
    field: 'companiesCount',
    align: 'right'
  },
  {
    label: i18n.global.t('address'),
    field: 'address',
    align: 'left'
  },
  {
    label: i18n.global.t('lastAppearance'),
    field: 'lastAppearance',
    align: 'left',
    format: (value) => value || '–'
  },
  {
    name: 'action',
    align: 'right'
  }
];
