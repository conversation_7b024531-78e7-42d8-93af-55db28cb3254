import i18n from '@/plugins/i18n-dynamic';

export default [
  {
    align: 'center',
    name: 'icon'
  },
  {
    label: i18n.global.t('name'),
    field: 'name',
    name: 'company_name',
    align: 'left',
    sortable: true
  },
  {
    label: i18n.global.t('vat'),
    field: 'vatFormatted',
    align: 'left',
    style: 'white-space: nowrap'
  },
  {
    label: i18n.global.t('mainActivity'),
    field: 'mainActivity',
    align: 'left',
    format: (value) => value || '–'
  },
  {
    label: i18n.global.t('juridicalForm'),
    field: 'juridicalForm',
    align: 'left',
    format: (value) => value || '–'
  },
  {
    label: i18n.global.t('age'),
    field: 'age',
    name: 'start_date',
    align: 'left',
    style: 'white-space: nowrap',
    sortable: true
  },
  {
    label: i18n.global.t('updateDate'),
    field: 'updatedAt',
    align: 'left'
  }
];
