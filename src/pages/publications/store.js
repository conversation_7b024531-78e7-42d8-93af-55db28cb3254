import { defineStore } from 'pinia';
import Api from '@/api';
import { notifyError } from '@/utils/notifications';
import i18n from '@/plugins/i18n-dynamic';

const state = () => ({
  loading: false,
  max: 10,
  page: 1,
  total: 0,
  searchInput: null,
  filters: {},
  sort: 'bestMatch',
  aggs: {
    notaries: [],
    auditors: [],
    functions: [],
    postalCodes: [],
    companyIdentifiers: []
  },
  results: []
});

function sortValue(sorting) {
  const sortDict = {
    bestMatch: [],
    publicationDateNewest: [{ publication_date: 'desc' }],
    publicationDateOldest: [{ publication_date: 'asc' }]
  };
  return sortDict[sorting] || sortDict.bestMatch;
}

const actions = {
  abort() {
    Api.publications.abort();
  },

  async search({ page, q, filters, sort, abort }) {
    this.loading = true;

    if (q === null || q === '') {
      this.searchInput = '';
      this.results = [];
      this.total = 0;
      this.page = 1;
      this.loading = false;
      this.filters = {};
      this.sort = [];
      return;
    }

    let params = {
      q,
      page: page || 1,
      max: this.max
    };

    if (filters) {
      params = {
        ...params,
        ...filters
      };
    }

    if (sort) {
      params.sort = sortValue(sort || this.sort);
    }

    const data = await Api.publications.search({ ...params, abort });

    if (data.isError) {
      notifyError(null, i18n.global.t('publicationsError'));
    }

    this.searchInput = q;
    this.page = page;
    this.filters = filters || {};
    this.sort = sort || this.sort;

    if (!data.isError && !data.isCanceled) {
      this.total = data.total;
      this.results = data.results;
    }

    this.loading = false;
  },

  updateSort(sort) {
    this.search({ sort, q: this.searchInput, filters: this.filters });
  },

  async aggregations({ page, q, abort }) {
    this.loading = true;

    if (q === null || q === '') {
      return;
    }

    const params = {
      q,
      page: page || 1,
      max: this.max
    };

    const data = await Api.publications.aggregations({ ...params, abort });

    if (data.isError) {
      notifyError(null, i18n.global.t('publicationsError'));
    }

    if (!data.isError && !data.isCanceled) {
      this.aggs = data;
    }

    this.loading = false;
  },

  clearSearch() {
    this.loading = false;
    this.results = [];
    this.total = 0;
    this.page = 1;
    this.filters = {};
    this.sort = 'bestMatch';
    this.searchInput = null;
  }
};

export const PublicationsStore = defineStore('publications', {
  state,
  actions
});
