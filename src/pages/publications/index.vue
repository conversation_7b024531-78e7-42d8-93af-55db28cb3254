<script setup>
import { onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { PublicationsStore } from '@/pages/publications/store';
import { AuthStore } from '@/pages/auth/store';
import { FULL_TEXT_SEARCH } from '@/utils/features';
import PublicationsTable from '@/pages/publications/components/publications-table.vue';
import PublicationsSort from '@/pages/publications/components/publications-sort.vue';
import FeatureTeasing from '@/components/commons/feature-teasing.vue';

defineProps({
  isMobile: Boolean
});

const authStore = AuthStore();
const { features } = storeToRefs(authStore);
const isAllowedForFullTextSearch = computed(() =>
  features.value.includes(FULL_TEXT_SEARCH)
);

const publicationsStore = PublicationsStore();
const { loading, results, total, searchInput } = storeToRefs(publicationsStore);

function updateSort(value) {
  publicationsStore.updateSort(value);
}

function search(value) {
  if (!value) return;

  const params = { q: value };

  if (loading.value) {
    params.abort = true;
  }

  publicationsStore.search(params);
  publicationsStore.aggregations(params);
}

onMounted(() => {
  publicationsStore.clearSearch();
});
</script>

<template>
  <div
    :class="[
      'publications-header',
      { hasResults: searchInput && results.length > 0 }
    ]"
  >
    <div class="publications-search shadow-10">
      <q-input
        v-model="searchInput"
        :loading="loading"
        :label="$t('searchForPublications')"
        :disable="!isAllowedForFullTextSearch || loading"
        :debounce="1000"
        borderless
        clearable
        class="publications-search-field"
        @update:model-value="search"
        @clear="publicationsStore.clearSearch"
      >
        <template v-slot:before>
          <q-icon
            color="primary"
            name="fa-regular fa-newspaper"
            size="xs"
            class="q-mr-sm"
          />
        </template>
        <template v-slot:after>
          <q-btn
            :disable="!isAllowedForFullTextSearch || loading"
            round
            flat
            size="md"
            color="primary"
            @click="() => search(searchInput)"
          >
            <q-icon color="primary" name="fa-regular fa-search" size="xs" />
          </q-btn>
        </template>
      </q-input>
    </div>
  </div>

  <div
    v-show="!isAllowedForFullTextSearch"
    class="result-container teasing-container"
  >
    <FeatureTeasing :feature="FULL_TEXT_SEARCH" />
  </div>

  <div
    v-show="searchInput && isAllowedForFullTextSearch"
    class="result-container"
  >
    <div class="flex items-center justify-between q-pb-md">
      <div class="text-h6">
        {{ $t('resultsFor', { total, input: searchInput }) }}
      </div>
      <q-space />
      <PublicationsSort @sort="updateSort" />
    </div>

    <PublicationsTable :overlay="false" />
  </div>
</template>

<style scoped>
.publications-header {
  position: relative;
  background: url(/company-bg.jpg),
    radial-gradient(circle at 25%, var(--primary), var(--primaryDark));
  background-size: cover;
  background-blend-mode: multiply;
  height: 20rem;
  margin-top: -24px;
  transition: height 300ms ease-in-out;
}

.publications-header.hasResults {
  height: 5rem;
}

.publications-search {
  position: absolute;
  left: 1rem;
  right: 1rem;
  bottom: 0;
  transform: translateY(50%);
  background: var(--white);
  border: 1px solid var(--border);
  padding: 1rem;
  z-index: 1;
  border-radius: 0.5rem;
}

@media (min-width: 1440px) {
  .publications-search {
    left: 3rem;
    right: 3rem;
    padding: 1rem 3rem;
  }
}

.publications-search-field {
  display: flex;
  border: 3px solid var(--primary);
  border-radius: 0.75rem;
  padding: 0.25rem 1rem;
}

.result-container {
  padding: 5rem 3rem 1rem;
}

@media (min-width: 600px) {
  .publications-sorting {
    max-width: 15rem;
  }
}

.teasing-container {
  position: relative;
  min-height: 400px;
}
</style>
