<script setup>
import { ref } from 'vue';

defineProps({
  title: String
});

const isCollapsed = ref(true);
</script>

<template>
  <div>
    <div class="text-subtitle2 q-pb-sm">{{ title }}</div>
    <div :class="['collapse-group', { opened: !isCollapsed }]">
      <slot />
    </div>
    <q-btn
      :label="isCollapsed ? $t('showMore') : $t('showLess')"
      class="full-width collapse-btn"
      color="primary"
      size="xs"
      flat
      @click="isCollapsed = !isCollapsed"
    />
  </div>
</template>

<style scoped>
.collapse-group {
  display: flex;
  max-height: 10rem;
  overflow: hidden;
  transition: max-height 1s ease-in-out;
}

.collapse-group.opened {
  max-height: 200rem;
  padding-bottom: 1rem;
}

.collapse-btn {
  position: relative;
  z-index: 1;
}

.collapse-btn:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  transform: translateY(-100%);
  height: 2rem;
  width: 100%;
  background: linear-gradient(transparent, var(--white));
}
</style>
