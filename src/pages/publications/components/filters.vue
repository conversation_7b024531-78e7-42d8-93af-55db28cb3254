<script setup>
import { ref, computed } from 'vue';
import { parseFromApi, formatForApi } from '@/utils/date';
import FilterBox from '@/pages/publications/components/filter-box.vue';

const emit = defineEmits(['change', 'reset']);
const props = defineProps({
  loading: Boolean,
  filters: Object,
  aggs: Object
});

const isDirty = computed(() => Object.keys(props.filters).length > 0);

const publicationDates = computed(() => {
  const { from, to } = props?.filters?.range_filters?.publication_date?.[0] ?? {
    from: null,
    to: null
  };

  return {
    from: parseFromApi(from),
    to: parseFromApi(to)
  };
});

const selectedPublicationDatesFrom = ref(publicationDates.value.from || null);
const selectedPublicationDatesTo = ref(publicationDates.value.to || null);
const selectedNotaries = ref([]);
const selectedAuditor = ref([]);
const selectedFunction = ref([]);
const selectedPostalCode = ref([]);
const selectedCompanyIdentifier = ref([]);
const dateFromMenu = ref(null);
const dateToMenu = ref(null);

const applyFilters = function () {
  // Close dates menus
  dateFromMenu.value.hide();
  dateToMenu.value.hide();

  let params = {};

  let to = selectedPublicationDatesTo.value || '';
  to = to.replaceAll('_', '');

  let from = selectedPublicationDatesFrom.value || '';
  from = from.replaceAll('_', '');

  if (from.length > 2 && to.length > 2) {
    const from = formatForApi(selectedPublicationDatesFrom.value);
    const to = formatForApi(selectedPublicationDatesTo.value);
    params = {
      ...params,
      range_filters: {
        publication_date: [{ from, to }]
      },
      terms_filters: {}
    };
  }

  const selectedFilters = {
    notary: selectedNotaries.value,
    auditor: selectedAuditor.value,
    function: selectedFunction.value,
    postal_code: selectedPostalCode.value
  };

  Object.keys(selectedFilters).forEach((key) => {
    if (selectedFilters[key] && selectedFilters[key].length) {
      params.terms_filters = {
        ...params.terms_filters,
        [key]: selectedFilters[key]
      };
    }
  });

  emit('change', params);
};

const resetFilters = function () {
  selectedPublicationDatesFrom.value = null;
  selectedPublicationDatesTo.value = null;
  selectedNotaries.value = [];
  selectedAuditor.value = [];
  selectedFunction.value = [];
  selectedPostalCode.value = [];
  selectedCompanyIdentifier.value = [];
  emit('reset');
};
</script>

<template>
  <q-card>
    <q-card-section>
      <div class="flex justify-between items-center">
        <div class="text-h6 text-capitalize">
          {{ $t('filters') }}
        </div>

        <q-btn
          v-show="isDirty"
          size="sm"
          flat
          color="primary"
          no-caps
          @click="resetFilters"
        >
          {{ $t('resetFilters') }}
        </q-btn>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <!-- PUBLICATION DATE -->
      <div class="text-subtitle2 q-pb-sm">{{ $t('publicationDate') }}</div>

      <!-- FROM -->
      <q-input
        v-model="selectedPublicationDatesFrom"
        label="From"
        mask="##/##/####"
        fill-mask
        class="q-mb-sm"
        outlined
        dense
      >
        <template #append>
          <q-icon
            name="fa-regular fa-calendar"
            size="xs"
            class="cursor-pointer"
          >
            <q-menu
              ref="dateFromMenu"
              cover
              transition-show="scale"
              transition-hide="scale"
            >
              <q-date
                v-model="selectedPublicationDatesFrom"
                mask="DD/MM/YYYY"
                @update:model-value="applyFilters"
              />
            </q-menu>
          </q-icon>
        </template>
      </q-input>

      <!-- TO -->
      <q-input
        v-model="selectedPublicationDatesTo"
        label="To"
        mask="##/##/####"
        fill-mask
        outlined
        dense
      >
        <template #append>
          <q-icon
            name="fa-regular fa-calendar"
            size="xs"
            class="cursor-pointer"
          >
            <q-menu
              ref="dateToMenu"
              cover
              transition-show="scale"
              transition-hide="scale"
            >
              <q-date
                v-model="selectedPublicationDatesTo"
                mask="DD/MM/YYYY"
                @update:model-value="applyFilters"
              />
            </q-menu>
          </q-icon>
        </template>
      </q-input>
    </q-card-section>

    <q-linear-progress
      v-show="loading"
      indeterminate
      color="primary"
      class="filter-loading"
    />

    <div v-show="!loading">
      <!-- NOTARIES -->
      <q-card-section v-show="aggs.notaries.length">
        <FilterBox :title="$t('notaries')">
          <q-option-group
            v-model="selectedNotaries"
            :options="aggs.notaries"
            class="text-capitalize"
            type="checkbox"
            name="notaries"
            dense
            @update:model-value="applyFilters"
          >
            <template #label="option">
              <q-item dense>
                <q-item-section class="text-caption">
                  {{ option.label }}
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="sm">{{ option.count }}</q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-option-group>
        </FilterBox>
      </q-card-section>
      <!-- AUDITORS -->
      <q-card-section v-show="aggs.auditors.length">
        <FilterBox :title="$t('auditors')">
          <q-option-group
            v-model="selectedAuditor"
            :options="aggs.auditors"
            type="checkbox"
            class="text-capitalize"
            name="auditors"
            dense
            @update:model-value="applyFilters"
          >
            <template #label="option">
              <q-item dense>
                <q-item-section class="text-caption">
                  {{ option.label }}
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="sm">{{ option.count }}</q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-option-group>
        </FilterBox>
      </q-card-section>
      <!-- FUNCTIONS -->
      <q-card-section v-show="aggs.functions.length">
        <FilterBox :title="$t('functions')">
          <q-option-group
            v-model="selectedFunction"
            :options="aggs.functions"
            type="checkbox"
            class="text-capitalize"
            name="functions"
            dense
            @update:model-value="applyFilters"
          >
            <template #label="option">
              <q-item dense>
                <q-item-section class="text-caption">
                  {{ option.label }}
                </q-item-section>
                <q-item-section avatar>
                  <q-chip size="sm">{{ option.count }}</q-chip>
                </q-item-section>
              </q-item>
            </template>
          </q-option-group>
        </FilterBox>
      </q-card-section>
      <!-- POSTAL CODES -->
      <q-card-section v-show="aggs.postalCodes.length">
        <FilterBox :title="$t('postalCodes')">
          <q-option-group
            v-model="selectedPostalCode"
            :options="aggs.postalCodes"
            type="checkbox"
            name="postalCodes"
            dense
            @update:model-value="applyFilters"
          />
        </FilterBox>
      </q-card-section>
      <!-- COMPANY IDENTIFIERS -->
      <q-card-section v-show="aggs.companyIdentifiers.length">
        <FilterBox :title="$t('companies')">
          <q-option-group
            v-model="selectedCompanyIdentifier"
            :options="aggs.companyIdentifiers"
            type="checkbox"
            name="companyIdentifiers"
            dense
            @update:model-value="applyFilters"
          />
        </FilterBox>
      </q-card-section>
    </div>
  </q-card>
</template>

<style scoped>
.filter-loading {
  border-bottom-left-radius: 0.75rem !important;
  border-bottom-right-radius: 0.75rem !important;
}
</style>
