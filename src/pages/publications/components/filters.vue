<script setup>
import get from 'lodash/get';
import { ref, computed } from 'vue';
import { parseFromApi, formatForApi } from '@/utils/date';
import FilterBox from '@/pages/publications/components/filter-box.vue';

const emit = defineEmits(['change', 'reset']);
const props = defineProps({
  loading: Boolean,
  filters: Object,
  aggs: Object
});

const isDirty = computed(() => Object.keys(props.filters).length > 0);

const publicationDates = computed(() => {
  const { from, to } = get(props, 'filters.range_filters.publication_date[0]', {
    from: null,
    to: null
  });

  return {
    from: parseFromApi(from),
    to: parseFromApi(to)
  };
});

const selectedPublicationDatesFrom = ref(publicationDates.value.from || null);
const selectedPublicationDatesTo = ref(publicationDates.value.to || null);
const selectedNotaries = ref([]);
const selectedAuditor = ref([]);
const selectedFunction = ref([]);
const selectedPostalCode = ref([]);
const selectedCompanyIdentifier = ref([]);
const dateFromMenu = ref(null);
const dateToMenu = ref(null);

const applyFilters = function () {
  // Close dates menus
  dateFromMenu.value.hide();
  dateToMenu.value.hide();

  let params = {};

  let to = selectedPublicationDatesTo.value || '';
  to = to.replaceAll('_', '');

  let from = selectedPublicationDatesFrom.value || '';
  from = from.replaceAll('_', '');

  if (from.length > 2 && to.length > 2) {
    const from = formatForApi(selectedPublicationDatesFrom.value);
    const to = formatForApi(selectedPublicationDatesTo.value);
    params = {
      ...params,
      range_filters: {
        publication_date: [{ from, to }]
      },
      terms_filters: {}
    };
  }

  const selectedFilters = {
    notary: selectedNotaries.value,
    auditor: selectedAuditor.value,
    function: selectedFunction.value,
    postal_code: selectedPostalCode.value
  };

  Object.keys(selectedFilters).forEach((key) => {
    if (selectedFilters[key] && selectedFilters[key].length) {
      params.terms_filters = {
        ...params.terms_filters,
        [key]: selectedFilters[key]
      };
    }
  });

  emit('change', params);
};

const resetFilters = function () {
  selectedPublicationDatesFrom.value = null;
  selectedPublicationDatesTo.value = null;
  selectedNotaries.value = [];
  selectedAuditor.value = [];
  selectedFunction.value = [];
  selectedPostalCode.value = [];
  selectedCompanyIdentifier.value = [];
  emit('reset');
};
</script>

<template>
  <q-card>
    <q-card-section>
      <div class="flex justify-between items-center">
        <div class="text-h6 text-capitalize">
          {{ $t('filters') }}
        </div>

        <q-btn
          v-show="isDirty"
          size="sm"
          flat
          color="primary"
          no-caps
          @click="resetFilters"
        >
          {{ $t('resetFilters') }}
        </q-btn>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <!-- PUBLICATION DATE -->
      <div class="text-subtitle2 q-pb-sm">{{ $t('publicationDate') }}</div>

      <!-- FROM -->
      <q-input
        v-model="selectedPublicationDatesFrom"
        label="From"
        mask="##/##/####"
        fill-mask
        class="q-mb-sm"
        outlined
        dense
      >
        <template #append>
          <q-icon
            name="fa-regular fa-calendar"
            size="xs"
            class="cursor-pointer"
          >
            <q-menu
              ref="dateFromMenu"
              cover
              transition-show="scale"
              transition-hide="scale"
            >
              <q-date
                v-model="selectedPublicationDatesFrom"
                mask="DD/MM/YYYY"
                @update:model-value="applyFilters"
              />
            </q-menu>
          </q-icon>
        </template>

<style scoped>
.filter-loading {
  border-bottom-left-radius: 0.75rem !important;
  border-bottom-right-radius: 0.75rem !important;
}
</style>
