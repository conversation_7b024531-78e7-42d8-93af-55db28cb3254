<script setup>
defineProps({
  results: Array,
  total: Number,
  page: Number,
  pages: Number,
  searchInput: String
});
</script>

<template>
  <div>
    <div v-show="!results.length">{{ $t('noResultFound') }}</div>

    <q-card
      v-for="result in results"
      :key="result.index"
      bordered
      flat
      class="q-mb-lg"
    >
      <q-card-section>
        <div class="text-bold q-pb-sm">
          {{ result.date }} -
          <a :href="result.uri" target="_blank">{{ result.highlightsTitle }}</a>
        </div>

        <div class="text-bold">
          <q-btn
            :to="`/companies/${result.companyVat}`"
            size="sm"
            no-caps
            flat
            color="primary"
            class="q-px-xs result-company-btn"
            icon="fa-regular fa-building"
          >
            <span class="text-caption q-pl-sm">
              {{ result.companyVatFormatted }} - {{ result.companyName }}
            </span>
          </q-btn>
          <span v-if="result.address" class="text-caption q-ml-md">
            {{ result.address }}
          </span>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <div class="highlights">
          <div
            v-for="(highlight, highlightIndex) in result.highlights"
            :key="highlightIndex"
            class="highlight"
          >
            <div v-html="highlight" />
          </div>
        </div>
      </q-card-section>

      <q-separator v-show="result.uri" />

      <q-card-actions v-show="result.uri">
        <q-btn
          :href="result.uri"
          target="_blank"
          unelevated
          color="primary"
          label="Read document"
          size="sm"
        />
        <q-btn
          v-for="tag in result.tags"
          :key="tag"
          flat
          size="sm"
          @click="$emit('apply-keyword', tag)"
        >
          #{{ tag }}
        </q-btn>
      </q-card-actions>
    </q-card>

    <div class="flex justify-center">
      <q-pagination
        :model-value="page"
        :max="pages"
        :max-pages="10"
        class="q-my-lg"
        @update:model-value="(value) => $emit('paginate', value)"
      />
    </div>
  </div>
</template>

<style scope>
.occurrence {
  background-color: rgb(255 255 0 / 0.3);
  font-weight: bold;
}

.highlights {
  padding-left: 1rem;
  border-left: 4px solid var(--greyLight);
}

.highlight {
  padding: 0.5rem 0;
  font-size: var(--small);
  border-bottom: 1px dashed var(--grey);
}

.highlight:last-child {
  border-bottom: none;
}

.result-company-btn {
  margin-left: -0.25rem;
}
</style>
