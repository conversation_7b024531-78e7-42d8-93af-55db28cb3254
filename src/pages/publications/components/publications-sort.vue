<script setup>
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { PublicationsStore } from '@/pages/publications/store';

defineProps({
  searchInput: String
});

const { t } = useI18n();

const publicationsStore = PublicationsStore();
const { sort } = storeToRefs(publicationsStore);

const sorts = [
  {
    label: t('bestMatch'),
    value: 'bestMatch'
  },
  {
    label: t('publicationDateNewest'),
    value: 'publicationDateNewest'
  },
  {
    label: t('publicationDateOldest'),
    value: 'publicationDateOldest'
  }
];
</script>

<template>
  <q-select
    :model-value="sort"
    :label="$t('sortBy')"
    :options="sorts"
    class="col-grow publication-sorting"
    outlined
    dense
    emit-value
    map-options
    style="max-width: 15rem"
    @update:model-value="(value) => $emit('sort', value)"
  />
</template>
