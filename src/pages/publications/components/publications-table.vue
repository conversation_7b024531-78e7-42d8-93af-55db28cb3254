<script setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { PublicationsStore } from '@/pages/publications/store';
import Filters from '@/pages/publications/components/filters.vue';
import Results from '@/pages/publications/components/results.vue';

const publicationsStore = PublicationsStore();
const { loading, searchInput, results, page, max, total, filters, sort, aggs } =
  storeToRefs(publicationsStore);
const pages = computed(() => Math.ceil(total.value / max.value));

const search = function ({ input, updatedPage, updatedFilters }) {
  const q = input || searchInput.value || '';
  const newPage = updatedPage || page.value || 1;

  publicationsStore.search({
    q,
    page: newPage,
    filters: updatedFilters || filters.value,
    sort: sort.value
  });

  publicationsStore.aggregations({
    q,
    page: newPage
  });
};
</script>

<template>
  <div class="row q-col-gutter-lg">
    <div class="col-12 col-sm-5 col-md-4 col-lg-3">
      <Filters
        :loading="loading"
        :filters="filters"
        :aggs="aggs"
        @change="(updatedFilters) => search({ updatedFilters })"
        @reset="search({ updatedFilters: {}, page: 1 })"
      />
    </div>
    <div class="col-12 col-sm-7 col-md-8 col-lg-9">
      <Results
        :loading="loading"
        :search-input="searchInput"
        :results="results"
        :total="total"
        :page="page"
        :pages="pages"
        @paginate="(updatedPage) => search({ updatedPage })"
        @apply-keyword="(input) => search({ input })"
      />
    </div>
  </div>
</template>
