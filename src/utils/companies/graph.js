import i18n from '@/plugins/i18n';

function toSnakeCase(str) {
  return str
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '');
}

export function prepareSeries(
  { selectedSeries, options, statements } = {
    selectedSeries: [],
    options: {},
    statements: []
  }
) {
  return selectedSeries.map((selectedSerieId) => {
    const option = options.find((option) => option.id === selectedSerieId);

    const serie = {
      id: selectedSerieId,
      name: selectedSerieId,
      type: option?.type,
      currency: null,
      data: []
    };

    statements.forEach((statement) => {
      // direct access data
      if (!option?.path.includes('.')) {
        const directValue = option?.path ? statement[option.path] : null;
        serie.data.push(directValue);

        if (statement.currency && serie.currency === null) {
          serie.currency = statement.currency;
        }
        return;
      }

      // look  for financials data
      const statementSource = option?.path
        .split('.')
        .reduce((obj, key) => obj?.[key], statement);
      const value = statementSource?.current_value;

      if (value === null || typeof value === 'undefined') return;

      if (statement.currency && serie.currency === null) {
        serie.currency = statement.currency;
      }
      serie.data.push(statementSource.current_value);
    });

    return serie;
  });
}

export function prepareAxis(statements = []) {
  const years = [];

  statements.forEach((statement) => {
    years.push(statement.year);
  });

  return years;
}

export function formatter(value, serie, locale) {
  if (value === null || typeof value === 'undefined') return 'N/A';

  const options = {
    notation: 'compact',
    maximumFractionDigits: 1
  };

  if (serie?.series?.currency) {
    options.style = 'currency';
    options.currency = serie.series.currency;
  }

  return Intl.NumberFormat(locale, options).format(value);
}

export function prepareOptions(series = [], categories = []) {
  const locale = i18n.global.locale.value;

  return {
    chart: {
      redrawOnParentResize: true,
      fontFamily: 'Open Sans, sans-serif'
    },

    title: {
      text: i18n.global.t('graphs'),
      align: 'left'
    },

    colors: ['#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800'],

    yaxis: series.map((serie, index) => ({
      seriesName: serie.name,

      opposite: series.length === 2 && index === 1,

      title: {
        text: i18n.global.t(
          `financialsKeys.overview.${toSnakeCase(serie.name)}`
        )
      },

      axisTicks: {
        show: true
      },

      axisBorder: {
        show: true
      },

      labels: {
        formatter: (value) => formatter(value, serie, locale)
      }
    })),

    xaxis: {
      title: {
        text: i18n.global.t('year')
      },
      categories
    },

    legend: {
      horizontalAlign: 'left',
      offsetX: 40,
      formatter: (seriesName) =>
        i18n.global.t(`financialsKeys.overview.${toSnakeCase(seriesName)}`)
    },

    plotOptions: {
      area: {
        fillTo: 'end'
      },

      bar: {
        horizontal: false,
        columnWidth: '55%',
        endingShape: 'rounded'
      }
    },

    fill: {
      opacity: series.map((serie) => (serie.type === 'area' ? 0.5 : 1))
    },

    // markers: { size: 6 },
    stroke: { width: 4 },

    tooltip: {
      y: {
        title: {
          formatter: (seriesName) =>
            i18n.global.t(`financialsKeys.overview.${toSnakeCase(seriesName)}`)
        }
      }
    }
  };
}
