import get from 'lodash/get';
import slice from 'lodash/slice';
import statementStructure from '@/pages/companies/data/statement-structure.json';
import i18n from '@/plugins/i18n';

export function prepareRows(statements, access) {
  const keys = get(statementStructure, access, []);

  if (!Array.isArray(keys)) {
    return Object.keys(keys).reduce((all, group) => {
      return {
        ...all,
        [group]: rowMaker(keys[group], statements, access, group)
      };
    }, {});
  }

  return rowMaker(keys, statements, access);
}

function rowMaker(keys, statements, access, group = null) {
  if (!Array.isArray(keys)) return [];

  return keys.map((key) => {
    let rowsValues = {};
    let valueCounter = 0;
    const groupAccessor = group ? `[${group}]` : '';

    statements.forEach((statement) => {
      const extract = get(statement, `${access}${groupAccessor}[${key}]`);

      if (!extract) return;

      if (extract.current_value) {
        valueCounter++;
      }

      rowsValues = {
        ...rowsValues,
        [statement.year]: {
          currency: extract.currency,
          current: extract.current_value,
          growth: extract.relative_growth
        }
      };
    });

    return {
      description: ['financialsKeys', access, group, key]
        .filter(Boolean)
        .join('.'),
      emptyValues: valueCounter === 0,
      ...rowsValues
    };
  });
}

export function prepareColumns(statements = [], limit = null) {
  const years = [];

  let localStatements = statements || [];

  if (!Array.isArray(localStatements)) {
    localStatements = Array.from(statements, ([_id, value]) => value);
  }

  if (limit !== null) {
    localStatements = slice(localStatements, -limit);
  }

  localStatements.forEach((statement) => {
    const year = statement.year;

    years.push({
      name: 'year',
      label: year,
      field: year,
      align: 'center'
    });
  });

  return [
    {
      name: 'description',
      label: 'Description',
      align: 'left',
      field: 'description',
      format: (value) => i18n.global.t(value),
      style: 'max-width: 240px; white-space: normal;'
    },
    ...years.slice().reverse()
  ];
}
