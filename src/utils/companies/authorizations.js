import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';
import { getAuthorizationCodes } from '@/utils/code-loader';
import i18n from '@/plugins/i18n';

export async function prepareAuthorizations(authorizations = []) {
  if (!Array.isArray(authorizations) || !authorizations.length) return [];

  const authorizationCodes = await getAuthorizationCodes();

  return authorizations.map((authorization, index) => ({
    id: index,
    code: authorization.code,
    name: findCodeName(authorization.code, authorizationCodes),
    startDate: authorization.start_date
      ? formatDate(authorization.start_date, i18n.global.locale)
      : null
  }));
}
