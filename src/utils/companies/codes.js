import i18n from '@/plugins/i18n';
import get from 'lodash/get';

const defaultLanguage = 'nl';

export function findCodeName(code, list = []) {
  if (!code) return null;

  const codeEntity = list.find(
    (codeEntityCode) =>
      String(Object.keys(codeEntityCode)[0]).toUpperCase() ===
      code.toUpperCase()
  );

  if (!codeEntity) return null;

  let locale = i18n.global.locale.value;

  if (locale === 'en-US') {
    locale = 'en';
  }

  const codeEntityByLocale = Object.values(codeEntity).map((translations) => {
    let translation;

    translation = translations.find(
      (translation) => Object.keys(translation)[0] === locale
    );

    if (translation) {
      return translation[locale];
    }

    translation = translations.find(
      (translation) => Object.keys(translation)[0] === defaultLanguage
    );

    return translation?.[defaultLanguage];
  });

  return get(codeEntityByLocale, '[0].label', null);
}
