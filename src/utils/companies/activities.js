import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';
import { getActivityCodes } from '@/utils/code-loader';
import i18n from '@/plugins/i18n-dynamic';

export async function findMainActivity(activities = [], takeMain = false) {
  const activitiesCodes = await getActivityCodes();

  let mainActivity;

  if (activities.length && takeMain) {
    mainActivity = activities.find(
      (activity) => activity.active && activity.primary
    );
  }

  if (activities.length && !takeMain) {
    mainActivity = activities[0];
  }

  if (mainActivity) {
    return findCodeName(mainActivity.code, activitiesCodes);
  }

  return null;
}

export async function prepareActivities(activities = []) {
  if (!activities.length) return [];

  const activitiesCodes = await getActivityCodes();

  return activities
    .map((activity, index) => ({
      id: index,
      primary: activity.primary,
      code: activity.code,
      name: findCodeName(activity.code, activitiesCodes),
      startDate: activity.start_date
        ? formatDate(activity.start_date, i18n.global.locale)
        : null
    }))
    .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
    .reverse();
}
