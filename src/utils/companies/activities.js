import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';
import { getActivitiesCode } from '@/utils/activities-code';import i18n from '@/plugins/i18n';

export function findMainActivity(activities = [], takeMain = false) {

  
  let mainActivity;

  if (activities.length && takeMain) {
    mainActivity = activities.find(
      (activity) => activity.active && activity.primary
    );
  }

  if (activities.length && !takeMain) {
    mainActivity = activities[0];
  }

  if (mainActivity) {
    return findCodeName(mainActivity.code, activitiesCodes);
  }

  return null;
}

export function prepareActivities(activities = []) {
  if (!activities.length) return [];

  return activities
    .map((activity, index) => ({
      id: index,
      primary: activity.primary,
      code: activity.code,
      name: findCodeName(activity.code, activitiesCodes),
      startDate: activity.start_date
        ? formatDate(activity.start_date, i18n.global.locale)
        : null
    }))
    .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
    .reverse();
}
