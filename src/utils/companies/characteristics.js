import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';
import { getCharacteristicCodes } from '@/utils/code-loader';
import i18n from '@/plugins/i18n-dynamic';

export async function prepareCharacteristics(characteristics = []) {
  if (!Array.isArray(characteristics) || !characteristics.length) return [];

  const characteristicCodes = await getCharacteristicCodes();

  return characteristics
    .map((characteristic, index) => ({
      id: index,
      code: characteristic.code,
      name: findCodeName(characteristic.code, characteristicCodes),
      startDate: characteristic.start_date
        ? formatDate(characteristic.start_date, i18n.global.locale)
        : null
    }))
    .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
    .reverse();
}
