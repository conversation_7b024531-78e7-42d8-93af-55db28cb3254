/**
 * Generic code loader utility for loading JSON code files from public directory
 * Supports caching, error handling, and multiple file types
 */

// Global cache for all code files
const codeCache = new Map();
const loadingPromises = new Map();

/**
 * Generic function to load any code file from public directory
 * @param {string} fileName - Name of the JSON file (without extension)
 * @returns {Promise<Array>} Code data array
 */
export const getCodeData = async (fileName) => {
  // Return cached data if available
  if (codeCache.has(fileName)) {
    return codeCache.get(fileName);
  }

  // Return existing promise if already loading
  if (loadingPromises.has(fileName)) {
    return loadingPromises.get(fileName);
  }

  // Create loading promise
  const loadingPromise = fetchCodeData(fileName);
  loadingPromises.set(fileName, loadingPromise);

  try {
    const data = await loadingPromise;
    codeCache.set(fileName, data);
    return data;
  } catch (error) {
    // Reset loading promise on error so it can be retried
    loadingPromises.delete(fileName);
    throw error;
  } finally {
    // Clear loading promise when done
    loadingPromises.delete(fileName);
  }
};

/**
 * Internal function to fetch the data
 * @param {string} fileName - Name of the JSON file
 */
async function fetchCodeData(fileName) {
  try {
    // Try to fetch from public directory
    const publicPaths = [
      `/data/${fileName}.json`,
      `${window.location.origin}/data/${fileName}.json`
    ];

    for (const path of publicPaths) {
      try {
        const response = await fetch(path);
        if (response.ok) {
          // Check if response is actually JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            return data;
          }
        }
      } catch (error) {
        console.warn(`Failed to load ${fileName} from ${path}:`, error.message);
        continue;
      }
    }

    // If we reach here, all paths failed
    throw new Error(
      `Failed to load ${fileName} data from public directory. ` +
        `Please ensure the file exists at public/data/${fileName}.json`
    );
  } catch (error) {
    console.error(`Error loading ${fileName}:`, error);
    throw new Error(`Failed to load ${fileName} data`);
  }
}

/**
 * Clear cache for a specific file or all files
 * @param {string} fileName - Optional file name to clear, if not provided clears all
 */
export const clearCodeCache = (fileName = null) => {
  if (fileName) {
    codeCache.delete(fileName);
    loadingPromises.delete(fileName);
  } else {
    codeCache.clear();
    loadingPromises.clear();
  }
};

/**
 * Check if data is already loaded for a specific file
 * @param {string} fileName - Name of the file to check
 * @returns {boolean} True if data is cached
 */
export const isCodeDataLoaded = (fileName) => {
  return codeCache.has(fileName);
};

/**
 * Preload multiple code files
 * @param {Array<string>} fileNames - Array of file names to preload
 * @returns {Promise<Object>} Object with fileName as key and data as value
 */
export const preloadCodeData = async (fileNames) => {
  const results = {};

  try {
    const promises = fileNames.map(async (fileName) => {
      try {
        const data = await getCodeData(fileName);
        results[fileName] = data;
        return { fileName, data, success: true };
      } catch (error) {
        console.error(`Failed to preload ${fileName}:`, error);
        results[fileName] = null;
        return { fileName, error, success: false };
      }
    });

    await Promise.allSettled(promises);
    return results;
  } catch (error) {
    console.error('Error during preload:', error);
    throw error;
  }
};

// Specific getters for each code type (for backward compatibility and convenience)
export const getActivityCodes = () => getCodeData('activity-codes');
export const getAuthorizationCodes = () => getCodeData('authorization-codes');
export const getJuridicalFormCodes = () => getCodeData('juridical-form-codes');
export const getJuridicalSituationCodes = () =>
  getCodeData('juridical-situation-codes');
export const getCharacteristicCodes = () => getCodeData('characteristic-codes');
export const getRolesCodes = () => getCodeData('roles-codes');
export const getLegalPersonTypeCodes = () =>
  getCodeData('legal-person-type-codes');

// Backward compatibility alias
export const getActivitiesCode = getActivityCodes;

// Export list of all available code files
export const AVAILABLE_CODE_FILES = [
  'activity-codes',
  'authorization-codes',
  'juridical-form-codes',
  'juridical-situation-codes',
  'characteristic-codes',
  'roles-codes',
  'legal-person-type-codes'
];
