import { findCodeName } from '@/utils/companies/codes';
import { formatDate } from '@/utils/date';
import {
  getJuridicalFormCodes,
  getJuridicalSituationCodes
} from '@/utils/code-loader';
import i18n from '@/plugins/i18n';

async function prepareJuridicalForm(form) {
  let juridicalFormCode = null;

  if (form) {
    const juridicalFormCodes = await getJuridicalFormCodes();
    juridicalFormCode = findCodeName(form, juridicalFormCodes);
  }

  return juridicalFormCode;
}

async function prepareJuridicalSituation(situation) {
  let juridicalFormCode = null;

  if (situation) {
    const juridicalSituationCodes = await getJuridicalSituationCodes();
    juridicalFormCode = findCodeName(situation, juridicalSituationCodes);
  }

  return juridicalFormCode;
}

function getGroupFromNodeType(type, isActive, isMain) {
  if (isMain) {
    return `${type}-main`;
  }

  return `${type}-${isActive ? 'active' : 'inactive'}`;
}

function getIconFromType(type, isExternal) {
  const spacer = '<i> </i>';
  const iconDict = {
    company: `<i></i>${spacer}`,
    address: `<i></i>${spacer}`,
    person: `<i></i>${spacer}`,
    external: `<i></i>${spacer}`
  };

  if (isExternal) return iconDict.external;

  return iconDict[type] || '';
}

function getColorFromType(type, isActive, isExternal) {
  const colorDict = {
    company: '#1F72C1',
    person: '#6AB759',
    address: '#FDCC62',
    external: '#46565E',
    inactive: '#87A5B2'
  };

  if (isExternal) return colorDict.external;

  if (!isActive) return colorDict.inactive;

  return colorDict[type] || colorDict.inactive;
}

function iconByType(type) {
  const iconDict = {
    company: 'fa-regular fa-building',
    address: 'fa-regular fa-location-dot',
    person: 'fa-regular fa-user'
  };

  return iconDict[type];
}

export default async function formatGraph(data) {
  if (!data) return null;

  const nodes = await Promise.all(
    data.nodes.map(async (node) => {
      let sources = [];

      if (Array.isArray(node.sources) && node.sources.length > 0) {
        sources = node.sources.map((source) => {
          const cleanedString = source.split('_');
          cleanedString.pop();
          return cleanedString.join('_');
        });
      }

      const infos = [];

      if (Object.keys(node.extra_information).length > 0) {
        for (const originalKey of Object.keys(node.extra_information)) {
          const key = originalKey.toLowerCase();

          if (['company_address', 'person_address'].includes(key)) {
            infos.push({
              label: 'address',
              value: node.extra_information[originalKey]
            });
          }

          if (key === 'company_form') {
            infos.push({
              label: 'juridicalForm',
              value: await prepareJuridicalForm(
                node.extra_information[originalKey]
              )
            });
          }

          if (key === 'company_situation') {
            infos.push({
              label: 'juridicalSituation',
              value: await prepareJuridicalSituation(
                node.extra_information[originalKey]
              )
            });
          }

          if (key === 'company_start') {
            infos.push({
              label: 'startDate',
              value: formatDate(
                node.extra_information[originalKey],
                i18n.global.locale
              )
            });
          }

          if (key === 'person_birthdate') {
            infos.push({
              label: 'birthdate',
              value: formatDate(
                node.extra_information[originalKey],
                i18n.global.locale
              )
            });
          }

          if (key === 'person_companies') {
            infos.push({
              label: 'companies',
              value: node.extra_information[originalKey]
                .split(',')
                .map((id) => ({ id, name: id }))
            });
          }

          if (key === 'company_employees') {
            infos.push({
              label: 'employees',
              value: node.extra_information[originalKey]
            });
          }

          if (key === 'company_turnover') {
            infos.push({
              label: 'turnover',
              value: node.extra_information[originalKey]
            });
          }
        }
      }

      const nodeData = {
        id: node.id,
        label: `${getIconFromType(node.type, node.external)} ${node.label}`,
        type: node.type,
        icon: iconByType(node.type),
        group: getGroupFromNodeType(node.type, node.has_active_edge, node.main),
        isActive: node.has_active_edge,
        isExternal: node.external,
        isMain: node.main,
        originalLabel: node.label,
        sources: [...new Set(sources)],
        infos
      };

      if (node.type === 'person') {
        const personUUIDs = node?.extra_information?.person_uuids ?? [];
        nodeData.personUUIDs = Array.isArray(personUUIDs)
          ? personUUIDs
          : personUUIDs.split(',');
      }

      return nodeData;
    })
  );

  const rawEdges = data?.edges ?? [];
  const edges = rawEdges.map((edge) => {
    const node = nodes.find((node) => node.id === edge.to);

    const preparedEdge = {
      id: edge.id,
      to: edge.to,
      from: edge.from,
      color: node
        ? getColorFromType(node.type, node.isActive, node.isExternal)
        : null,
      arrows: {
        from: edge.reversed,
        to: !edge.reversed
      },
      title: {},
      label: edge.label
    };

    if (edge.external) {
      preparedEdge.dashes = true;
      preparedEdge.length = 400;
    }

    if (edge.start_date) {
      preparedEdge.startDate = formatDate(edge.start_date, i18n.global.locale);
    }

    if (edge.end_date) {
      preparedEdge.endDate = formatDate(edge.end_date, i18n.global.locale);
    }

    return preparedEdge;
  });

  return {
    nodes,
    edges
  };
}
