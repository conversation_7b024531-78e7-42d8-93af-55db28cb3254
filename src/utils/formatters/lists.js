import { typesToLevels } from '@/pages/monitoring/utils/alert-levels';

export default function formatLists(list) {
  if (!list) return null;

  const watchedCompanies = list?.watchedCompanies ?? [];

  return {
    ...list,
    id: list.watchlistShortId || list.id,
    companiesCount: watchedCompanies.length,
    watchedCompanies: watchedCompanies.map(
      (company) => company.companyIdentifier
    ),
    alertLevels: typesToLevels(list.eventTypes)
  };
}
