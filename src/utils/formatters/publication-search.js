import { formatDate } from '@/utils/date';
import { findCodeName } from '@/utils/companies/codes';
import { getJuridicalSituationCodes, getRolesCodes } from '@/utils/code-loader';
import i18n from '@/plugins/i18n';

export async function formatPublicationSearch(publication) {
  if (!publication) return null;

  let address = null;

  if (publication.company_address) {
    address = [
      publication?.company_address?.postal_code,
      publication?.company_address?.locality
    ].join(' - ');
  }

  const publicationTags = publication?.tags ?? [];
  const juridicalSituationCodes = await getJuridicalSituationCodes();

  return {
    date: formatDate(publication.content_date, i18n.global.locale),
    companyName: publication.company_name.name,
    companyVat: publication.company_identifier,
    companyVatFormatted: publication.company_identifier.replace('_', ' '),
    companyJuridicalSituation: findCodeName(
      publication?.company_juridical_form?.code,
      juridicalSituationCodes
    ),
    title: publication?.title ?? '',
    highlightsTitle: publication?.high_lights?.title?.[0],
    highlights: publication?.high_lights?.content,
    uri: publication.uri,
    tags: publicationTags
      .map((tag) => tag.keywords)
      .map((keyword) => keyword)
      .flat(),
    address
  };
}

export async function formatAggs(agg) {
  const notaries = agg?.notary ?? [];
  const auditors = agg?.auditor ?? [];
  const functions = agg?.function ?? [];
  const postalCodes = agg?.postal_code ?? [];
  const companyIdentifiers = agg?.company_identifier ?? [];

  const rolesCodes = await getRolesCodes();

  return {
    notaries: notaries.map((notary) => ({
      label: notary.key,
      count: notary.doc_count,
      value: notary.key
    })),

    auditors: auditors.map((auditor) => ({
      label: auditor.key,
      count: auditor.doc_count,
      value: auditor.key
    })),

    functions: functions.map((func) => ({
      label: findCodeName(func.key, rolesCodes),
      count: func.doc_count,
      value: func.key
    })),

    postalCodes: postalCodes.map((postalCode) => ({
      label: postalCode.key,
      count: postalCode.doc_count,
      value: postalCode.key
    })),

    companyIdentifiers: companyIdentifiers.map((companyIdentifier) => ({
      label: companyIdentifier.key,
      count: companyIdentifier.doc_count,
      value: companyIdentifier.key
    }))
  };
}
