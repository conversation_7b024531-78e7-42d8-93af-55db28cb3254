import get from 'lodash/get';
import { formatDate } from '../date';
import juridicalSituationCodes from '@/data/juridical-situation-codes';
import { findCodeName } from '@/utils/companies/codes';
import rolesCodes from '@/data/roles-codes.json';
import i18n from '@/plugins/i18n';

export function formatPublicationSearch(publication) {
  if (!publication) return null;

  let address = null;

  if (publication.company_address) {
    address = [
      get(publication, 'company_address.postal_code'),
      get(publication, 'company_address.locality')
    ].join(' - ');
  }

  return {
    date: formatDate(publication.content_date, i18n.global.locale),
    companyName: publication.company_name.name,
    companyVat: publication.company_identifier,
    companyVatFormatted: publication.company_identifier.replace('_', ' '),
    companyJuridicalSituation: findCodeName(
      get(publication, 'company_juridical_form.code'),
      juridicalSituationCodes
    ),
    title: get(publication, 'title', '') || '',
    highlightsTitle: get(publication, 'high_lights.title[0]'),
    highlights: get(publication, 'high_lights.content'),
    uri: publication.uri,
    tags: get(publication, 'tags', [])
      .map((tag) => tag.keywords)
      .map((keyword) => keyword)
      .flat(),
    address
  };
}

export function formatAggs(agg) {
  const notaries = get(agg, 'notary', []);
  const auditors = get(agg, 'auditor', []);
  const functions = get(agg, 'function', []);
  const postalCodes = get(agg, 'postal_code', []);
  const companyIdentifiers = get(agg, 'company_identifier', []);

  return {
    notaries: notaries.map((notary) => ({
      label: notary.key,
      count: notary.doc_count,
      value: notary.key
    })),

    auditors: auditors.map((auditor) => ({
      label: auditor.key,
      count: auditor.doc_count,
      value: auditor.key
    })),

    functions: functions.map((func) => ({
      label: findCodeName(func.key, rolesCodes),
      count: func.doc_count,
      value: func.key
    })),

    postalCodes: postalCodes.map((postalCode) => ({
      label: postalCode.key,
      count: postalCode.doc_count,
      value: postalCode.key
    })),

    companyIdentifiers: companyIdentifiers.map((companyIdentifier) => ({
      label: companyIdentifier.key,
      count: companyIdentifier.doc_count,
      value: companyIdentifier.key
    }))
  };
}
