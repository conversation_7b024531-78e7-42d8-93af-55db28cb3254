import get from 'lodash/get';
import { typesToLevels } from '@/pages/monitoring/utils/alert-levels';
import formatAlert from '@/utils/formatters/alert';

export default function formatWatchlist(watchlist, withAlerts = false) {
  if (!watchlist) return null;

  const notifications = get(watchlist, 'notifications', []) || [];
  const alerts = get(watchlist, 'alerts', []) || [];
  const watchedCompanies = get(watchlist, 'watchedCompanies', []) || [];

  const formattedWatchlist = {
    id: watchlist.watchlistShortId,
    name: watchlist.name,
    type: 'COMPANY', // or 'PERSON',
    watchlistType: watchlist.watchlistType,
    watchedCompanies: watchedCompanies.map(
      (company) => company.companyIdentifier
    ),
    subscribers: notifications,
    subscribersCount: notifications.length,
    lastestAlert: watchlist.lastNotificationDateDaily
      ? new Date(watchlist.lastNotificationDateDaily).valueOf()
      : null,
    tags: get(watchlist, 'tags', []),
    alertLevels: typesToLevels(watchlist.eventTypes)
  };

  if (withAlerts) {
    formattedWatchlist.alerts = alerts.map(formatAlert);
  }

  return formattedWatchlist;
}
