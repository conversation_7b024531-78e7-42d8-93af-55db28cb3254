import { getAvatarColor, getInitials } from '@/utils/avatar';
import { formatDate } from '@/utils/date';
import i18n from '@/plugins/i18n-dynamic';

export default function formatUser(user = null) {
  if (!user) return null;

  const name = [user.firstName, user.lastName].join(' ');

  return {
    email: user.email,
    name,
    firstName: user.firstName,
    lastName: user.lastName,
    color: getAvatarColor(user.customerShortId),
    initials: getInitials(name),
    customerShortId: user.customerShortId,
    organisationShortId: user.organisationShortId,
    latestUpdate: formatDate(user.updatedAt, i18n.global.locale),
    language: user.language,
    level: user.subscription?.subscriptionType
  };
}
