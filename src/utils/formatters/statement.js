import { formatDate } from '../date';
import i18n from '@/plugins/i18n';

export function extractOverview(statement) {
  if (!statement) return null;

  const parts = {
    employees: statement.employees,
    turnover: statement.turnover,
    current_assets: statement.current_assets,
    gross_operating_margin: statement.gross_operating_margin,
    tangible_fixed_assets: statement.tangible_fixed_assets,
    equity: statement.equity,
    gain_loss_period: statement.gain_loss_period,
    current_ratio: statement.current_ratio,
    net_cash: statement.net_cash,
    self_financing_degree: statement.self_financing_degree,
    return_on_equity: statement.return_on_equity,
    added_value: statement.added_value
  };

  parts.date = formatDate(
    statement?.statement_identifier?.account_date,
    i18n.global.locale
  );

  return parts;
}

export function formatStatement(statement, extended = false) {
  if (!statement) return null;

  let accountingYear = statement?.statement_identifier?.account_date;

  if (accountingYear) {
    accountingYear = new Date(accountingYear).getFullYear();
  }

  const formattedStatement = {
    year: statement?.statement_identifier?.year,
    identifier: statement?.statement_identifier?.identifier,
    currentRatio: statement.current_ratio || null,
    currentRatioGrowth: Math.round(
      statement?.ratios?.current_ratio?.relative_growth ?? 0
    ),
    employees: statement.employees,
    netCash: statement.net_cash,
    netCashGrowth: Math.round(
      statement?.ratios?.net_cash?.relative_growth ?? 0
    ),
    netCashCurrency: statement?.ratios?.net_cash?.currency ?? null,
    returnOnEquity: statement.return_on_equity,
    returnOnEquityGrowth: Math.round(
      statement?.ratios?.return_on_equity?.relative_growth ?? 0
    ),
    cashFlowToDebt: statement?.ratios?.cash_flow_to_debt?.current_value ?? 0,
    cashFlowToDebtGrowth: Math.round(
      statement?.ratios?.cash_flow_to_debt?.relative_growth ?? 0
    ),
    cashFlowToDebtCurrency:
      statement?.ratios?.cash_flow_to_debt?.currency ?? null,
    grossOperatingMargin: statement.gross_operating_margin,
    grossOperatingMarginGrowth: Math.round(
      statement?.financials?.gross_operating_margin?.relative_growth ?? 0
    ),
    grossOperatingMarginCurrency:
      statement?.financials?.gross_operating_margin?.currency ?? null,
    currentAssets: statement.current_assets || null,
    tangibleFixedAssets: statement.tangible_fixed_assets || null,
    gainLossPeriod: statement.gain_loss_period || null,
    selfFinancingDegree: statement.self_financing_degree || null,
    addedValue: statement.added_value || null,
    accountingYear
  };

  if (extended) {
    formattedStatement.financials = statement.financials;
    formattedStatement.socials = statement.socials;
    formattedStatement.ratios = statement.ratios;
  }

  return formattedStatement;
}
