import { getAvatarColor, getInitials } from '@/utils/avatar';
import { prepareAddresses } from '@/utils/companies/addresses';
import { findMainActivity } from '@/utils/companies/activities';

export function formatEstablishment(establishment) {
  if (!establishment) return null;

  const vat = establishment?.vat_formatted ?? '';
  const { mainAddress } = prepareAddresses(establishment.addresses);

  return {
    identifier: establishment.identifier,
    id: establishment.identifier,
    name: establishment.company_name,
    vatFormatted: vat.replace('_', ' '),
    color: getAvatarColor(establishment.identifier),
    initials: getInitials(establishment.company_name),
    language: establishment.language || null,
    isActive: establishment.active,
    startDate: establishment.start_date,
    mainActivity: findMainActivity(establishment.activities, true),
    mainAddress: mainAddress ? mainAddress.formatted_address : null
  };
}
