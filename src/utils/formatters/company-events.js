import { formatDate } from '@/utils/date';
import { getActivitiesCode } from '@/utils/activities-code';
import sortBy from 'lodash/sortBy';
import { findCodeName } from '@/utils/companies/codes';
import {
  getJuridicalFormCodes,
  getJuridicalSituationCodes
} from '@/utils/code-loader';
import i18n from '@/plugins/i18n';

export async function formatCompanyEvents(events) {
  if (!events) return null;

  let maxEventByYear = 0;

  const activitiesCodes = await getActivitiesCode();
  const juridicalFormCodes = await getJuridicalFormCodes();
  const juridicalSituationCodes = await getJuridicalSituationCodes();

  const years = events.reduce((all, current) => {
    const year = current.date.split('-')[0];
    const event = {
      identifier: current.identifier,
      date: formatDate(current.date, i18n.global.locale),
      originalDate: current.date,
      severity: current.event_severity,
      type: current.event_type,
      from: current.diff.from,
      to: current.diff.to
    };

    const sources = {
      COMPANY_NACE_CODES: activitiesCodes,
      COMPANY_LEGAL_SITUATION: juridicalSituationCodes,
      COMPANY_LEGAL_FORM: juridicalFormCodes
    };

    if (Object.keys(sources).includes(event.type)) {
      const fromCode = findCodeName(event.from, sources[event.type]) || '';
      const toCode = findCodeName(event.to, sources[event.type]) || '';

      event.from = fromCode ? `${fromCode} (${event.from})` : event.from;
      event.to = toCode ? `${toCode} (${event.to})` : event.to;
    }

    const previousEntry = all.findIndex((entry) => entry.year === year);

    if (previousEntry !== -1) {
      const newCount = all[previousEntry].count + 1;
      const sortedEvents = [...all[previousEntry].events, event].sort(
        (a, b) => new Date(b.date) - new Date(a.date)
      );

      all[previousEntry] = {
        ...all[previousEntry],
        year,
        count: newCount,
        events: sortedEvents.map((event) => {
          const { originalDate, ...rest } = event;
          return rest;
        })
      };
      maxEventByYear = maxEventByYear < newCount ? newCount : maxEventByYear;
      return all;
    } else {
      maxEventByYear = maxEventByYear < 1 ? 1 : maxEventByYear;
      const { originalDate, ...eventRest } = event;
      return [
        ...all,
        {
          year,
          count: 1,
          events: [eventRest]
        }
      ];
    }
  }, []);

  return {
    events: sortBy(years, 'year').reverse(),
    max: maxEventByYear
  };
}
