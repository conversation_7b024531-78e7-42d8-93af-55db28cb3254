import get from 'lodash/get';
import { getAvatarColor } from '@/utils/avatar';
import { formatDate, getAge } from '@/utils/date';
import { prepareAllAddresses } from '@/utils/companies/addresses';
import i18n from '@/plugins/i18n';

export function formatSearchPerson(person) {
  if (!person) return null;

  const primaryAddress = person.addresses.find((address) => address.primary);
  let address = null;

  if (primaryAddress) {
    address = [
      primaryAddress.postal_code,
      primaryAddress.locality,
      primaryAddress.country.toUpperCase()
    ].join(', ');
  }

  const companiesIds = get(person, 'company_identifiers', []);

  return {
    id: person.identifier,
    initials: [person.first_name[0], person.last_name[0]].join(''),
    color: getAvatarColor(person.identifier),
    fullName: person.full_name,
    lastAppearance: formatDate(person.last_appearance, i18n.global.locale),
    title: person.title,
    birthDate: formatDate(person.birth_date, i18n.global.locale),
    age: getAge(person.birth_date),
    companiesCount: companiesIds?.length || 0,
    companiesIdentifiers: companiesIds,
    address
  };
}

export function formatPerson(person) {
  if (!person) return null;

  const addresses = prepareAllAddresses(person.addresses);
  const companiesIds = get(person, 'company_identifiers', []);

  return {
    id: person.identifier,
    initials: [person.first_name[0], person.last_name[0]].join(''),
    identifier: [person.first_name, person.last_name]
      .join('_')
      .toLocaleLowerCase(),
    color: getAvatarColor(person.identifier),
    fullName: person.full_name,
    firstName: person.first_name,
    lastName: person.last_name,
    firstAppearance: formatDate(person.first_appearance, i18n.global.locale),
    lastAppearance: formatDate(person.last_appearance, i18n.global.locale),
    title: person.title,
    gender: person.gender,
    birthDate: formatDate(person.birth_date, i18n.global.locale),
    age: getAge(person.birth_date),
    companiesCount: companiesIds.length || 0,
    companiesIdentifiers: companiesIds,
    addresses,
    profession: person.profession,
    phone: person.phone_number,
    fax: person.fax_number,
    email: person.email,
    website: person.website,
    isOrWasIndependent: person.is_or_was_independent
  };
}
