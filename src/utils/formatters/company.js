import { format } from 'timeago.js';
import i18n from '@/plugins/i18n';
import { formatDate } from '@/utils/date';
import {
  getJuridicalFormCodes,
  getJuridicalSituationCodes
} from '@/utils/code-loader';
import legalPersonTypeCodes from '@/data/legal-person-type-codes';
import { getAvatarColor, getInitials } from '@/utils/avatar';
import {
  prepareAllAddresses,
  prepareAddresses
} from '@/utils/companies/addresses';
import { findCodeName } from '@/utils/companies/codes';
import {
  findMainActivity,
  prepareActivities
} from '@/utils/companies/activities';
import { prepareCharacteristics } from '@/utils/companies/characteristics';
import { prepareAuthorizations } from '@/utils/companies/authorizations';

function prepareAbbreviation(company) {
  if (!company || !Array.isArray(company.company_names)) {
    return null;
  }

  const abbreviationObject = company.company_names.find(
    (name) => name && name.code === 'ABB'
  );

  return abbreviationObject?.name || null;
}

function prepareLegalPersonType(company) {
  if (!company) return null;

  const legalPersonType = company?.legal_person_type ?? null;

  if (!legalPersonType || !legalPersonType.code) {
    return null;
  }

  return findCodeName(legalPersonType.code, legalPersonTypeCodes);
}

async function prepareJuridicalForm(company) {
  if (!company) return null;

  const juridicalForm = company?.juridical_form ?? null;

  if (!juridicalForm || !juridicalForm.code) {
    return null;
  }

  const juridicalFormCodes = await getJuridicalFormCodes();
  return findCodeName(juridicalForm.code, juridicalFormCodes);
}

async function prepareJuridicalSituation(company) {
  if (!company) return null;

  const juridicalSituation = company?.juridical_situation?.code ?? null;

  if (!juridicalSituation) {
    return null;
  }

  const juridicalSituationCodes = await getJuridicalSituationCodes();
  return findCodeName(juridicalSituation, juridicalSituationCodes);
}

function formatVat(vat = '') {
  if (!vat || typeof vat !== 'string') {
    return '';
  }

  const code = vat.replace(/\D/g, '');
  if (code.length < 10) {
    return vat;
  }

  return `BE ${code.substring(0, 4)}.${code.substring(4, 7)}.${code.substring(
    7,
    10
  )}`;
}

export async function formatCompany(company, complete = false) {
  if (!company) return null;

  const vat = company?.vat_formatted ?? '';
  const vatClean = company?.vat_clean ?? '';

  const preparedCompany = {
    identifier: company.identifier,
    id: company.identifier,
    name: company.company_name || '',
    active: !!company.active,
    formattedName: [vat, company.company_name].filter(Boolean).join(' - '),
    vat: vatClean ? `BE_${vatClean}` : null,
    vatFormatted: vat ? vat.replace('_', ' ') : '',
    color: getAvatarColor(company.identifier),
    initials: getInitials(company.company_name || ''),
    language: company.language || null,
    isActive: !!company.active,
    age: company.start_date
      ? format(company.start_date, i18n.global.locale.value)
      : null
  };

  if (company.parent_identifier) {
    preparedCompany.parentIdentifier = company.parent_identifier;
    preparedCompany.parentCompany = formatVat(company.parent_identifier);
  }

  if (!complete) return preparedCompany;

  const { mainAddress, otherAddresses } = prepareAddresses(
    company.addresses || []
  );
  const activities = await prepareActivities(
    Array.isArray(company.activities) ? company.activities : []
  );

  return {
    ...preparedCompany,
    startDate: formatDate(company.start_date, i18n.global.locale.value),
    orginialStartDate: company.start_date || null,
    endDate: formatDate(company.end_date, i18n.global.locale.value),
    updatedAt: formatDate(company.last_update, i18n.global.locale.value),
    updatedAtOriginal: company.last_update || null,
    mainActivity: await findMainActivity(company.activities || [], true),
    firstActivity: await findMainActivity(company.activities || []),
    activities,
    characteristics: await prepareCharacteristics(
      company.characteristics || []
    ),
    authorizations: await prepareAuthorizations(company.authorizations || []),
    country: company.country || null,
    names: Array.isArray(company.company_names) ? company.company_names : [],
    addresses: prepareAllAddresses(company.addresses || []),
    mainAddress,
    otherAddresses,
    contacts: {
      phone: company.phone_number || null,
      faxNumber: company.fax || null,
      email: company.email || null,
      website: company.website || null
    },
    abbreviation: prepareAbbreviation(company),
    legalPersonType: prepareLegalPersonType(company),
    juridicalForm: await prepareJuridicalForm(company),
    juridicalSituation: await prepareJuridicalSituation(company),
    legalRepresentativePersons: company?.legal_representatives?.total ?? null,
    employees: company?.financial_summary?.employee_size ?? null,
    establishmentUnits: company.establishment_units || 0,
    grossMargin: company?.financial_summary?.gross_margin ?? null,
    financialMeta: company?.financial_meta ?? {}
  };
}

export async function formatAutocomplete(company) {
  if (!company) return null;

  const vat = company?.vat_formatted ?? '';
  const mainAddress = Array.isArray(company.addresses)
    ? company.addresses.find((addr) => addr && addr.type === 'MAIN')
    : null;

  const juridicalForm = company?.juridical_form?.code ?? null;
  const juridicalFormCodes = await getJuridicalFormCodes();
  const juridicalFormName = juridicalForm
    ? findCodeName(juridicalForm, juridicalFormCodes)
    : null;

  const mainActivity = await findMainActivity(company.activities || []);
  const mainActivityName = mainActivity
    ? [mainActivity.code, mainActivity.label, mainActivity.description]
        .filter(Boolean)
        .join(' ')
    : null;

  return {
    identifier: company.identifier,
    id: company.identifier,
    name: company.company_name || '',
    formattedName: [vat, company.company_name].filter(Boolean).join(' - '),
    vatFormatted: vat ? vat.replace('_', ' ') : null,
    color: getAvatarColor(company.identifier),
    entity: company.entity || null,
    type: company?.type?.code ?? null,
    initials: getInitials(company.company_name || ''),
    isActive: !!company.active,
    juridicalForm: juridicalFormName,
    mainActivity: mainActivityName,
    address: mainAddress
  };
}
