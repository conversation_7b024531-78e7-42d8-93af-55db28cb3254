import { findCodeName } from '@/utils/companies/codes';
import { getRolesCodes } from '@/utils/code-loader';
import { formatDate } from '../date';
import i18n from '@/plugins/i18n';

export async function formatFunction(companyFunction) {
  if (!companyFunction) return null;

  const rolesCodes = await getRolesCodes();

  return {
    ...companyFunction,
    codeName: findCodeName(companyFunction.role_code, rolesCodes),
    roleStartDate: formatDate(
      companyFunction.role_start_date,
      i18n.global.locale
    )
  };
}
