import { getAvatarColor, getInitials } from '@/utils/avatar';
import { formatDate } from '@/utils/date';
import i18n from '@/plugins/i18n-dynamic';

export default function formatOrganisation(organisation) {
  if (!organisation || !organisation.organisationShortId) return null;

  const email = organisation.email || '';

  return {
    organisationShortId: organisation.organisationShortId,
    email,
    name: organisation.name || '?',
    color: getAvatarColor(organisation.organisationShortId),
    initials: getInitials(organisation.name || '?'),
    latestUpdate: formatDate(organisation.updatedAt, i18n.global.locale)
  };
}
