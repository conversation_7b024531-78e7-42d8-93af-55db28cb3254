import legalPersonTypeCodes from '@/data/legal-person-type-codes';
import juridicalFormCodes from '@/data/juridical-form-codes';
import { findCodeName } from '@/utils/companies/codes';

export function formatSearchAggs(aggs = {}) {
  const startDates = aggs?.start_date ?? [];
  const legalPersonTypes = aggs?.legal_person_type ?? [];
  const postalCodes = aggs?.postal_code ?? [];
  const juridicalForms = aggs?.juridical_form ?? [];

  return {
    startDates: startDates.map((starDate) => ({
      value: starDate.key,
      label: starDate.key,
      count: starDate.doc_count
    })),

    legalPersonTypes: legalPersonTypes.map((legalPersonType) => ({
      value: legalPersonType.key,
      label: findCodeName(legalPersonType.key, legalPersonTypeCodes),
      count: legalPersonType.doc_count
    })),

    postalCodes: postalCodes.map((postalCode) => ({
      value: parseInt(postalCode.key, 10),
      label: parseInt(postalCode.key, 10),
      count: postalCode.doc_count
    })),

    juridicalForms: juridicalForms.map((juridicalForm) => ({
      value: juridicalForm.key,
      label: findCodeName(juridicalForm.key, juridicalFormCodes),
      count: juridicalForm.doc_count
    }))
  };
}
