import { formatDate } from '@/utils/date';
import i18n from '@/plugins/i18n';

export default function formatPublication(publication) {
  if (!publication) return null;

  const publicationTags = publication?.tags ?? [];

  return {
    identifier: publication.identifier,
    publicationDate: formatDate(
      publication.publication_date,
      i18n.global.locale
    ),
    originalDate: publication.publication_date,
    language: publication.language,
    type: publication.publication_type,
    title: publication?.title ?? '',
    tags: publicationTags
      .map((tag) => tag.keywords)
      .map((keyword) => keyword)
      .flat(),
    hasURI:
      publication.uri !== null &&
      typeof publication.uri === 'string' &&
      publication.uri.trim() !== ''
  };
}
