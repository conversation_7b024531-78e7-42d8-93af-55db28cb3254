import { formatDateWithTime } from '@/utils/date';

export default function formatAlert(alert) {
  if (!alert) return null;

  const filteredAlert = {
    alertShortId: alert.alertShortId,
    watchlistShortId: alert.watchlistShortId,
    eventShortId: alert.eventShortId,
    alertLastStatus: alert.alertLastStatus,
    comments: alert.comments,
    createdAt: alert.createdAt,
    updatedAt: alert.updatedAt,
    assignations: alert.assignations,
    companyIdentifier: alert.companyIdentifier,
    auditTrails: alert.auditTrails
  };

  const events = alert?.events;

  if (Array.isArray(events)) {
    filteredAlert.eventType = events?.[0]?.eventType ?? '';
    filteredAlert.severity = events?.[0]?.severity ?? '';
  } else {
    filteredAlert.eventType = alert?.indexedMeta?.eventType ?? '';
    filteredAlert.severity = alert?.indexedMeta?.eventSeverity ?? '';
  }

  if (!filteredAlert.comments) {
    filteredAlert.comments = [];
  }

  filteredAlert.comments.forEach((comment) => {
    comment.commentDate = formatDateWithTime(comment.commentDate);
  });

  if (!filteredAlert.assignations) {
    filteredAlert.assignations = [];
  }

  if (!filteredAlert.auditTrails) {
    filteredAlert.auditTrails = [];
  }

  filteredAlert.auditTrails.forEach((auditTrail) => {
    auditTrail.date = formatDateWithTime(auditTrail.createdAt);
  });

  return filteredAlert;
}
