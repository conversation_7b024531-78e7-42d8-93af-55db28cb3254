export function getCookie (name) {
  const value = document.cookie.match('(^|;)?' + name + '=([^;]*)(;|$)');
  return value ? decodeURIComponent(value[2]) : null;
}

export function setCookie (name, value) {
  const secure = import.meta.env.VITE_COOKIE_SECURE === 'true';
  document.cookie = `${name}=${value}; SameSite=Lax; Secure=${secure}; Expires=2592000`;
}

export function clearCookie (name) {
  document.cookie = name + '=;';
}
