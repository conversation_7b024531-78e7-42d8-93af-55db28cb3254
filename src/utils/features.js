import get from 'lodash/get';

export const BUSINESS_GRAPH = 'BUSINESS_GRAPH';
export const COMPANY_ALERTS_ADVANCED = 'COMPANY_ALERTS_ADVANCED';
export const COMPANY_ALERTS_BASIC = 'COMPANY_ALERTS_BASIC';
export const COMPANY_ALERTS_WEBHOOK = 'COMPANY_ALERTS_WEBHOOK';
export const COMPANY_EXPORT = 'COMPANY_EXPORT';
export const COMPANY_FULL_FINANCIALS = 'COMPANY_FULL_FINANCIALS';
export const COMPANY_INFO = 'COMPANY_INFO';
export const COMPANY_KEY_FINANCIALS = 'COMPANY_KEY_FINANCIALS';
export const COMPANY_LEAD_GENERATOR = 'COMPANY_LEAD_GENERATOR';
export const COMPANY_MANDATES = 'COMPANY_MANDATES';
export const COMPANY_PDF_REPORT = 'COMPANY_PDF_REPORT';
export const COMPANY_PUBLICATIONS = 'COMPANY_PUBLICATIONS';
export const COMPANY_SEARCH = 'COMPANY_SEARCH';
export const COMPANY_SEARCH_ADVANCED_FILTERS =
  'COMPANY_SEARCH_ADVANCED_FILTERS';
export const COMPANY_TIMELINE = 'COMPANY_TIMELINE';
export const COMPLIANCE_REPORT = 'COMPLIANCE_REPORT';
export const FULL_TEXT_SEARCH = 'FULL_TEXT_SEARCH';
export const MULTI_USERS = 'MULTI_USERS';
export const PERSON_SEARCH = 'PERSON_SEARCH';

export const feautures = {
  WATCH: {
    features: [
      COMPANY_ALERTS_BASIC,
      COMPANY_INFO,
      COMPANY_KEY_FINANCIALS,
      COMPANY_PUBLICATIONS,
      COMPANY_SEARCH
    ]
  },
  ANALYZE: {
    features: [
      BUSINESS_GRAPH,
      COMPANY_ALERTS_BASIC,
      COMPANY_EXPORT,
      COMPANY_FULL_FINANCIALS,
      COMPANY_INFO,
      COMPANY_KEY_FINANCIALS,
      COMPANY_PDF_REPORT,
      COMPANY_PUBLICATIONS,
      COMPANY_SEARCH_ADVANCED_FILTERS,
      COMPANY_SEARCH,
      COMPANY_TIMELINE,
      FULL_TEXT_SEARCH,
      MULTI_USERS,
      PERSON_SEARCH
    ]
  },
  SELL: {
    features: [
      BUSINESS_GRAPH,
      COMPANY_ALERTS_BASIC,
      COMPANY_EXPORT,
      COMPANY_FULL_FINANCIALS,
      COMPANY_INFO,
      COMPANY_KEY_FINANCIALS,
      COMPANY_LEAD_GENERATOR,
      COMPANY_PDF_REPORT,
      COMPANY_PUBLICATIONS,
      COMPANY_SEARCH_ADVANCED_FILTERS,
      COMPANY_SEARCH,
      COMPANY_TIMELINE,
      FULL_TEXT_SEARCH,
      MULTI_USERS,
      PERSON_SEARCH
    ]
  },
  KYC: {
    features: [
      BUSINESS_GRAPH,
      COMPANY_ALERTS_ADVANCED,
      COMPANY_ALERTS_BASIC,
      COMPANY_ALERTS_WEBHOOK,
      COMPANY_EXPORT,
      COMPANY_FULL_FINANCIALS,
      COMPANY_INFO,
      COMPANY_KEY_FINANCIALS,
      COMPANY_LEAD_GENERATOR,
      COMPANY_MANDATES,
      COMPANY_PDF_REPORT,
      COMPANY_PUBLICATIONS,
      COMPANY_SEARCH_ADVANCED_FILTERS,
      COMPANY_SEARCH,
      COMPANY_TIMELINE,
      COMPLIANCE_REPORT,
      FULL_TEXT_SEARCH,
      MULTI_USERS,
      PERSON_SEARCH
    ]
  },
  ENTERPRISE: {
    features: [
      BUSINESS_GRAPH,
      COMPANY_ALERTS_ADVANCED,
      COMPANY_ALERTS_BASIC,
      COMPANY_ALERTS_WEBHOOK,
      COMPANY_EXPORT,
      COMPANY_FULL_FINANCIALS,
      COMPANY_INFO,
      COMPANY_KEY_FINANCIALS,
      COMPANY_LEAD_GENERATOR,
      COMPANY_MANDATES,
      COMPANY_PDF_REPORT,
      COMPANY_PUBLICATIONS,
      COMPANY_SEARCH_ADVANCED_FILTERS,
      COMPANY_SEARCH,
      COMPANY_TIMELINE,
      COMPLIANCE_REPORT,
      FULL_TEXT_SEARCH,
      MULTI_USERS,
      PERSON_SEARCH
    ]
  }
};

export function featuresByLevel(level) {
  if (!level) return [];
  return get(feautures, `[${level}].features`, []);
}
