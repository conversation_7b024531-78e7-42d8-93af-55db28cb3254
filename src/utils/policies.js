import i18n from '@/plugins/i18n-dynamic';

export function checkPassword(password) {
  // at least a digit, a letter and must be a length between 8 and 32
  return /^(?=.*\d)(?=.*[A-Za-z]).{8,32}$/.test(password);
}

export function checkEmail(email) {
  return /.+@.+/.test(email);
}

export function noEmptyRules(val) {
  return String(val).trim().length > 0 || i18n.global.t('nameRuleError');
}

export function requiredField(val) {
  return !!val || i18n.global.t('requiredField');
}
