import i18n from '@/plugins/i18n-dynamic';

export function isValidDate(date) {
  return date instanceof Date && !Number.isNaN(date.getTime());
}

export function formatDate(
  date,
  locale = i18n.global.locale.value,
  options = {}
) {
  if (!date) {
    return null;
  }

  if (!isValidDate(new Date(date))) {
    return date;
  }

  // Options example
  // {
  //   weekday: 'long',
  //   year: 'numeric',
  //   month: 'long',
  //   day: 'numeric'
  // }

  return new Date(date).toLocaleDateString(locale, options);
}

export function formatDateWithTime(
  date,
  locale = i18n.global.locale.value,
  options = {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
) {
  if (!date || !isValidDate(new Date(date))) return null;

  return new Date(date).toLocaleString(locale, options);
}

export function sortByDate(array = [], reversed = false) {
  if (!Array.isArray(array) || array.length < 2) return array;

  array.sort((a, b) => {
    return new Date(a.date) - new Date(b.date);
  });

  if (!reversed) return array;

  return array.reverse();
}

export function getMonthName(
  monthIndex = null,
  locale = i18n.global.locale.value
) {
  if (monthIndex === null) return '–';

  const date = new Date();
  date.setMonth(monthIndex);
  return date.toLocaleDateString(locale, { month: 'long' });
}

export function parseFromApi(date) {
  if (!date) return null;

  const year = date.substring(0, 4);
  const month = date.substring(4, 6);
  const day = date.substring(6, 8);
  return `${year}/${month}/${day}`;
}

export function formatForApi(date) {
  if (!date) return null;

  const day = date.substring(0, 2);
  const month = date.substring(3, 5);
  const year = date.substring(6, 10);
  return `${year}${month}${day}`;
}

export function getAge(birthDate) {
  if (!birthDate || !isValidDate(new Date(birthDate))) return null;

  const today = new Date();
  const birth = new Date(birthDate);

  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();

  // Adjust age if birthday hasn't occurred this year
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }

  return age;
}
