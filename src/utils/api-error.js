import i18n from '@/plugins/i18n-dynamic';

export default function errorMessage(error) {
  const status = error?.response?.status ?? 500;
  const errorMessage = error?.response?.data ?? i18n.global.t('genericError');

  if (error?.code === 'ERR_CANCELED') {
    return {
      isCanceled: true
    };
  }

  return {
    isError: true,
    error: {
      message: errorMessage,
      status
    }
  };
}
