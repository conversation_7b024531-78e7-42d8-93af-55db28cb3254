/**
 * Dynamic loader for vis-network components
 * This ensures vis-network is only loaded when actually needed,
 * reducing the main bundle size significantly.
 */

let networkModule = null;
let dataModule = null;

/**
 * Dynamically import vis-network components
 * @returns {Promise<{Network: any, DataSet: any, DataView: any}>}
 */
export async function loadGraphComponents() {
  try {
    // Load both modules in parallel for better performance
    const [networkImport, dataImport] = await Promise.all([
      import('vis-network'),
      import('vis-data/peer')
    ]);

    networkModule = networkImport;
    dataModule = dataImport;

    return {
      Network: networkImport.Network,
      DataSet: dataImport.DataSet,
      DataView: dataImport.DataView
    };
  } catch (error) {
    console.error('Failed to load graph components:', error);
    throw new Error('Graph visualization components could not be loaded');
  }
}

/**
 * Check if graph components are already loaded
 * @returns {boolean}
 */
export function areGraphComponentsLoaded() {
  return networkModule !== null && dataModule !== null;
}

/**
 * Get already loaded components (throws if not loaded)
 * @returns {{Network: any, DataSet: any, DataView: any}}
 */
export function getLoadedGraphComponents() {
  if (!areGraphComponentsLoaded()) {
    throw new Error(
      'Graph components not loaded. Call loadGraphComponents() first.'
    );
  }

  return {
    Network: networkModule.Network,
    DataSet: dataModule.DataSet,
    DataView: dataModule.DataView
  };
}

/**
 * Preload graph components for better UX
 * Call this when user is likely to need the graph soon
 */
export function preloadGraphComponents() {
  if (!areGraphComponentsLoaded()) {
    loadGraphComponents().catch((error) => {
      console.warn('Failed to preload graph components:', error);
    });
  }
}
