export default function downloadCSV(person) {
  if (!person) return null;

  let preparedPersonData = {
    firstName: person.firstName || '-',
    lastName: person.lastName || '-',
    email: person.email || '-',
    phone: person.phone || '-',
    fax: person.fax || '-',
    website: person.website || '-',
    dataUrl: `https://pro.data.be/persons/${person.fullName}?companies=${person.companiesIdentifiers}`
  };

  if (person.address) {
    preparedPersonData.address = person.address;
  } else {
    const mainAddress = person.addresses?.filter((address) => address.primary);

    if (mainAddress) {
      preparedPersonData = {
        ...preparedPersonData,
        streetAddress: mainAddress[0].streets[0].value,
        houseNumber: mainAddress[0].street_number,
        postOfficeBoxNumber: mainAddress[0].box,
        postalCode: mainAddress[0].postal_code,
        locality: mainAddress[0].locality,
        country: mainAddress[0].country
      };
    }
  }

  const headers = Object.keys(preparedPersonData);
  const replacer = (_key, value) => (value === null ? '' : value);
  const csv = [
    headers.join(';'),
    Object.keys(preparedPersonData)
      .map((fieldName) =>
        JSON.stringify(preparedPersonData[fieldName], replacer)
      )
      .join(';')
  ].join('/r/n');

  const blob = new Blob([csv], { type: 'text' });
  const fileUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('download', `${person.fullName}.csv`);
  link.href = fileUrl;
  link.click();
}
