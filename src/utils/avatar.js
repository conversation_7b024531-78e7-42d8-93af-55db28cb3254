const DATA_IDENTIFIER = 'BE_0844044609';

export function getAvatarColor(identifier = '') {
  if (identifier === DATA_IDENTIFIER) return 'hsl(209, 60%, 50%)';

  // Extract the last three digits from the identifier for the hue
  const numericPart = identifier.replace(/\D/g, '');
  const hueDigits = numericPart.substring(numericPart.length - 3);
  const hue = Math.floor((parseInt(hueDigits, 10) / 999) * 360);

  // Set fixed values for saturation and brightness
  // const saturation = '60%';
  // const brightness = '100%';

  return `hsl(${hue || 0}, 60%, 50%)`;
}

export function getInitials(name = '') {
  if (!name) return '?';

  const names = name
    .replace('.', ' ')
    .replace(/[^a-z\u0400-\u04FF]/gi, ' ')
    .split(' ')
    .filter((part) => part !== '');

  const firstName = names[0] || '?';
  let initials = firstName.substring(0, 1).toUpperCase();

  if (!initials.length) return firstName.substring(0, 1);

  if (names.length > 1) {
    initials += names[names.length - 1].substring(0, 1).toUpperCase();
  }

  return initials;
}
