// Cache to avoid repeated fetches
let activitiesCodeCache = null;
let loadingPromise = null;

/**
 * Load activities code data from public directory
 * @returns {Promise<Object>} Activities code data
 */
export const getActivitiesCode = async () => {
  // Return cached data if available
  if (activitiesCodeCache) {
    return activitiesCodeCache;
  }

  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }

  // Create loading promise
  loadingPromise = fetchActivitiesCode();

  try {
    activitiesCodeCache = await loadingPromise;
    return activitiesCodeCache;
  } catch (error) {
    // Reset loading promise on error so it can be retried
    loadingPromise = null;
    throw error;
  } finally {
    // Clear loading promise when done
    loadingPromise = null;
  }
};

/**
 * Internal function to fetch the data
 */
async function fetchActivitiesCode() {
  try {
    // First try to fetch from public directory
    const publicPaths = [
      '/data/activities-code.json',
      `${window.location.origin}/data/activities-code.json`
    ];

    for (const path of publicPaths) {
      try {
        const response = await fetch(path);
        if (response.ok) {
          // Check if response is actually JSON by looking at content type
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            console.log(
              'Activities code data loaded successfully from public directory'
            );
            return data;
          }
        }
      } catch (error) {
        console.warn(`Failed to load from ${path}:`, error.message);
        continue;
      }
    }

    // If we reach here, all paths failed
    throw new Error(
      'Failed to load activities code data from public directory. ' +
        'Please ensure the file exists at public/data/activities-codes.json and is accessible at /data/activities-codes.json'
    );
  } catch (error) {
    console.error('Error loading activities code:', error);
    throw new Error('Failed to load activities code data');
  }
}

/**
 * Clear the cache (useful for testing or if you need to reload)
 */
export const clearActivitiesCodeCache = () => {
  activitiesCodeCache = null;
  loadingPromise = null;
};

/**
 * Check if data is already loaded
 */
export const isActivitiesCodeLoaded = () => {
  return activitiesCodeCache !== null;
};
