// Cache to avoid repeated fetches
let activitiesCodeCache = null;
let loadingPromise = null;

/**
 * Load activities code data from public directory
 * @returns {Promise<Object>} Activities code data
 */
export const getActivitiesCode = async () => {
  // Return cached data if available
  if (activitiesCodeCache) {
    return activitiesCodeCache;
  }

  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }

  // Create loading promise
  loadingPromise = fetchActivitiesCode();

  try {
    activitiesCodeCache = await loadingPromise;
    return activitiesCodeCache;
  } catch (error) {
    // Reset loading promise on error so it can be retried
    loadingPromise = null;
    throw error;
  } finally {
    // Clear loading promise when done
    loadingPromise = null;
  }
};

/**
 * Internal function to fetch the data
 */
async function fetchActivitiesCode() {
  try {
    const response = await fetch('/data/activities-code.json');

    if (!response.ok) {
      throw new Error(
        `Failed to load activities code: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log('Activities code data loaded successfully');
    return data;
  } catch (error) {
    console.error('Error loading activities code:', error);
    throw new Error('Failed to load activities code data');
  }
}

/**
 * Clear the cache (useful for testing or if you need to reload)
 */
export const clearActivitiesCodeCache = () => {
  activitiesCodeCache = null;
  loadingPromise = null;
};

/**
 * Check if data is already loaded
 */
export const isActivitiesCodeLoaded = () => {
  return activitiesCodeCache !== null;
};
