import { Notify } from 'quasar';
import i18n from '@/plugins/i18n-dynamic';

export function notify({ message, type, callback } = {}) {
  const options = {
    message,
    type
  };

  if (callback) {
    options.actions = [
      {
        label: 'close',
        color: 'white',
        handler: () => callback()
      }
    ];
  }

  if (Notify.create) {
    Notify.create(options);
  }

  return options;
}

export function notifyError(data, message) {
  notify({
    message: data?.error?.message || message || i18n.global.t('genericError'),
    type: 'negative'
  });
}
