<script setup>
import { ref } from 'vue'
import { useAuthorizationCodes, useActivitiesCode } from '@/composables/useCodeData'
import { preloadCodeData, AVAILABLE_CODE_FILES } from '@/utils/code-loader'

// Example 1: Using specific composables
const { 
  codeData: authCodes, 
  loading: authLoading, 
  error: authError,
  getFlatCodes: getAuthFlat 
} = useAuthorizationCodes()

const { 
  codeData: activitiesCodes, 
  loading: activitiesLoading, 
  error: activitiesError 
} = useActivitiesCode()

// Example 2: Manual loading without auto-load
const { 
  codeData: manualCodes, 
  loading: manualLoading, 
  loadData: loadManualData 
} = useAuthorizationCodes(false) // Don't auto-load

// Example 3: Preload all code files
const preloadAll = async () => {
  try {
    const results = await preloadCodeData(AVAILABLE_CODE_FILES)
    console.log('Preloaded all code files:', results)
  } catch (error) {
    console.error('Failed to preload:', error)
  }
}

// Example 4: Search functionality
const searchQuery = ref('')
const searchResults = ref([])

const searchAuth = () => {
  if (!authCodes.value) return
  
  const results = authCodes.value.filter(item => {
    const code = Object.keys(item)[0]
    const translations = Object.values(item)[0]
    
    return code.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
           translations.some(t => {
             const langData = Object.values(t)[0]
             return langData.label?.toLowerCase().includes(searchQuery.value.toLowerCase())
           })
  })
  
  searchResults.value = results
}
</script>

<template>
  <div class="code-data-example">
    <h2>Code Data Loading Examples</h2>
    
    <!-- Example 1: Auto-loaded data -->
    <div class="example-section">
      <h3>1. Auto-loaded Authorization Codes</h3>
      <div v-if="authLoading">Loading authorization codes...</div>
      <div v-else-if="authError" class="error">Error: {{ authError }}</div>
      <div v-else-if="authCodes">
        <p>Loaded {{ authCodes.length }} authorization codes</p>
        <div class="code-list">
          <div v-for="(item, index) in authCodes.slice(0, 3)" :key="index" class="code-item">
            <strong>{{ Object.keys(item)[0] }}</strong>: 
            {{ Object.values(item)[0][0]?.nl?.label || 'No label' }}
          </div>
          <div v-if="authCodes.length > 3">... and {{ authCodes.length - 3 }} more</div>
        </div>
      </div>
    </div>

    <!-- Example 2: Manual loading -->
    <div class="example-section">
      <h3>2. Manual Loading</h3>
      <button @click="loadManualData" :disabled="manualLoading">
        {{ manualLoading ? 'Loading...' : 'Load Authorization Codes Manually' }}
      </button>
      <div v-if="manualCodes">
        <p>Manually loaded {{ manualCodes.length }} codes</p>
      </div>
    </div>

    <!-- Example 3: Preload all -->
    <div class="example-section">
      <h3>3. Preload All Code Files</h3>
      <button @click="preloadAll">Preload All Code Files</button>
      <p>Check console for results</p>
    </div>

    <!-- Example 4: Search -->
    <div class="example-section">
      <h3>4. Search Authorization Codes</h3>
      <div class="search-box">
        <input 
          v-model="searchQuery" 
          @input="searchAuth"
          placeholder="Search authorization codes..."
          class="search-input"
        />
      </div>
      <div v-if="searchResults.length" class="search-results">
        <h4>Search Results ({{ searchResults.length }})</h4>
        <div v-for="(item, index) in searchResults.slice(0, 5)" :key="index" class="code-item">
          <strong>{{ Object.keys(item)[0] }}</strong>: 
          {{ Object.values(item)[0][0]?.nl?.label || 'No label' }}
        </div>
      </div>
    </div>

    <!-- Example 5: Flat codes for dropdowns -->
    <div class="example-section">
      <h3>5. Flat Codes for Dropdowns</h3>
      <div v-if="!authLoading && authCodes">
        <select class="code-select">
          <option value="">Select an authorization...</option>
          <option 
            v-for="item in getAuthFlat('nl').slice(0, 10)" 
            :key="item.code" 
            :value="item.code"
          >
            {{ item.code }} - {{ item.label }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<style scoped>
.code-data-example {
  padding: 20px;
  max-width: 800px;
}

.example-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.code-list, .search-results {
  margin-top: 10px;
}

.code-item {
  padding: 5px;
  margin: 2px 0;
  background: #f5f5f5;
  border-radius: 3px;
  font-size: 14px;
}

.error {
  color: red;
  font-weight: bold;
}

.search-input, .code-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #0056b3;
}
</style>
