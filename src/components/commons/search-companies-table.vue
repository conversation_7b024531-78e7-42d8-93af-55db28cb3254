<script setup>
import { ref } from 'vue';

const props = defineProps({
  loading: <PERSON>olean,
  companies: Array,
  columns: Array
});

const emit = defineEmits(['paginate', 'remove']);

const pagination = ref({
  rowsPerPage: 10,
  page: 1,
  rowsNumber: props.companies.length || 0
});

function paginate(options) {
  pagination.value = options.pagination;
  emit('paginate', options.pagination);
}
</script>

<template>
  <q-card bordered flat class="q-mt-md">
    <q-table
      :loading="loading"
      :columns="columns"
      :rows="companies"
      row-key="id"
      flat
      v-model:pagination="pagination"
      :rows-per-page-options="[10, 50]"
      @request="paginate"
    >
      <template v-slot:loading>
        <q-inner-loading showing color="primary" />
      </template>

      <template #body-cell-avatar="props">
        <q-td :props="props">
          <router-link :to="`/companies/${props.row.identifier}`">
            <q-avatar
              size="md"
              :style="{
                color: 'var(--white)',
                background: props.row.color
              }"
            >
              {{ props.row.initials }}
            </q-avatar>
          </router-link>
        </q-td>
      </template>

      <template #body-cell-action="props">
        <q-td :props="props">
          <q-btn
            :label="$q.screen.lt.lg ? '' : $t('company')"
            icon="fa-regular fa-arrow-up-right-from-square"
            size="sm"
            flat
            color="primary"
            :to="`/companies/${props.row.identifier}`"
          />

          <q-btn
            :label="$q.screen.lt.lg ? '' : $t('remove')"
            icon="fa-regular fa-trash-can"
            size="sm"
            flat
            color="red"
            @click="$emit('remove', props.row.identifier)"
          />
        </q-td>
      </template>
    </q-table>
  </q-card>
</template>
