<script setup>
import { computed } from 'vue';
import { AppStore } from '@/store';
import { useQuasar } from 'quasar';

const appStore = AppStore();
const $q = useQuasar();
const isDarkMode = computed(() => appStore.theme === 'dark');
const switchTheme = (value) => {
  appStore.setTheme(value);
  $q.dark.set(value === 'dark');
};
</script>

<template>
  <div class="dark-mode-toggle">
    <q-btn
      :unelevated="isDarkMode"
      :color="isDarkMode ? '' : 'white'"
      class="dark-mode-toggler"
      size="xs"
      @click="switchTheme('light')"
    >
      <q-icon
        :color="isDarkMode ? 'grey' : 'grey-dark'"
        name="fa-regular fa-sun-bright"
      />
    </q-btn>
    <q-btn
      :unelevated="!isDarkMode"
      :color="isDarkMode ? 'black' : ''"
      size="xs"
      class="dark-mode-toggler"
      @click="switchTheme('dark')"
    >
      <q-icon
        :color="isDarkMode ? 'grey-dark' : 'grey'"
        name="fa-regular fa-moon-stars"
      />
    </q-btn>
  </div>
</template>

<style scoped>
.dark-mode-toggle {
  display: flex;
  padding: 0.25rem;
  background-color: var(--primarySmoke);
  border-radius: 0.25rem;
  border: 1px solid var(--border);
  gap: 0.5rem;
}

.dark-mode-toggler {
  padding: 0.5rem;
}
</style>
