<script setup>
const props = defineProps({ vat: String });
const sources = [
  {
    id: 'kbo',
    title: 'Crossroads Bank for Companies',
    url: props.vat
      ? `https://data.be/en/company/official/${props.vat}/kbo-bce`
      : 'https://kbopub.economie.fgov.be/kbopub/zoeknummerform.html',
    img: '/kbo.png'
  },
  {
    id: 'fod',
    title: 'SPF Justice',
    url: props.vat
      ? `https://data.be/en/company/official/${props.vat}/staatsblad-moniteur`
      : 'http://justitie.belgium.be',
    img: '/fod.png'
  },
  {
    id: 'nbb',
    title: 'National Bank',
    url: props.vat
      ? `https://data.be/en/company/official/${props.vat}/nbb-bnb`
      : 'https://www.nbb.be',
    img: '/nbb.png'
  },
  {
    id: 'rsz',
    title: 'Social Security',
    url: props.vat
      ? `https://employer-identification-consult.socialsecurity.be/employer/enterprise/${props.vat?.substring(
          3
        )}`
      : 'https://www.socialsecurity.be',
    img: '/rsz.png'
  }
];
</script>

<template>
  <div class="sources">
    <a
      v-for="source in sources"
      :key="source.id"
      :href="source.url"
      :title="source.title"
      class="sources-link"
      target="_blank"
      rel="nofollow"
    >
      <img class="source-img" :src="source.img" :alt="source.title" />
    </a>
  </div>
</template>

<style scoped>
.sources {
  --imgSize: 64px;

  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  max-width: calc(var(--imgSize) * 4);
  width: 100%;
}

.sources-link {
  display: block;
  flex-shrink: 1;
  flex-grow: 0;
  width: 25%;
  max-width: var(--imgSize);
  height: auto;
  filter: saturate(0);
  transition: filter 300ms ease-in;
}

.sources-link:hover {
  filter: saturate(1);
}

.source-img {
  width: 100%;
  height: auto;
}

.body--dark .source-img {
  filter: contrast(0.75) invert();
}
</style>
