<template>
  <div class="breadcrumbs row no-wrap items-center">
    <template v-for="(link, index) in links" :key="link.label">
      <router-link
        v-if="link.path"
        class="crumb text-h6 q-ma-none text-bold text-blue-grey"
        :to="link.path"
      >
        {{ link.label }}
      </router-link>

      <h1 v-else class="crumb text-h6 q-ma-none text-bold">
        {{ link.label }}
      </h1>

      <q-icon
        v-if="index + 1 < links.length"
        name="fa-regular fa-chevron-right"
        class="text-blue-grey q-mx-md"
      />
    </template>
  </div>
</template>

<script setup>
defineProps({ links: Array });
</script>

<style scoped>
.breadcrumbs {
  padding-top: 0.75rem;
  flex-grow: 1;
}

.crumb {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-bottom: 0.25rem;
}

@media (min-width: 600px) {
  .crumb {
    max-width: 25%;
  }
}
</style>
