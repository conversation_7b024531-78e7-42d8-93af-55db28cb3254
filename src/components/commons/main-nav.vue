<template>
  <q-drawer
    :model-value="isNavOpen"
    :mini="isNavCollapsed"
    persistent
    :overlay="isMobile"
    :breakpoint="600"
    ref="mainNav"
    class="main-nav-drawer"
    bordered
    @update:model-value="$emit('toggle')"
  >
    <div :class="['main-nav q-py-md', { 'q-px-lg': !isNavCollapsed }]">
      <div
        :class="[
          'flex justify-between items-center',
          { 'q-pt-md': isNavCollapsed }
        ]"
      >
        <img
          v-if="!isNavCollapsed"
          :src="$q.dark.isActive ? logoNeg : logo"
          class="logo q-my-md"
          alt="Data.be"
        />

        <q-btn
          v-if="!isMobile"
          flat
          round
          class="grow-0 q-mx-sm"
          @click="onToggleCollapse"
        >
          <q-icon
            name="fa-regular fa-bars"
            :color="isNavCollapsed ? '' : 'grey-light'"
            size="xs"
          />
        </q-btn>
      </div>

      <div class="q-py-md">
        <SidebarSearch
          :collapsed="isNavCollapsed"
          @toggle-collapse="isNavCollapsed = !isNavCollapsed"
        />
      </div>

      <q-list class="main-nav-list" padding role="navigation">
        <q-item-label header>{{ $t('applications') }}</q-item-label>
        <template
          v-for="([icon, text, link, children, disabled], index) in appLinks"
          :key="icon"
        >
          <q-item
            v-if="children && children.length === 0"
            :to="link"
            :disable="disabled"
            :exact="index === 0"
          >
            <q-item-section avatar>
              <q-icon :name="icon" size="xs" />
            </q-item-section>
            <q-item-section class="main-nav-item-title">
              <q-item-label>
                {{ text }}
              </q-item-label>
            </q-item-section>
          </q-item>

          <!-- SUB NAV items -->
          <q-expansion-item
            v-else
            v-model="expanded"
            :to="isNavCollapsed ? link : null"
            hide-expand-icon
          >
            <template #header>
              <q-item-section avatar>
                <q-icon :name="icon" />
              </q-item-section>
              <q-item-section class="main-nav-item-title">
                {{ text }}
              </q-item-section>
              <q-item-section side>
                <q-icon
                  :name="expanded ? 'fa-minus' : 'fa-plus'"
                  color="grey"
                  size="xs"
                />
              </q-item-section>
            </template>

            <q-item
              v-for="[childTitle, childTo] in children"
              :key="childTitle"
              :to="childTo"
              class="main-nav-group-item"
              clickable
            >
              <q-item-section class="main-nav-item-title">
                {{ childTitle }}
              </q-item-section>
            </q-item>
          </q-expansion-item>
        </template>
      </q-list>

      <!-- SOLUTIONS -->
      <div v-show="!isNavCollapsed" class="static-links">
        <q-list padding class="static-links-list">
          <q-expansion-item
            :label="$t('solutions')"
            expand-separator
            dense
            expand-icon="fa-regular fa-plus"
            expand-icon-class="list-collapse-icon"
            header-class="q-item__label--header"
          >
            <!-- <q-item-label header>{{ $t('solutions') }}</q-item-label> -->
            <q-item
              v-for="[icon, title, href] in solutions"
              :key="title"
              :href="href"
              target="_blank"
            >
              <q-item-section avatar>
                <q-icon :name="icon" size="xs" />
              </q-item-section>
              <q-item-section class="main-nav-item-title">
                {{ title }}
              </q-item-section>
            </q-item>
          </q-expansion-item>
        </q-list>
      </div>

      <q-list class="settings-nav-list" color="secondary">
        <q-item to="/settings">
          <q-item-section avatar>
            <q-icon name="fa-regular fa-gear" size="xs" />
          </q-item-section>
          <q-item-section>{{ $t('settings') }}</q-item-section>
        </q-item>

        <!-- <q-item v-if="!isNavCollapsed">
          <q-item-section avatar>
            <q-icon name="fa-regular fa-palette" size="xs" />
          </q-item-section>
          <q-item-section>{{ $t('interface') }}</q-item-section>
          <q-item-section side>
            <DarkModeToggle />
          </q-item-section>
        </q-item> -->
      </q-list>

      <UserInfos v-if="!isNavCollapsed" class="main-nav-user" />
    </div>
  </q-drawer>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import logo from '@/assets/logo.svg';
import logoNeg from '@/assets/logo-neg.svg';
import { COMPANY_ALERTS_ADVANCED } from '@/utils/features';
import { AppStore } from '@/store';
import { AuthStore } from '@/pages/auth/store';
import SidebarSearch from '@/components/commons/sidebar-search.vue';
import UserInfos from '@/components/commons/user-infos.vue';
// import DarkModeToggle from '@/components/commons/dark-mode-toggle.vue';

const props = defineProps({
  isNavOpen: Boolean,
  isMobile: Boolean
});

const { t } = useI18n();
const authStore = AuthStore();
const appStore = AppStore();
const { features } = storeToRefs(authStore);
const { isNavCollapsed } = storeToRefs(appStore);

const appLinks = computed(() => {
  // [icon, text, link, children, disabled]
  const links = [
    ['fa-regular fa-chart-line', t('dashboard'), '/', []],
    ['fa-regular fa-buildings', t('companies'), '/companies', []],
    ['fa-regular fa-newspaper', t('publications'), '/publications', []],
    ['fa-regular fa-users', t('persons'), '/persons', []]
  ];

  if (features.value.includes(COMPANY_ALERTS_ADVANCED)) {
    links.push([
      'fa-regular fa-monitor-waveform',
      t('monitoring'),
      '/monitoring',
      []
    ]);
  } else {
    links.push(['fa-regular fa-list', t('lists'), '/lists', []]);
  }

  links.push([
    'fa-regular fa-chart-network',
    t('businessGraph'),
    '/business-graph',
    []
  ]);

  return links;
});

const solutions = [
  [
    'fa-regular fa-chart-pie-simple',
    t('companyInsights'),
    'https://data.be/en/content/sales-marketing'
  ],
  [
    'fa-regular fa-heart-pulse',
    t('risk'),
    'https://data.be/en/content/risk-management'
  ],
  [
    'fa-regular fa-ballot-check',
    t('compliance'),
    'https://data.be/en/content/compliance-automation'
  ],
  ['fa-regular fa-webhook', 'API', 'https://data.be/en/content/products/api'],
  [
    'fa-regular fa-address-card',
    t('kycAndMandates'),
    'https://data.be/en/mandate'
  ]
];

const expanded = ref(false);

function onToggleCollapse() {
  appStore.setNavCollapse(!isNavCollapsed.value);
}

watch(
  () => props.isMobile,
  (isMobile) => {
    if (isMobile) {
      appStore.setNavCollapse(false);
    }
  }
);

onMounted(() => {
  if (props.isMobile) {
    appStore.setNavCollapse(false);
  }
});
</script>

<style scoped>
.main-nav {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: calc(100dvh - 1px);
  height: 100%;
  overflow: hidden;
}

.logo {
  max-width: 150px;
  width: 100%;
}

.main-nav-list {
  flex-shrink: 0;
  flex-grow: 1;
}

.main-nav-group-item {
  padding-left: 4.5rem;
}

.main-nav-item-title {
  font-size: 1rem;
}

.static-links {
  display: grid;
  align-items: center;
  flex-shrink: 0;
}

.settings-nav-list {
  flex-shrink: 0;
}

.main-nav-user {
  flex-shrink: 0;
  gap: 1rem;
}
</style>

<style>
.list-collapse-icon i {
  font-size: 1rem !important;
}
.main-nav .main-nav-search .q-field__control::before {
  border-color: transparent;
}

.main-nav .main-nav-search:hover .q-field__control::before {
  border-color: var(--grey);
}
</style>
