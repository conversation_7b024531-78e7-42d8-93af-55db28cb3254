<script setup>
import { computed } from 'vue';

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  vat: {
    type: String,
    default: ''
  },
  companyType: {
    type: String,
    default: ''
  },
  isActive: {
    type: Boolean,
    default: false
  },
  juridicalForm: {
    type: String,
    default: ''
  },
  mainActivity: {
    type: String,
    default: ''
  },
  address: {
    type: String,
    default: ''
  },
  entity: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  withLink: Boolean
});

const iconsByType = {
  NAT: 'fa-building-user',
  JUR: 'fa-buildings',
  establishment: 'fa-building',
  person: 'fa-user'
};

const colorByType = {
  company: 'blue',
  establishment: 'green',
  person: 'orange'
};

const link = computed(() => {
  if (!props.withLink) return null;
  const path = props.entity === 'establishment' ? 'establishments/' : '';
  return `/companies/${path}${props.id}`;
});
</script>

<template>
  <q-item :to="link">
    <q-item-section avatar>
      <q-avatar
        :color="isActive ? `${colorByType[entity]}-1` : 'grey-3'"
        size="md"
      >
        <q-icon
          :name="`fa-regular ${
            entity === 'company' ? iconsByType[type] : iconsByType[entity]
          }`"
          :color="isActive ? colorByType[entity] : 'grey-6'"
        />
      </q-avatar>
    </q-item-section>
    <q-item-section>
      <q-item-label overline class="text-uppercase text-bold">
        {{ name }}
        <q-chip v-if="!isActive" size="xs">
          {{ $t('inactive') }}
        </q-chip>
      </q-item-label>
      <q-item-label caption>
        <div class="row">
          <span v-show="juridicalForm">
            {{ juridicalForm }}
          </span>
          <span v-show="juridicalForm && mainActivity" class="q-px-sm">–</span>
          <span v-show="mainActivity">{{ mainActivity }}</span>
        </div>
      </q-item-label>
      <q-item-label caption>
        <div class="row">
          <span v-show="vat">{{ vat }}</span>
          <span v-show="vat && address" class="q-px-sm">–</span>
          <span v-show="address">{{ address }}</span>
        </div>
      </q-item-label>
    </q-item-section>

    <q-item-section side>
      <slot />

      <q-icon
        v-if="withLink"
        color="primary"
        name="fa-regular fa-chevron-right"
        size="xs"
      />
    </q-item-section>
  </q-item>
</template>
