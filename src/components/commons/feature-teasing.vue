<template>
  <div class="feature-teasing">
    <q-card :class="['feature-teasing-box', { condenced }]">
      <q-card-section>
        <div class="text-h6">
          {{ $t(`features.${feature}.title`) }}
        </div>
      </q-card-section>

      <q-card-section>
        <p>{{ $t(`features.${feature}.body`) }}</p>
      </q-card-section>

      <q-card-actions vertical align="center">
        <q-btn
          :label="$t('learnMore')"
          :href="`${accountURL}/pricing?token=${token}`"
          target="_blank"
          color="primary"
        />
      </q-card-actions>
    </q-card>

    <span v-if="overlay" class="feature-teasing-overlay" />
  </div>
</template>

<script setup>
import { getAccountUrl } from '@/utils/account-url';
import { getCookie } from '@/utils/cookies';

defineProps({
  feature: String,
  condenced: Boolean,
  overlay: Boolean
});

const accountURL = getAccountUrl(window.location.hostname);
const token = getCookie('token');
</script>

<style scoped>
.feature-teasing {
  width: 100%;
  height: 100%;
}

.feature-teasing-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.feature-teasing-box.condenced {
  width: 95%;
  max-width: 100%;
}

.feature-teasing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
