<script setup>
import { ref } from 'vue';
import { clickOutSide as vClickOutSide } from '@mahdikhashan/vue3-click-outside';
import CompanyAutocompleteItem from '@/components/commons/company-autocomplete-item.vue';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  results: {
    type: Array,
    default: () => []
  },
  selection: {
    type: Array,
    default: () => []
  },
  isNew: Boolean
});

const emit = defineEmits(['search', 'clear', 'add', 'remove']);

const searchInput = ref('');
const displaySearchResults = ref(false);

function toggleSearchResults(value = false) {
  if (value && props.results.length > 0) {
    displaySearchResults.value = true;
    return;
  }

  displaySearchResults.value = false;
}

function clearSearch() {
  emit('clear');
}

function search(searchInput) {
  if (searchInput === null || searchInput.trim().length === 0) {
    displaySearchResults.value = false;
    clearSearch();
    return;
  }

  emit('search', searchInput);
  displaySearchResults.value = true;
}

function isSelected(id) {
  return props.selection.findIndex((selectionId) => selectionId === id) !== -1;
}

function add(companyId) {
  emit('add', companyId);
  displaySearchResults.value = false;
}

function remove(companyId) {
  emit('remove', companyId);
  displaySearchResults.value = false;
}
</script>

<template>
  <div class="companies-search" v-click-out-side="() => toggleSearchResults()">
    <q-input
      v-model="searchInput"
      :loading="loading"
      :placeholder="$t('searchForCompanies')"
      :disable="isNew"
      debounce="500"
      clearable
      rounded
      outlined
      dense
      class="search-companies-input"
      @clear="clearSearch"
      @update:model-value="search"
      @focus="toggleSearchResults(true)"
    >
      <template #prepend>
        <q-icon name="fa-regular fa-magnifying-glass" size="xs" />
      </template>
    </q-input>

    <div class="search-result-container">
      <transition name="fade">
        <div v-if="displaySearchResults" class="search-results shadow-1">
          <div v-show="loading" class="q-pa-lg">
            <q-inner-loading :showing="loading" color="primary" />
          </div>

          <q-list v-if="!loading" style="min-width: 100%" separator>
            <q-item v-if="results.length === 0">
              <q-item-section>{{ $t('noCompaniesFound') }}</q-item-section>
            </q-item>

            <CompanyAutocompleteItem
              v-for="company in results"
              :key="company.id"
              :id="company.id"
              :name="company.name"
              :vat="company.vatFormatted"
              :is-active="company.isActive"
              :juridical-form="company.juridicalForm"
              :main-activity="company.mainActivity"
              :address="company.address"
              :type="company.type"
              :entity="company.entity"
            >
              <!--  REMOVE -->
              <q-btn
                v-if="isSelected(company.id)"
                :label="$q.screen.lt.lg ? '' : $t('remove')"
                icon="fa-regular fa-trash-can"
                color="red"
                outline
                @click.stop="remove(company.id)"
              />

              <!-- ADD -->
              <q-btn
                v-else
                :label="$q.screen.lt.lg ? '' : $t('add')"
                icon="fa-regular fa-plus"
                color="primary"
                outline
                @click.stop="add(company.id)"
              />
            </CompanyAutocompleteItem>
          </q-list>
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.companies-search {
  flex-grow: 1;
}

.search-companies-input {
  overflow: hidden;
  border-radius: 1.5rem;
}

.search-result-container {
  position: relative;
}

.search-results {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--white);
  z-index: 1;
  padding: 1rem;
  border-radius: 0.25rem;
  max-height: 400px;
  overflow-y: auto;
}
</style>
