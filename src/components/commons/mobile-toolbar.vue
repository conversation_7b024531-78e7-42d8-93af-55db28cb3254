<script setup>
import logoNeg from '@/assets/logo-neg.svg';
import UserInfos from '@/components/commons/user-infos.vue';
</script>

<template>
  <q-header reveal>
    <q-toolbar class="bg-primary text-white">
      <q-btn flat round dense @click="$emit('toggle')">
        <q-icon name="fa-regular fa-bars" />
      </q-btn>

      <q-toolbar-title>
        <img :src="logoNeg" alt="Data.be" class="toolbar-logo" />
      </q-toolbar-title>

      <UserInfos dense />
    </q-toolbar>
  </q-header>
</template>

<style scoped>
.toolbar-logo {
  width: auto;
  height: 30px;
}
</style>
