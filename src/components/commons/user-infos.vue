<script setup>
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { AuthStore } from '@/pages/auth/store';

defineProps({ dense: Boolean });

const authStore = AuthStore();
const router = useRouter();
const { user } = storeToRefs(authStore);

const logout = () => {
  authStore.logout();
  router.push({ name: 'login' });
};
</script>

<template>
  <div v-if="user" class="row items-center q-pa-md text-body-2">
    <template v-if="!dense">
      <q-avatar
        size="48"
        :style="{ color: 'var(--white)', background: user.color }"
        class="q-mr-md"
      >
        {{ user.initials }}
      </q-avatar>
      <div>
        <div>{{ user.email }}</div>
        <q-btn color="grey" outline size="xs" @click="logout">
          {{ $t('logout') }}
        </q-btn>
      </div>
    </template>

    <q-btn v-if="dense" flat>
      <q-avatar
        size="48"
        :style="{ color: 'var(--white)', background: user.color }"
      >
        {{ user.initials }}
      </q-avatar>

      <q-menu>
        <q-list>
          <q-item>
            <q-item-section>
              <q-item-label>{{ user.name }}</q-item-label>
              <q-item-label caption>{{ user.email }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item clickable @click="logout">
            <q-item-section class="text-red">
              {{ $t('logout') }}
            </q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-btn>
  </div>
</template>
