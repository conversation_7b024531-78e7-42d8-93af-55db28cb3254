<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

defineProps({ collapsed: Boolean });

const emit = defineEmits(['toggle-collapse']);
const router = useRouter();
const searchInput = ref();

const search = () => {
  router.push(`/search/companies?q=${searchInput.value}`);
};

const toggleCollapse = function () {
  emit('toggle-collapse');
};

function clear() {
  searchInput.value = null;
}
</script>

<template>
  <div class="siderbar-search">
    <q-btn
      v-if="collapsed"
      flat
      round
      @click="toggleCollapse"
      class="q-mx-sm"
      icon="fa-regular fa-magnifying-glass"
    />

    <q-input
      v-else
      ref="sidebarSearch"
      :bg-color="$q.dark.isActive ? 'blue-grey-9' : 'blue-grey-1'"
      placeholder="Search..."
      class="main-nav-search"
      rounded
      outlined
      clearable
      debounce="500"
      clear-icon="fa-solid fa-circle-xmark"
      v-model="searchInput"
      @keyup.enter="search"
      @clear="clear"
    >
      <template v-slot:prepend>
        <q-btn
          flat
          round
          icon="fa-regular fa-magnifying-glass"
          size="sm"
          @click="search"
        />
      </template>
    </q-input>
  </div>
</template>

<style scoped>
.siderbar-search {
  position: relative;
}

.sidebar-search-menu {
  min-width: 250px;
}
</style>
