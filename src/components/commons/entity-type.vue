<script setup>
import { computed } from 'vue';

defineProps({
  type: {
    type: String,
    default: 'COMPANY'
  }
});

const iconByType = computed(() => {
  const mapping = {
    COMPANY: 'fa-regular fa-building',
    PERSON: 'fa-regular fa-user'
  };

  return mapping[this.type];
});
</script>

<template>
  <div>
    <q-icon :name="iconByType" color="primary" class="q-mr-sm" />
    <span class="text-primary text-bold text-uppercase">{{ type }}</span>
  </div>
</template>
