<script setup>
// import DarkModeToggle from '@/components/commons/dark-mode-toggle.vue';
</script>

<template>
  <div class="no-auth-pages">
    <div class="center-content">
      <slot />
    </div>

    <!-- <DarkModeToggle class="darkModeToggle shadow-1" /> -->

    <footer class="text-caption pa-2">
      <ul class="footer-links">
        <li>
          &copy; 2012-{{ new Date().getFullYear() }} {{ $t('dataCopyrights') }}
        </li>
        <li>{{ $t('dataTVA') }}</li>
        <li>
          <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
        </li>
        <li>
          <a href="https://data.be/fr/content/legal" target="_blank">
            {{ $t('legalNotices') }}</a
          >
        </li>
        <li>
          <a href="https://data.be/fr/content/legal-api" target="_blank">
            {{ $t('apiTerms') }}
          </a>
        </li>
        <li>
          <a href="https://data.be/fr/content/disclaimer" target="_blank">
            {{ $t('disclaimer') }}
          </a>
        </li>
      </ul>
    </footer>
  </div>
</template>

<style scoped>
.no-auth-pages {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100dvh;
}

.center-content {
  display: grid;
  align-content: center;
  justify-content: center;
  height: 100%;
  flex-grow: 1;
}

.darkModeToggle {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
}

.footer-links {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--black);
  flex-wrap: wrap;
}

.footer-links > li {
  display: inline-block;
  padding: 0 1rem;
  position: relative;
}

.footer-links > li:not(:last-child)::after {
  content: '';
  position: absolute;
  width: 0.5rem;
  height: 0.0125rem;
  background: var(--black);
  right: -0.25rem;
  top: calc(50% - 0.0125rem);
}

.footer-links > a {
  color: var(--black);
}
</style>
