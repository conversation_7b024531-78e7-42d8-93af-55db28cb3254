<template>
  <mainNav
    :is-nav-open="isNavOpen"
    :is-mobile="isMobile"
    @toggle="onToggleNav"
  />

  <q-page-container>
    <mobileToolbar v-if="isMobile" @toggle="onToggleNav" />

    <q-page :padding="$route.meta && $route.meta.noPadding ? false : true">
      <router-view :is-mobile="isMobile" />
    </q-page>
  </q-page-container>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import mainNav from '@/components/commons/main-nav.vue';
import mobileToolbar from '@/components/commons/mobile-toolbar.vue';

const $q = useQuasar();
const isMobile = ref($q.platform.is.mobile || $q.screen.lt.md);
const isNavOpen = ref(!isMobile.value);

window.addEventListener('resize', () => {
  isMobile.value = $q.platform.is.mobile || $q.screen.lt.md;
  isNavOpen.value = !isMobile.value;

  if (!isMobile.value) {
    document.body.classList.remove('q-body--prevent-scroll');
  }
});

function onToggleNav() {
  isNavOpen.value = !isNavOpen.value;
}
</script>
