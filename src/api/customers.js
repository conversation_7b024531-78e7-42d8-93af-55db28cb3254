import { httpClient } from '@/api/axios';
import formatCustomer from '@/utils/formatters/customer';
import formatOrganisation from '@/utils/formatters/organisation';
import errorMessage from '@/utils/api-error';

export default {
  async byOrganisation(organisationShortId) {
    try {
      const { data } = await httpClient.get(
        `/customers/organisationShortId=${organisationShortId}`
      );

      return data.map(formatCustomer);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async byId(customerShortId) {
    try {
      const { data } = await httpClient.get(`/pro/customers/${customerShortId}`);

      return formatCustomer(data[0]);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getOrganisation(organisationShortId) {
    if (!organisationShortId) return;

    try {
      const { data } = await httpClient.get(
        `/pro/organisations/${organisationShortId}`
      );
      const organisation = Array.isArray(data) ? data[0] : data;
      return formatOrganisation(organisation);
    } catch (error) {
      return errorMessage(error);
    }
  }
};
