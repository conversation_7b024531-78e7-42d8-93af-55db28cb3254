import { httpClient } from '@/api/axios';
import errorMessage from '@/utils/api-error';
import {
  formatPublicationSearch,
  formatAggs
} from '@/utils/formatters/publication-search';

let searchAbortController = new AbortController();
let aggsAbortController = new AbortController();

export default {
  async search(params) {
    if (!params) return;

    try {
      const { abort, ...rest } = params;
      const { data } = await httpClient.post('/2.1/publications/search', rest, {
        signal: searchAbortController.signal
      });

      if (params.abort) {
        searchAbortController.abort();
        searchAbortController = new AbortController();
      }

      return {
        results: await Promise.all(
          data.publications.map(formatPublicationSearch)
        ),
        total: data.total
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async aggregations(params = {}) {
    try {
      const { abort, ...rest } = params;
      const { data } = await httpClient.post(
        '/2.1/publications/search/aggregations',
        {
          ...rest,
          terms_aggs: {
            company_identifier: 200,
            publication_company_denomination: 200,
            notary: 200,
            auditor: 200,
            other_function: 200,
            function: 200,
            postal_code: 200,
            juridical_form: 200
          }
        },
        { signal: aggsAbortController.signal }
      );

      if (params.abort) {
        aggsAbortController.abort();
        aggsAbortController = new AbortController();
      }

      return await formatAggs(data.aggregations);
    } catch (error) {
      return errorMessage(error);
    }
  }
};
