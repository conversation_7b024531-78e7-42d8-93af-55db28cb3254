import { clearCookie, get<PERSON><PERSON>ie, setCookie } from '@/utils/cookies';
import axios from 'axios';
import { getAccountUrl } from '@/utils/account-url';

const accountURL = getAccountUrl(window.location.hostname);

const getToken = () => {
  return getCookie('token');
};

// From location
const getEndpoint = () => {
  const { hostname } = window.location;
  switch (hostname) {
    case 'localhost':
      return 'https://api-staging.cluster.data.be';
    case 'pro-preview.data.be':
      return 'https://api-staging.cluster.data.be';
    case 'pro.data.be':
    default:
      return 'https://api.data.be';
  }
};

export const httpClient = axios.create({
  baseURL: getEndpoint(),
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json'
  }
});

const token = getToken();

if (token) {
  httpClient.defaults.headers.common.Authorization = `Bearer ${token}`;
}

const setToken = (token) => {
  setCookie('token', token);
  httpClient.defaults.headers.common.Authorization = `Bearer ${token}`;
};

const clearToken = () => {
  clearCookie('token');
  delete httpClient.defaults.headers.common.Authorization;
};

// TODO FIXME discuss this + error handling
// httpClient.interceptors.response.use(
//   (response) => response,
//   (error) => {
//     if (error.response.status === 401) {
//       // remove invalid token
//       // remove user
//       // go to login page // logout
//       clearToken();
//     }
//     return error;
//   }
// );

// httpClient.defaults.headers.common['Cache-Control'] = 'no-cache';

httpClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Redirect to account for payment
    if (error.response.status === 402) {
      window.location.href = `${accountURL}/pricing?token=${token}`;
    }
    return error;
  }
);

export default {
  httpClient,
  getToken,
  setToken,
  clearToken
};
