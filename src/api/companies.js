import last from 'lodash/last';
import { httpClient } from '@/api/axios';
import { formatCompany, formatAutocomplete } from '@/utils/formatters/company';
import formatPublication from '@/utils/formatters/publication';
import { formatStatement, extractOverview } from '@/utils/formatters/statement';
import errorMessage from '@/utils/api-error';
import { formatFunction } from '@/utils/formatters/functions';
import { formatEstablishment } from '@/utils/formatters/establishment';
import { formatCompanyEvents } from '@/utils/formatters/company-events';
import { formatSearchAggs } from '@/utils/formatters/search';
import { getAvatarColor, getInitials } from '@/utils/avatar';

export default {
  async byId(identifier, complete = true) {
    if (!identifier) return;

    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${identifier}/info`
      );

      return formatCompany(data, complete);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async publicationsByCompanyIdentifier(
    companyIdentifier,
    publicationType = null
  ) {
    if (!companyIdentifier) return;

    try {
      let url = `/2.1/companies/${companyIdentifier}/publications`;

      if (publicationType) {
        url = `${url}/${publicationType}`;
      }

      const { data } = await httpClient.get(url);

      return data.publications.map(formatPublication);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async statementIdentifiers(companyIdentifier) {
    if (!companyIdentifier) return;

    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyIdentifier}/statements`
      );

      return {
        id: companyIdentifier,
        total: data.total,
        identifiers: data.identifiers
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async statementById(companyIdentifier, statementIdentifier, isExtended) {
    if (!companyIdentifier || !statementIdentifier) return;

    try {
      const { data } = await httpClient.get(
        encodeURI(
          `/2.1/companies/${companyIdentifier}/statement/${statementIdentifier}`
        )
      );

      return formatStatement(data, isExtended);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async latestStatement(companyIdentifier) {
    if (!companyIdentifier) return;

    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyIdentifier}/statement/latest`
      );

      return {
        overview: extractOverview(data),
        data: formatStatement(data)
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async search(
    { q, page, max, sort, identifiers, active, filters, options },
    complete = false
  ) {
    try {
      const body = {
        q,
        identifiers,
        page,
        max,
        sort,
        active,
        ...options,
        terms_filters: filters
      };

      const { data } = await httpClient.post('/2.1/companies/search', body);

      return {
        total: data.total,
        count: data.companies.length,
        companies: data.companies.map((company) =>
          formatCompany(company, complete)
        ),
        aggregations: data.aggregations
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async aggregations(params = {}) {
    try {
      const { data } = await httpClient.post(
        '/2.1/companies/search/aggregations',
        {
          ...params,
          terms_aggs: {
            activity: 200,
            juridical_form: 200,
            postal_code: 200,
            legal_person_type: 200
          }
        }
      );
      return formatSearchAggs(data.aggregations);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async searchByIdentifers(identifiers, options = {}) {
    try {
      const { includeInactive, ...rest } = options;
      const { data } = await httpClient.post('/2.1/companies/search', {
        identifiers,
        max: identifiers.length,
        include_inactive: includeInactive || false
      });

      return data.companies.map((company) => {
        const preparedCompany = {
          active: company.active,
          id: company.identifier,
          name: company.company_name
        };

        if (rest?.withAvatar) {
          preparedCompany.color = getAvatarColor(company.identifier);
          preparedCompany.initials = getInitials(company.company_name);
        }

        return preparedCompany;
      });
    } catch (error) {
      return errorMessage(error);
    }
  },

  async autocomplete({
    autocompleteInput,
    includeInactive = true,
    includeEstablishments = true,
    includeNaturalPersons = true
  }) {
    try {
      let companyName = null;
      let identifier = null;
      const numeric = autocompleteInput.replace(/\D+/g, '');

      if (numeric.length > 3) {
        identifier = numeric;
      } else {
        companyName = autocompleteInput;
      }

      const body = {
        company_name: companyName,
        identifier,
        include_inactive: includeInactive,
        include_establishments: includeEstablishments,
        include_natural_persons: includeNaturalPersons
      };

      const { data } = await httpClient.post(
        '/2.1/companies/search/autocomplete',
        body
      );

      return {
        total: data.total,
        count: data.companies.length,
        companies: data.companies.map(formatAutocomplete)
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getFunctions(companyId) {
    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyId}/functions`
      );
      return data.list.map(formatFunction);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getRepresentatives(companyId) {
    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyId}/representatives`
      );
      return data.list.map(formatFunction);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getEstablishments(companyId) {
    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyId}/establishments`
      );
      return data.establishments.map(formatEstablishment);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getEvents({ companyId, params }) {
    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyId}/events`,
        { params }
      );
      const { max, events } = formatCompanyEvents(data.events);
      return {
        count: data.count,
        events,
        max
      };
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getPublicationFile(identifier) {
    try {
      const { data } = await httpClient.get(
        `/2.1/publications/${identifier}/file`,
        {
          responseType: 'blob'
        }
      );
      const file = new Blob([data], { type: 'application/pdf' });
      return URL.createObjectURL(file);
    } catch (error) {
      return errorMessage(error);
    }
  },

  async getReport(companyIdentifier) {
    try {
      const { data } = await httpClient.get(
        `/2.1/companies/${companyIdentifier}/report`
      );

      const {
        company,
        publications,
        establishments,
        statements,
        representatives,
        functions
      } = data;

      return {
        company: formatCompany(company, true),
        publications: publications.map(formatPublication),
        establishments: establishments.map(formatFunction),
        statements: {
          overview: extractOverview(last(statements)),
          data: formatStatement(last(statements)),
          list: statements.map(formatStatement)
        },
        representatives: representatives.map(formatFunction),
        functions: functions.map(formatFunction)
      };
    } catch (error) {
      return errorMessage(error);
    }
  }
};
