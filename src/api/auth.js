import { httpClient } from '@/api/axios';
import formatUser from '@/utils/formatters/user';
import errorMessage from '@/utils/api-error';

/**
 * Authentication service for handling user authentication operations
 * @typedef {Object} LoginResponse
 * @property {Object|null} user - The user object if login successful
 * @property {string|null} token - The authentication token if login successful
 * @property {Error|null} error - Error object if login failed
 */
export default {
  /**
   * Authenticate user with email and password
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User's email
   * @param {string} credentials.password - User's password
   * @returns {Promise<LoginResponse>} Login response object
   */
  async login({ email, password }) {
    // Input validation
    if (!email || !password) {
      return {
        user: null,
        token: null,
        error: new Error('Email and password are required')
      };
    }

    if (!email.includes('@')) {
      return {
        user: null,
        token: null,
        error: new Error('Invalid email format')
      };
    }

    try {
      const { data, status } = await httpClient.post('/login', {
        username: email,
        password
      });

      if (status !== 200 || !data.success) {
        throw new Error(data.message || 'Authentication failed');
      }

      // Store refresh token if provided
      if (data.refreshToken) {
        localStorage.setItem('refreshToken', data.refreshToken);
      }

      return {
        user: formatUser(data.user),
        token: data.token,
        error: null
      };
    } catch (error) {
      return {
        user: null,
        token: null,
        error: new Error(
          error.response?.data?.message || 'Login failed. Please try again.'
        )
      };
    }
  },

  /**
   * Log out the current user
   * @returns {Promise<{error: Error|null}>} Logout response
   */
  async logout() {
    try {
      const { status } = await httpClient.post('/logout');

      if (status !== 200) {
        throw new Error('Logout failed');
      }

      // Clear stored tokens
      localStorage.removeItem('refreshToken');

      return { error: null };
    } catch (error) {
      return {
        error: new Error(
          error.response?.data?.message || 'Logout failed. Please try again.'
        )
      };
    }
  },

  /**
   * Get the currently logged in user
   * @returns {Promise<Object>} User object or error
   */
  async loggedUser() {
    try {
      const { data, status } = await httpClient.get('/logged-user');

      if (status !== 200) {
        return false;
      }

      return formatUser(data);
    } catch (error) {
      return false;
    }
  },

  /**
   * Refresh the authentication token
   * @returns {Promise<{token: string|null, error: Error|null}>} New token or error
   */
  async refreshToken() {
    try {
      const refreshToken = localStorage.getItem('refreshToken');

      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const { data, status } = await httpClient.post('/refresh-token', {
        refreshToken
      });

      if (status !== 200 || !data.success) {
        throw new Error('Token refresh failed');
      }

      return {
        token: data.token,
        error: null
      };
    } catch (error) {
      return {
        token: null,
        error: new Error(
          error.response?.data?.message || 'Token refresh failed'
        )
      };
    }
  },

  async update(params) {
    try {
      const { id, ...rest } = params;
      const { data } = await httpClient.put(`/customers/${id}`, rest);
      return { user: formatUser(data.user) };
    } catch (error) {
      return errorMessage(error);
    }
  }
};
