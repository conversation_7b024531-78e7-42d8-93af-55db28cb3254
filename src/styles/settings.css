/* GLOBAL STYLES */

:root {
  --white: #fff;
  --primaryDark: #00557e;
  --primary: #1f72c1;
  --primaryLight: #ebf2f9;
  --primarySmoke: #f3f5f9;
  --primaryTransparent: #1f72c120;
  --primaryGrey: #87a5b3;
  --deepBlack: #1d1d1d;
  --neutralBlack: #35363a;
  --black: #283d50;
  --greyDark: #42555e;
  --grey: #87a5b3;
  --greyLight: #d0d8de;
  --green: #6ab759;
  --yellow: #fecc61;
  --red: #d46160;

  --redTransparent: #d4616020;
  --primarySmokeTransparent: #f3f5f9d0;

  --border: rgba(0, 0, 0, 0.12);

  --whiteRGB: 255, 255, 255;

  --normal: 1rem;
  --large: 1.125rem;
  --small: 0.875rem;
  --xsmall: 0.75rem;
}

::selection {
  color: var(--white);
  background-color: var(--primary);
}

body {
  font-family: 'Open Sans', sans-serif;
  color: var(--black);
  background-color: var(--primarySmoke);
  font-size: 16px; /* 1rem */
}

body.body--dark {
  --white: #1d1d1d;
  --black: #fff;

  --primary: #44a4ff;
  --primarySmoke: #242f3a;
  --primaryLight: #00557e;
  --primaryDark: #f3f5f9;

  --greyDark: #d0d8de;
  --greyLight: #42555e;

  --border: rgba(255, 255, 255, 0.28);
  --whiteRGB: 0, 0, 0;
}

img {
  display: block;
  margin: 0;
}

a {
  color: var(--primary);
}

/* QUASAR */
.q-tabs .q-tab .q-tab__label {
  font-size: (--small);
  font-weight: bold;
}

.q-btn .q-icon {
  font-size: var(--normal);
}

.q-btn-dropdown .q-icon {
  font-size: var(--small);
}

.q-select__dropdown-icon {
  font-size: var(--small);
}

.q-btn.q-btn--outline.text-primary {
  background-color: var(--primaryTransparent) !important;
}

.q-btn.q-btn--outline.text-red {
  background-color: var(--redTransparent) !important;
}

.q-field:not(.transparent) .q-field__inner {
  background-color: var(--white);
}

.q-card {
  border-radius: 0.5rem;
}

.q-table thead {
  background-color: var(--primarySmoke);
}

.q-table thead th {
  text-transform: uppercase;
  color: var(--primaryGrey);
  font-weight: bold;
  white-space: nowrap;
}

.q-table__container .q-table__title {
  font-size: 0.85rem;
  font-weight: bold;
}

.q-page {
  padding-top: 24px !important;
}

@media print {
  .q-notifications {
    visibility: hidden !important;
  }
}

.text-yellow {
  color: var(--yellow) !important;
}

.text-red {
  color: var(--red) !important;
}

.text-green {
  color: var(--green) !important;
}

.font-size-normal {
  font-size: var(--normal);
}

.font-size-small {
  font-size: var(--small);
}

.font-size-xsmall {
  font-size: var(--xsmall);
}

/* ANINATIONS */
.fade-enter-active {
  animation: fade 250ms;
}

.fade-leave-active {
  animation: fade 250ms reverse;
}

@keyframes fade {
  from {
    opacity: 0%;
  }

  to {
    opacity: 100%;
  }
}

.white-space-nowrap {
  white-space: nowrap;
}

/* COLORS and ICONS */
.color-white,
.text-white {
  color: var(--white);
}

.color-black,
.text-black {
  color: var(--black);
}

.color-blue,
.color-primary,
.text-primary,
.text-blue {
  color: var(--primary);
}

.color-yellow,
.color-warning,
.text-yellow,
.text-warning {
  color: var(--yellow);
}

.color-green,
.color-success,
.text-green,
.text-success {
  color: var(--green);
}

.color-red,
.color-danger,
.color-error,
.text-error,
.text-danger {
  color: var(--red);
}

.color-grey,
.text-grey {
  color: var(--grey);
}

.color-grey-light,
.color-greyLight,
.text-grey-light {
  color: var(--greyLight);
}

.color-greyDark,
.color-grey-dark,
.text-grey-dark {
  color: var(--greyDark);
}

.border-rounded {
  border-radius: 0.25rem;
  overflow: hidden;
}

.grow-1 {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}
